import {
  saveRuleInfo,
  delRuleInfo,
  editRuleInfo,
  saveRuleInfoSort,
  queryAllStandardTag,
  queryCustomerCountryList,
  queryRoutingCountList,
} from '@/service/allocation';
export default {
  namespace: 'allocation',
  state: {},
  effects: {
    *saveRuleInfo({ payload, callback }, { call, put }) {
      let response = yield call(saveRuleInfo, payload);
      if (response) {
        callback(response);
      }
    },
    *delRuleInfo({ payload, callback }, { call, put }) {
      let response = yield call(delRuleInfo, payload);
      if (response) {
        callback(response);
      }
    },
    //
    *editRuleInfo({ payload, callback }, { call, put }) {
      let response = yield call(editRuleInfo, payload);
      if (response) {
        callback(response);
      }
    },
    // 保存规则排序
    *saveRuleInfoSort({ payload, callback }, { call, put }) {
      let response = yield call(saveRuleInfoSort, payload);
      if (response) {
        callback(response);
      }
    },
    // 查询客户国家
    *queryCustomerCountryList({ callback }, { call, put }) {
      console.log('queryCustomerCountryList');
      let response = yield call(queryCustomerCountryList);
      if (response) {
        callback(response);
      }
    },
    // 查询客户标签
    *queryAllStandardTag({ callback }, { call, put }) {
      let response = yield call(queryAllStandardTag);
      if (response) {
        callback(response);
      }
    },
    // 智能路由规则 各渠道数量
    *queryRoutingCountList({ payload, callback }, { call, put }) {
      let response = yield call(queryRoutingCountList, payload);
      if (response) {
        callback(response);
      }
    },
  },

  reducers: {
    saveState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};
