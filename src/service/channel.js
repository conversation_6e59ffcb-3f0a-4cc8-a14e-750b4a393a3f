import request from '@/utils/request';

/** 通道列表 */
export async function channelList(payload) {
  // console.log(payload);
  return request('api/crm/channel/channelConfig/channelList', {
    method: 'get',
    data: { payload },
    params: {
      pageSize: payload.pageSize,
      pageNum: payload.pageNum,
      channelTypeId: payload.channelTypeId,
      connectId: payload.connectId,
    },
  });
}

/** 渠道分组列表 */
export async function getChannelGroup() {
  return request('api/crm/channel/channelConfig/queryChannelTree', {
    method: 'get',
  });
}
/** 通道类型列表 */
export async function channelType(payload) {
  return request('api/crm/channel/channelConfig/channelType', {
    method: 'get',
    data: {
      payload: payload,
    },
  });
}

/** 通道类型列表 */
export async function typeCode(payload) {
  return request('api/crm/channel/channelConfig/type', {
    method: 'get',
    params: {
      channelType: payload.channelType,
      subType: payload.subType,
    },
  });
}

/** 创建通道 */
export async function createChannel(payload) {
  return request('api/crm/channel/channelConfig/createChannel', {
    method: 'POST',
    data: payload,
  });
}

/** 更新通道 */
export async function updateChannel(payload) {
  return request('api/crm/channel/channelConfig/updateChannel', {
    method: 'POST',
    data: payload,
    params: {
      id: payload.channelId,
    },
  });
}

/** 删除通道 */
export async function deleteChannel(payload) {
  return request('api/crm/channel/channelConfig/deleteChannel', {
    method: 'post',
    params: {
      id: payload.id,
    },
  });
}

/**
 * 更新渠道状态
 * @param payload
 * @returns {Promise<*>}
 */
export async function updateStatus(payload) {
  return request('api/crm/channel/channelConfig/updateStatus', {
    method: 'post',
    params: {
      id: payload.id,
      status: payload.channelStatus,
    },
  });
}

export async function flowList(payload) {
  return request('api/crm/channel/flow/flowList', {
    method: 'get',
    params: {
      connectId: payload.connectId,
    },
  });
}
export async function channelDetails(payload) {
  return request('api/crm/channel/channelConfig/channelDetails', {
    method: 'get',
    params: {
      id: payload.id,
    },
  });
}
export async function queryChannelById(payload) {
  return request('api/crm/channel/channelConfig/queryChannelById', {
    method: 'get',
    params: {
      channelId: payload.channelId,
    },
  });
}

export async function getChannelByEmailTo(payload) {
  return request('api/crm/channel/channelConfig/getChannelByEmailTo', {
    method: 'get',
    params: {
      emailTo: payload,
    },
  });
}

export async function templateList(payload) {
  return request('api/crm/channel/template/templateList', {
    method: 'get',
    params: {
      connectId: payload.connectId,
    },
  });
}

export async function queryRegionDefList() {
  return request('api/crm/channel/regionDef/queryRegionDefList', {
    method: 'GET',
  });
}

/**
 * 获取web在线聊天渠道详情配置信息
 */
export async function channelDetails1(payload) {
  return request('api/crm/channel/channelConfig/channelDetails', {
    method: 'GET',
    params: { id: payload.id },
  });
}

/**
 * 获取web在线聊天渠道script代码
 */
export async function getScript(payload) {
  return request('api/crm/channel/channelConfig/getScript', {
    method: 'GET',
    params: payload,
  });
}

/**
 * 获取web在线聊天渠道部署script代码
 */
export async function deploy(payload) {
  return request('api/crm/channel/channelConfig/deploy', {
    method: 'GET',
    params: { channelId: payload.channelId },
  });
}

/**
 * 清除当前聊天的token
 */
export async function closeChat(payload) {
  return request('api/crm/channel/media/token', {
    method: 'DELETE',
    data: payload,
  });
}
/**
 * 查询livechat语言包
 */
export async function queryWebLanguage(payload) {
  return request('api/crm/system/international/queryWebLanguage', {
    method: 'GET',
    params: payload,
  });
}
/**
 * 查询各类型渠道信息下拉
 */
export async function queryChannelTypeContact(payload) {
  return request('api/crm/channel/channelConfig/queryChannelTypeContact', {
    method: 'GET',
    params: payload,
  });
}
/**
 * 查询whatsApp电话列表
 */
export async function queryPhoneList(payload) {
  return request('api/crm/channel/channelConfig/queryPhoneList', {
    method: 'GET',
    params: payload,
  });
}
/**
 * whatsApp注册号码
 */
export async function retrievePhone(payload) {
  return request('api/crm/channel/channelConfig/retrievePhone', {
    method: 'GET',
    params: payload,
  });
}

/**
 * 新版查询渠道类型列表
 */
export async function newChannelTypeList() {
  return request('api/crm/channel/channelConfig/channelTypeList', {
    method: 'GET',
  });
}
/**
 * 新版渠道类型详情页查询渠道
 * */
export async function newChannelList(payload) {
  return request('api/crm/channel/channelConfig/newChannelList', {
    method: 'post',
    data: payload,
    params: {
      pageSize: payload.pageSize,
      pageNum: payload.pageNum,
    },
  });
}
/**
 * 查询渠道配置信息
 * */
export async function queryPartConfig(payload) {
  return request(
    'api/crm/channel/channelConfig/queryPartConfig?channelId=' +
      payload.channelId +
      '&&channelTypeCode=' +
      payload.channelTypeCode,
    {
      method: 'GET',
    },
  );
}
/**
 *查询GooglePlay所有应用
 */
export async function getAllApps() {
  return request('api/crm/channel/googlePlay/getAllApps', {
    method: 'GET',
  });
}
/**
 * 获取line webhook url
 */
export async function lineWebhookUrl(payload) {
  return request('api/crm/channel/media/lineWebhookUrl', {
    method: 'POST',
    params: payload,
  });
}
// 获取微信公众号配置
export async function wxOfficeAConfiguration() {
  return request('api/crm/channel/media/wxOfficeAConfiguration', {
    method: 'POST',
  });
}

// 获取特殊渠道配置
export async function querySpecialChannelDefList(payload) {
  return request(
    'api/crm/channel/channelConfig/querySpecialChannelDefList?channelCodes=' +
      payload,
    {
      method: 'GET',
    },
  );
}
//获取当前公司都支持哪些线路供应商
export async function queryVoiceSupplierList() {
  return request('api/crm/channel/supplier/queryVoiceSupplierList', {
    method: 'GET',
  });
}
//获取connect联络线路下拉列表
export async function queryConnectList() {
  return request('api/crm/channel/supplier/queryConnectList', {
    method: 'GET',
  });
}
//获取twilio联络线路下拉列表
export async function queryTwilioList() {
  return request('api/crm/channel/supplier/queryTwilioList', {
    method: 'GET',
  });
}
// 获取层级返回联络线路--新版联络线路层级希下拉列表
export async function queryContactLineList() {
  return request('api/crm/channel/supplier/queryContactLineList', {
    method: 'GET',
  });
}
