import request from '@/utils/request';

/**
 * 智能规则保存
 * @param {} payload
 * @returns
 */
export async function saveRuleInfo(payload) {
  return request('api/crm/call/routingChannel/saveRuleInfo', {
    method: 'POST',
    data: payload,
  });
}

/**
 * 删除智能规则
 * @param {} payload
 * @returns
 */
export async function delRuleInfo(payload) {
  return request('api/crm/call/routingChannel/delRuleInfo', {
    method: 'POST',
    data: payload,
  });
}

/**
 * 修改智能规则
 * @param {} payload
 * @returns
 */
export async function editRuleInfo(payload) {
  return request('api/crm/call/routingChannel/queryRuleDetails', {
    method: 'GET',
    params: {
      routingId: payload.routingId,
    },
  });
}

/**
 * 保存规则排序
 * @param {} payload
 * @returns
 */
export async function saveRuleInfoSort(payload) {
  return request('api/crm/call/routingChannel/updateOrder', {
    method: 'POST',
    data: payload,
  });
}

/**
 * 查询客户标签
 * @param {} payload
 * @returns
 */
export async function queryAllStandardTag() {
  return request('api/crm/customer/tag/queryAllStandardTag', {
    method: 'GET',
  });
}

// 查询客户国家
export async function queryCustomerCountryList() {
  return request('api/crm/customer/customerinfo/queryCustomerCountryList', {
    method: 'GET',
  });
}

// 智能路由规则 各渠道数量
export async function queryRoutingCountList(payload) {
  return request('api/crm/call/routingChannel/countList', {
    method: 'GET',
    params: payload,
  });
}
