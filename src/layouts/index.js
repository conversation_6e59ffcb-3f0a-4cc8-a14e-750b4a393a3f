import React, { Component, useState } from 'react';
import {
  <PERSON>readcrumb,
  Layout,
  Menu,
  Select,
  Dropdown,
  Tabs,
  Button,
  Icon,
  notification,
  Checkbox,
  Alert,
} from 'antd';
import {
  ScheduleOutlined,
  ProfileOutlined,
  PrinterOutlined,
  SolutionOutlined,
  TeamOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  PaperClipOutlined,
} from '@ant-design/icons';
import { AgentIconAvailable, AgentIconOffline } from '@/pages/agentImChat/icon';
import Draggable from 'react-draggable';
import HOCAuth from '@/components/HOCAuth/index';
import { ReactComponent as Beta } from '../assets/beta.svg';
import { ReactComponent as Activity } from '../assets/Activity.svg';
import { ReactComponent as Chart } from '../assets/Chart.svg';
import { ReactComponent as Discount } from '../assets/Discount.svg';
import { ReactComponent as Graph } from '../assets/Graph.svg';
import { ReactComponent as HomeIndex } from '../assets/HomeIndex.svg';
import { ReactComponent as InfoCircle } from '../assets/InfoCircleCopy.svg';
import { ReactComponent as Message } from '../assets/Message.svg';
import { ReactComponent as Profile } from '../assets/Profile.svg';
import { ReactComponent as MoreSquare } from '../assets/MoreSquare.svg';
import { ReactComponent as AIGCIcon } from '../assets/AIGC.svg';
import { ReactComponent as KnowledgeBase } from '../assets/knowledge-base.svg';
import { ReactComponent as MarketingActivities } from '../assets/marketing-activities-menu-icon.svg';
import { ReactComponent as Statistics } from '../assets/new-statistics-icon.svg';
import { ReactComponent as StatisticsIcon } from '../assets/Statistics.svg';
import { ReactComponent as CustomerVoicelan } from '../assets/CustomerVoicelan.svg';
import { ReactComponent as CustomerVoicebai } from '../assets/CustomerVoicebai.svg';
import { ReactComponent as EvaluationIcon } from '../assets/EvaluationIcon.svg';
import { ReactComponent as DataInsights } from '../assets/dataInsights.svg';
import { ReactComponent as ChannelIcon } from '../assets/channelIcon.svg';
import { ReactComponent as AllocationRule } from '../assets/allocationRule.svg';
import { ReactComponent as IntelligentAgentIcon } from '../assets/intelligent-agent-menu.svg';
import { ReactComponent as SmartQualityInspectionIcon } from '../assets/smartQualityInspection.svg';
import { ReactComponent as MenuTagIcon } from '../assets/menu-tag.svg';
import { ReactComponent as SettingIcon } from '../assets/setting-menu-icon.svg';
import AIGCGif from '../assets/ai.gif';
import BetaPng from '../assets/betaPng.png';

import AIGC from '../components/AIGC/index';
import Call2 from '../assets/Call2.svg';
import SetMenu from '../assets/setMenu.svg';

// import Call1 from '../assets/call-new.svg';
import Call1 from '../assets/new-downLine.png';
import CallLine from '../assets/callLine.svg';
import Telegram from '../assets/telegram-new.svg';
import NotificationBanner from '../assets/NotificationBanner.svg';
import OffLine from '../assets/offline.png';
import Frame from '../assets/Frame.svg';
import styles from './index.less';
import styleCss from './index.css';
import LogoImg from '../assets/logo-white.png';
import LogoutIcon from '../assets/export-outlined-icon.png';
import bannerTop from '../assets/bannerTop.png';
import SettingOutlined from '../assets/setting-outlined-icon.png';
import SettingBanner from '../assets/SettingBanner.svg';
import ErrorIcon from '../assets/synchronized-fail.png';
import CloseIcon from '../assets/close-icon-error.png';
// import CnIcon from '../assets/cn-icon.png';
// import EnIcon from '../assets/en-icon.png';
import CnIcon from '../assets/CnIcon.svg';
import EnIcon from '../assets/EnIcon.svg';
// import CcpPanelHome from '../pages/worktable/ccpHome';
import CustomerInformation from '../pages/customerInformation';
import WorktableContentOld from '../pages/worktable';
// 引入新的工作台
import WorktableContentNew from '../pages/workTableUpgrade';

import verticalBeginnerGuide from '../assets/beginner-guide-1.png';
import TransverseVerticalBeginnerGuideIcon from '../assets/beginner-guide-2.png';
import UserBanner from '../assets/user-name.png';
import HelpIcon from '../assets/header-help-icon.png';
import WaringIcon from '../assets/delete-tips-icon.png';
import BeginnerGuidanceArrow from '../assets/beginner-guidance-arrow.gif';
import useAuthAccess from '@/utils/authAccess';
import { IMWebClient } from '../pages/agentImChat/sdk/im.web.sdk';
import LanguageEnUS from '@/locales/language-en-US.json';
import LanguageZhCN from '@/locales/language-zh-CN.json';
import LanguageDeDE from '@/locales/language-de-DE.json';
import LanguageJa from '@/locales/language-ja.json';
import LanguageIdID from '@/locales/language-id-ID.json';
import {
  connect,
  Link,
  history,
  setLocale,
  getIntl,
  FormattedMessage,
  getLocale,
} from 'umi';
import { Column } from '@ant-design/charts';

const { Header, Content, Sider } = Layout;
const { SubMenu } = Menu;
const MemoizedWorktableContentNew = React.memo(WorktableContentNew);
const MemoizedWorktableContentOld = React.memo(WorktableContentOld);

const Icons = {
  Activity: <Activity />,
  Chart: <Chart />,
  Discount: <Discount />,
  Graph: <Graph />,
  HomeIndex: <HomeIndex />,
  InfoCircle: <InfoCircle />,
  Message: <Message />,
  Profile: <Profile />,
  MoreSquare: <MoreSquare />,
  AIGCIcon: <AIGCIcon />,
  KnowledgeBase: <KnowledgeBase />,
  MarketingActivities: <MarketingActivities />,
  Statistics: <Statistics />,
  statisticsIcon: <StatisticsIcon width="16" height="16" />,
  CustomerVoice: <CustomerVoicelan width="16" height="16" />,
  evaluation: <DataInsights width="16" height="16" />,
  dataInsights: <EvaluationIcon width="16" height="16" />,
  channelIcon: <ChannelIcon width="16" height="16" />,
  allocationRule: <AllocationRule width="16" height="16" />,
  IntelligentAgentIcon: <IntelligentAgentIcon width="16" height="16" />,
  smartQualityInspectionIcon: (
    <SmartQualityInspectionIcon width="16" height="16" />
  ),
  MenuTagIcon: <MenuTagIcon width="16" height="16" />,
  SettingIcon: <SettingIcon width="16" height="16" />,
};
const IconsShrink = {
  Activity: <Activity style={{ width: '100%', height: '100%' }} />,
  Chart: <Chart style={{ width: '100%', height: '100%' }} />,
  Discount: <Discount style={{ width: '100%', height: '100%' }} />,
  Graph: <Graph style={{ width: '100%', height: '100%' }} />,
  HomeIndex: <HomeIndex style={{ width: '100%', height: '100%' }} />,
  InfoCircle: <InfoCircle style={{ width: '100%', height: '100%' }} />,
  Message: <Message style={{ width: '100%', height: '100%' }} />,
  Profile: <Profile style={{ width: '100%', height: '100%' }} />,
  MoreSquare: <MoreSquare style={{ width: '100%', height: '100%' }} />,
  KnowledgeBase: <KnowledgeBase style={{ width: '100%', height: '100%' }} />,
  statisticsIcon: <StatisticsIcon style={{ width: '100%', height: '100%' }} />,
  MarketingActivities: (
    <MarketingActivities style={{ width: '100%', height: '100%' }} />
  ),
  CustomerVoice: <CustomerVoicelan style={{ width: '100%', height: '100%' }} />,
  Statistics: <Statistics style={{ width: '100%', height: '100%' }} />,
  evaluation: <DataInsights style={{ width: '100%', height: '100%' }} />,
  dataInsights: <EvaluationIcon style={{ width: '100%', height: '100%' }} />,
  channelIcon: <ChannelIcon style={{ width: '100%', height: '100%' }} />,
  allocationRule: <AllocationRule style={{ width: '100%', height: '100%' }} />,
  IntelligentAgentIcon: (
    <IntelligentAgentIcon style={{ width: '100%', height: '100%' }} />
  ),
  smartQualityInspectionIcon: (
    <SmartQualityInspectionIcon style={{ width: '100%', height: '100%' }} />
  ),
  MenuTagIcon: <MenuTagIcon style={{ width: '100%', height: '100%' }} />,
  SettingIcon: <SettingIcon style={{ width: '100%', height: '100%' }} />,
};

class LayoutContent extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showWorkTable: false,
      language: true,
      openKeys: [],
      collapsed: false,
      path: [true ? '首页' : 'Home', ''],
      url: '/',
      ccpKey: 0,
      selectKeys: ['800000'],
      positionX: 50,
      positionY: 50,
      parentStateValue: false,
      selectID: '800000',
      isDragging: false,
      showErrorTips: true,
      roleId: '',
      beginnerGuideNum: 0,
      promptCheck: false,
      agentStatusCurrent: '',
      alertList: [],
      agentStatusList: [],
      showReconnectAlert: false,
      allowReconnect: false,
    };

    this.items = [
      {
        key: '1',
        label: (
          <a target="_blank" rel="noopener noreferrer" onClick={this.ccpOnline}>
            <FormattedMessage id="home.login" />
          </a>
        ),
      },
      {
        key: '2',
        label: (
          <a
            target="_blank"
            rel="noopener noreferrer"
            // onClick={() => handleClick('2')}
          >
            <FormattedMessage id="home.logout" />
          </a>
        ),
      },
    ];

    this.itemsPerson = [
      {
        key: '1',
        label: (
          <a
            target="_blank"
            rel="noopener noreferrer"
            onClick={this.routerPersonal}
          >
            <FormattedMessage id="home.personal.center" />
          </a>
        ),
      },
      {
        key: '2',
        label: (
          <a
            target="_blank"
            rel="noopener noreferrer"
            onClick={this.routerPreferences}
          >
            <FormattedMessage id="home.preferences" />
          </a>
        ),
      },
    ];
  }

  AIGCref = React.createRef();

  componentDidMount() {
    this.queryLanguage();
    this.queryAgentStatus();
    this.getMenuList();
    this.getUser();
    this.getUserRoleId();
    this.queryRemindFlag();
    this.queryUserInfoDetail();
    //管理员需要进行计量计费提示
    this.queryPageAlarmInfo();
    // setLocale('en-US', true);
    this.getAllConnectList();
    this.deptList();
    this.channelTypeList();

    let lang = localStorage.getItem('lang');
    // if (['1003', '1005'].includes(this.props.user.roleList?.[0]?.roleId)) {
    //   this.props.dispatch({
    //     type: 'layouts/workerTablePush',
    //     payload: true,
    //   });
    // }
    if (history.location.pathname != '/documentKnowledgeBase') {
      this.getUserRules();
    }

    console.log('history.location.pathname', history.location.pathname);

    //记录路由数据,目前是失效的
    //1.livechat影响该功能
    //2.项目没有用户权限，营销，客服页面不同步，记忆页面问题会导致路由不在菜单却展示在了页面上
    this.setState(
      {
        path: sessionStorage.getItem('currentMenu')
          ? sessionStorage.getItem('currentMenu').split(',')
          : lang == 'en-US'
          ? [false ? '首页' : 'Home', '']
          : [true ? '首页' : 'Home', ''],
        url: sessionStorage.getItem('currentUrl')
          ? sessionStorage.getItem('currentUrl')
          : '/',
        selectKeys: sessionStorage.getItem('currentSelectKeys')
          ? sessionStorage.getItem('currentSelectKeys').split()
          : ['800000'],
        selectID: sessionStorage.getItem('currentSelectID')
          ? sessionStorage.getItem('currentSelectID')
          : '800000',
        openKeys: sessionStorage.getItem('currentOpenKeys')
          ? sessionStorage.getItem('currentOpenKeys').split()
          : [],
      },
      // ,
      //   () => {
      //     console.log(
      //       this.state.path,
      //       this.state.url,
      //       this.state.selectKeys,
      //       this.state.selectID,
      //       this.state.openKeys,
      //     );
      // },
    );
    //监听企业微信渠道回调地址路径，引入到正确路径（解决#/后的路由无法识别问题）
    let params = window.location.search?.split('?')?.[1]?.split('&'); // 获取参数列表并分割成数组
    let authCode = params
      ?.find(param => param.includes('auth_code'))
      ?.split('=')[1]; // 获取auth_code的值

    let state = params?.find(param => param.includes('state'))?.split('=')[1]; // 获取state的值
    console.log(window.location, authCode, state, 'wechat====');
    if (state === '/channelAllocation/wechatConfiguration' && authCode) {
      window.location.replace(
        `https://${process.env.WXCHAT_DOMAIN}/#/channelAllocation/wechatConfiguration?auth_code=${authCode}`,
      );
    }
    //监听ins喝tiktok渠道回调地址路径，引入到正确路径（解决#/后的路由无法识别问题）
    let codeName = params?.find(param => param.includes('code'))?.split('=')[0];
    let code = params?.find(param => param.includes('code'))?.split('=')[1]; // 获取code的值(ins)
    let shop_region = params
      ?.find(param => param.includes('shop_region'))
      ?.split('=')[1]; // 获取shop_region的值(tiktok)
    let app_key = params
      ?.find(param => param.includes('app_key'))
      ?.split('=')[1]; // 获取app_key的值(tiktok)
    let locale = params?.find(param => param.includes('locale'))?.split('=')[1]; // 获取locale的值(tiktok)
    let tiktokState = params
      ?.find(param => param.includes('state'))
      ?.split('=')[1]; // 获取state的值(tiktok)
    console.log(window.location, code, shop_region, app_key, 'tiktok,ins====');
    if (
      code &&
      codeName === 'code' &&
      !shop_region &&
      !app_key &&
      !locale &&
      !tiktokState
    ) {
      window.location.replace(
        `https://${process.env.DOMAIN_NAME_OVER}/#/channelAllocation/instagramConfiguration?code=${code}`,
      );
    } else if (
      (code && codeName === 'code' && shop_region && app_key && locale) ||
      (tiktokState && code)
    ) {
      window.location.replace(
        `https://${process.env.DOMAIN_NAME_OVER}/#/channelAllocation/tiktokConfiguration?code=${code}&shop_region=${shop_region}&app_key=${app_key}&&locale=${locale}&&state=${tiktokState}`,
      );
    }

    //监听amazon渠道回调地址路径，引入到正确路径（解决#/后的路由无法识别问题）
    let spapi_oauth_code = params
      ?.find(param => param.includes('spapi_oauth_code'))
      ?.split('=')[1];
    let amazonState = params
      ?.find(param => param.includes('state'))
      ?.split('=')[1]; // 获取state的值
    let selling_partner_id = params
      ?.find(param => param.includes('selling_partner_id'))
      ?.split('=')[1];
    console.log(spapi_oauth_code, amazonState, 'amazon====');
    if (spapi_oauth_code && amazonState) {
      window.location.replace(
        `https://${process.env.DOMAIN_NAME_OVER}/#/channelAllocation/amazonRegionConfiguration?state=${amazonState}&code=${spapi_oauth_code}&selling_partner_id=${selling_partner_id}`,
      );
    }
  }

  componentDidUpdate(prevProps, prevState, snapshot) {
    if (this.props.showWorkTable !== prevProps.showWorkTable) {
      this.setState(
        {
          showWorkTable: this.props.showWorkTable,
        },
        () => {
          if (this.state.showWorkTable == true) {
            this.setState({
              path: [
                getIntl().formatMessage({
                  id: 'layout.worktable',
                }),
                '',
              ],
              url: '/worktable',
              selectKeys: ['800000'],
              selectID: '800000',
              openKeys: [],
            });
          }
        },
      );
    }
    //这段逻辑只用于初始化时，跳转工作台，其他时间都置为false
    if (
      JSON.parse(sessionStorage.getItem('showWorkTable')) === true &&
      JSON.parse(sessionStorage.getItem('showWorkTable')) !==
        this.props.showWorkTable
    ) {
      this.props.dispatch({
        type: 'layouts/workerTablePush',
        payload: true,
      });
      this.setState(
        {
          showWorkTable: true,
        },
        () => {
          if (this.state.showWorkTable == true) {
            this.setState({
              path: [
                getIntl().formatMessage({
                  id: 'layout.worktable',
                }),
                '',
              ],
              url: '/worktable',
              selectKeys: ['800000'],
              selectID: '800000',
              openKeys: [],
            });
          }
        },
      );
    }

    //菜单收缩状态存储到storage
    if (prevState.collapsed !== this.state.collapsed) {
      sessionStorage.setItem('menu_collapsed_state', this.state.collapsed);
    }
    //websocketStatus和agentStatus同时为true才上线，否则提示ws正在重新连接，强制下线
    if (
      (this.props.websocketStatus !== prevProps.websocketStatus ||
        this.props.agentStatus !== prevProps.agentStatus) &&
      ['1003', '1005'].includes(this.props.user.roleList?.[0]?.roleId)
    ) {
      if (
        (this.props.websocketStatus === true &&
          this.props.agentStatus?.split('_')?.[0] === '1' &&
          this.props.user.roleList?.[0]?.roleId === '1003') ||
        (this.props.user.roleList?.[0]?.roleId === '1005' &&
          this.props.websocketStatus === true)
      ) {
        //下面两个状态永远绑定
        this.setState({
          agentStatusCurrent: this.props.agentStatus,
        });
        this.props.dispatch({
          type: 'global/changeConnectState',
          payload: 'topLine',
        });
        this.setState({ showReconnectAlert: false });
        localStorage.setItem('allowReconnect', JSON.stringify(false));
      } else if (this.props.agentStatus?.split('_')?.[0] === '0') {
        //下面两个状态永远绑定
        this.setState({
          agentStatusCurrent: this.props.agentStatus,
        });
        this.props.dispatch({
          type: 'global/changeConnectState',
          payload: 'downLine',
        });
      } else if (this.props.agentStatus?.split('_')?.[0] === '1') {
        //下面两个状态永远绑定
        this.setState({
          agentStatusCurrent: this.props.agentStatus,
        });
        this.props.dispatch({
          type: 'global/changeConnectState',
          payload: 'topLine',
        });
      }
      if (this.props.websocketStatus === false) {
        this.setState({ showReconnectAlert: true });
        // this.setState({
        //   allowReconnect: true,
        // });
        // notification.warning({
        //   message: getIntl().formatMessage({
        //     id: 'im.chat.translation.retry.re',
        //   }),
        // });
      }
      //  else if (
      //   this.props.websocketStatus === false &&
      //   JSON.parse(localStorage.getItem('allowReconnect')) === true
      // ) {
      //   this.setState({ showReconnectAlert: true, allowReconnect: true });
      // }
    }
    // if (
    //   this.props.websocketStatus === false &&
    //   JSON.parse(localStorage.getItem('allowReconnect')) === true &&
    //   this.state.allowReconnect !==
    //     JSON.parse(localStorage.getItem('allowReconnect'))
    // ) {
    //   this.setState({ showReconnectAlert: true, allowReconnect: true });
    // }
  }

  // 查询坐席状态列表
  queryAgentStatus = () => {
    this.props.dispatch({
      type: 'manageAgentStatus/queryLayoutAgentStatus',
      callback: response => {
        let { code, data, msg } = response;
        if (code === 200) {
          let statusList = data;
          this.setState({
            agentStatusList: statusList,
          });
          this.props.dispatch({
            type: 'layouts/setAgentStatusList',
            payload: statusList,
          });
          //如果已达上线人数上限，则不允许上线
          if (
            !this.props.allowOnlineCountFlag &&
            localStorage.getItem('agentStatus')?.split('_')?.[0] === '1'
          ) {
            notification.error({
              message: getIntl().formatMessage({
                id: 'workerOffers.agent.status.limit',
              }),
            });
            return;
          }
          //缓存中没有agentStatus，则默认下线
          if (
            localStorage.getItem('agentStatus') &&
            statusList.find(
              item =>
                item.type + '_' + item.agentStatusId ===
                localStorage.getItem('agentStatus'),
            )
          ) {
            this.setState({
              agentStatusCurrent: localStorage.getItem('agentStatus'),
            });
            this.props.dispatch({
              type: 'layouts/setAgentStatus',
              payload: localStorage.getItem('agentStatus'),
            });
            this.props.dispatch({
              type: 'layouts/setAgentStatusName',
              payload: localStorage.getItem('agentStatusName'),
            });
          } else {
            let index = statusList.find(
              item => item.type === '0' && item.initStatus === '1',
            );
            this.setState({
              agentStatusCurrent: index.type + '_' + index.agentStatusId,
            });
            this.props.dispatch({
              type: 'layouts/setAgentStatus',
              payload: index.type + '_' + index.agentStatusId,
            });
            this.props.dispatch({
              type: 'layouts/setAgentStatusName',
              payload: index.agentStatusName,
            });
          }
        } else {
          notification.error({
            message: msg,
          });
        }
      },
    });
  };
  /**
   * 查询是否需要提示,是的话提示什么样式
   */
  queryPageAlarmInfo() {
    this.props.dispatch({
      type: 'meteringBilling/queryPageAlarmInfo',
      callback: response => {
        if (response.code === 200) {
          this.setState({
            alertList: response.data,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  }
  /**
   * 获取当前登录用户所在公司的connect-list
   */
  async getAllConnectList() {
    await this.props.dispatch({
      type: 'layouts/getAllConnectList',
      payload: null,
    });
    await this.getSelectedChannel();
    if (
      this.isEmptyObject(this.props.selectedConnect) ||
      !this.props.selectedConnect
    ) {
      // setTimeout(() => {
      //刚进来默认选择第一个实例
      // 暂存到redux中
      await this.props.dispatch({
        type: 'layouts/updateConnect',
        payload: this.props.connectList?.[0]?.value
          ? JSON.parse(this.props.connectList?.[0]?.value)
          : '',
      });

      // 存到Redis中
      await this.props.dispatch({
        type: 'layouts/saveSelectedConnect',
        payload: this.props.connectList?.[0]?.value
          ? JSON.parse(this.props.connectList?.[0]?.value)
          : '',
      });
      // }, 1000);
      await this.getSelectedChannel();
    }
  }
  isEmptyObject(obj) {
    return obj && Object.keys(obj).length === 0 && obj.constructor === Object;
  }
  queryRemindFlag = () => {
    this.props.dispatch({
      type: 'layouts/queryRemindFlag',
      callback: response => {
        if (response.code == 200) {
          let remindFlag = response.data;
          // remindFlag 0-不提醒 1-提醒
          this.setState({
            remindFlag: response.data,
          });

          if (remindFlag == 1) {
            this.setState({
              promptCheck: false,
            });
          } else {
            this.setState({
              promptCheck: true,
            });
          }
          let clickBeginnerGuide = localStorage.getItem('clickBeginnerGuide');
          if (clickBeginnerGuide !== '1') {
            this.getUser1();
          }
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 第几次登录及是否展示新手提示
  getUser1 = () => {
    let { remindFlag } = this.state;
    this.props.dispatch({
      type: 'layouts/getUser1',
      callback: response => {
        if (response.code == 200) {
          let roleList = response.data.roleList;
          let roleId = roleList[0].roleId;

          let loginNum = response.data.loginNum;
          // remindFlag 0-不提醒 1-提醒
          // let remindFlag = response.data.remindFlag;
          if (remindFlag == 1) {
            if (loginNum <= 3) {
              this.setState({
                beginnerGuideNum: 1,
              });
            }
          } else {
            this.setState({
              promptCheck: true,
            });
          }

          this.setState({
            roleId: roleId,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 获取角色ID
  getUserRoleId = () => {
    this.props.dispatch({
      type: 'layouts/getUser1',
      callback: response => {
        if (response.code == 200) {
          let roleList = response.data.roleList;
          let roleId = roleList[0].roleId;

          this.setState({
            roleId: roleId,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  /**
   * 查询当前语言
   */
  queryLanguage() {
    this.props.dispatch({
      type: 'personalCenter/queryUserPreference',
      callback: response => {
        let { code, data, msg } = response;
        if (code === 200) {
          // 偏好设置中存在则取偏好设置的值
          if (data) {
            let info = {};
            data?.forEach(item => {
              info[item.code] = item.value;
            });

            // 切换国际化
            setLocale(info.language);
            localStorage.setItem('lang', info.language);
            if (info.language == 'zh-CN') {
              localStorage.setItem(
                'languageLocal',
                JSON.stringify(LanguageZhCN),
              );
            } else if (info.language == 'en-US') {
              localStorage.setItem(
                'languageLocal',
                JSON.stringify(LanguageEnUS),
              );
            } else if (info.language == 'de-DE') {
              localStorage.setItem(
                'languageLocal',
                JSON.stringify(LanguageDeDE),
              );
            } else if (info.language == 'ja') {
              localStorage.setItem('languageLocal', JSON.stringify(LanguageJa));
            } else if (info.language == 'id-ID') {
              localStorage.setItem(
                'languageLocal',
                JSON.stringify(LanguageIdID),
              );
            }
          } else {
            // 偏好设置中不存在则取浏览器的值
            const utcTimezoneOffset = new Date().getTimezoneOffset();
            const utcTimezone = `UTC${
              utcTimezoneOffset >= 0 ? '-' : '+'
            }${Math.abs(utcTimezoneOffset / 60)}`;
            let language = navigator.language || navigator.userLanguage;
            let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            let data = [
              {
                code: 'timezone',
                value: utcTimezone,
              },
              {
                code: 'language',
                value: language,
              },
              {
                code: 'timezoneId',
                value: timezone,
              },
            ];

            this.props.dispatch({
              type: 'personalCenter/updateUserPreference',
              payload: data,
              callback: response => {
                let { code, msg } = response;
                if (200 === code) {
                  if (language == 'zh-CN') {
                    localStorage.setItem(
                      'languageLocal',
                      JSON.stringify(LanguageZhCN),
                    );
                  } else if (language == 'de-DE') {
                    localStorage.setItem(
                      'languageLocal',
                      JSON.stringify(LanguageDeDE),
                    );
                  } else if (language == 'ja') {
                    localStorage.setItem(
                      'languageLocal',
                      JSON.stringify(LanguageJa),
                    );
                  } else if (language == 'id-ID') {
                    localStorage.setItem(
                      'languageLocal',
                      JSON.stringify(LanguageIdID),
                    );
                  } else {
                    localStorage.setItem(
                      'languageLocal',
                      JSON.stringify(LanguageEnUS),
                    );
                  }
                  notification.success({
                    message: getIntl().formatMessage({
                      id: 'user.management.operation.success',
                    }),
                  });
                } else {
                  notification.error({
                    message: msg,
                  });
                }
              },
            });
          }
        } else {
          notification.error({
            message: msg,
          });
        }
      },
    });
  }

  /**
   * 获取左侧树目录
   */
  getMenuList() {
    this.props.dispatch({
      type: 'layouts/menuList',
      payload: null,
    });
    this.props.dispatch({
      type: 'layouts/getMenuStatus',
      callback: response => {
        let { code, data, msg } = response;
        if (code === 200) {
          this.setState(
            prevState => ({
              collapsed: data == '1' ? false : true,
            }),
            () => {
              sessionStorage.setItem(
                'menu_collapsed_state',
                this.state.collapsed,
              );
            },
          );
        } else {
          notification.error({
            message: msg,
          });
        }
      },
    });
  }

  /**
   * 获取当前登录用户信息
   */
  getUser() {
    this.props.dispatch({
      type: 'layouts/getUser',
      payload: null,
    });
  }
  // 调用用户权限规则
  // 调用user获取全局权限设置规则
  getUserRules() {
    this.props.dispatch({
      type: 'layouts/getUserRules',
      payload: null,
      callback: response => {
        let { code, data, msg } = response;
        if (code === 200) {
        } else {
          notification.error({
            message: msg,
          });
        }
      },
    });
  }

  /**
   * 查询当前登录用户所在公司的部门信息
   */
  deptList() {
    this.props.dispatch({
      type: 'layouts/deptList',
      payload: null,
    });
  }

  /**
   * 获取选中的connect
   */
  async getSelectedChannel() {
    await this.props.dispatch({
      type: 'layouts/getSelectedConnect',
    });
  }

  /**
   * 控制菜单栏缩放展开
   */
  toggleCollapsed = () => {
    this.setState(
      prevState => ({
        collapsed: !prevState.collapsed,
      }),
      () => {
        this.props.dispatch({
          type: 'layouts/setMenuStatus',
          payload: this.state.collapsed == false ? 1 : 0,
          callback: response => {
            let { code, data, msg } = response;
            if (code === 200) {
            } else {
              notification.error({
                message: msg,
              });
            }
          },
        });
      },
    );
  };
  /**
   * 退出
   */
  logout = () => {
    localStorage.removeItem('clickBeginnerGuide');
    this.props.dispatch({
      type: 'layouts/logout',
      payload: null,
      callback: () => {
        // 情况本地的localStorage
        sessionStorage.clear();
        // localStorage.clear();
        localStorage.removeItem('Authorization');
        // sessionStorage.removeItem('currentMenu');
        // sessionStorage.removeItem('currentUrl');
        // sessionStorage.removeItem('currentSelectKeys');
        // sessionStorage.removeItem('currentSelectID');
        // sessionStorage.removeItem('currentOpenKeys');
        localStorage.removeItem('agentStatus');
        // 跳转到登录页
        history.replace('/login');
        IMWebClient.stop();
      },
    });
  };
  /**
   * 跳转个人中心
   */
  routerPersonal = () => {
    history.push('/personalCenter');
    sessionStorage.setItem('showWorkTable', false);
    this.props.dispatch({
      type: 'layouts/workerTablePush',
      payload: false,
    });
    this.setState({
      path: [
        getIntl().formatMessage({
          id: 'home.personal.center',
        }),
        '',
      ],
      url: '/personalCenter',
    });
  };
  /**
   * 偏好设置
   */
  routerPreferences = () => {
    history.push('/preferences');
    sessionStorage.setItem('showWorkTable', false);
    this.props.dispatch({
      type: 'layouts/workerTablePush',
      payload: false,
    });
    this.setState({
      path: [
        getIntl().formatMessage({
          id: 'home.preferences',
        }),
      ],
      url: '/preferences',
    });
    // 缓存偏好设置面包屑
    sessionStorage.setItem(
      'currentMenu',
      `${getIntl().formatMessage({
        id: 'home.preferences',
      })}`,
    );
    sessionStorage.setItem('currentUrl', '/preferences');
  };
  /**
   * 个人信息头像回显
   */
  queryUserInfoDetail = () => {
    this.props.dispatch({
      type: 'personalCenter/queryUserInfoDetail',
      callback: response => {
        let { code, data, msg } = response;
        if (code === 200) {
          if (data.profilePhoto) {
            this.props.dispatch({
              type: 'layouts/setUserAvatar',
              payload: data.profilePhoto,
            });
          }
        } else {
          notification.error({
            message: msg,
          });
        }
      },
    });
  };

  /**
   * 查询渠道列表
   */
  channelTypeList = () => {
    this.props.dispatch({
      type: 'layouts/getChannelType',
      payload: null,
    });
  };
  /**
   * 打开弹窗AIGC
   */
  openAIGC = event => {
    if (this.state.isDragging) {
      this.setState({
        isDragging: false,
      });
      return;
    }
    this.props.dispatch({
      type: 'aigc/setOpen',
      payload: true,
    });
    this.handleParentStateChange(true);
  };
  // draggableStart = () => {
  //   console.log('draggableStart');
  //   this.setState({
  //     isDragging: true,
  //   });
  // };
  // draggableEnd = () => {
  //   console.log('draggableEnd');
  //   this.setState({
  //     isDragging: false,
  //   });
  // };

  handleParentStateChange = value => {
    // 在父组件中更新父组件的state值
    this.setState({ parentStateValue: value });
  };
  /**
   * connect onchange事件
   */
  onChangeConnect = value => {
    // 获取被选中的connect
    let connect = JSON.parse(value);

    if (connect.identityManagementType === 'SAML') {
      this.props.dispatch({
        type: 'global/changeConnectState',
        payload: 'topLine',
      });
    } else {
      this.props.dispatch({
        type: 'global/changeConnectState',
        payload: 'downLine',
      });
    }
    // 暂存到redux中
    this.props.dispatch({
      type: 'layouts/updateConnect',
      payload: connect,
    });

    // 存到Redis中
    this.props.dispatch({
      type: 'layouts/saveSelectedConnect',
      payload: connect,
    });
  };
  /**
   * 跳转到工作台
   */
  workerPush = () => {
    // 暂存到redux中
    // sessionStorage.setItem('showWorkTable', true);
    this.props.dispatch({
      type: 'layouts/workerTablePush',
      payload: true,
    });
    // history.push('/worktable');
    this.setState({
      path: [
        getIntl().formatMessage({
          id: 'layout.worktable',
        }),
        '',
      ],
      url: '/worktable',
    });
  };
  /**
   *  上线
   */
  ccpOnline = () => {
    // history.push('/worktable');
    // sessionStorage.setItem('showWorkTable', true);
    this.props.dispatch({
      type: 'layouts/workerTablePush',
      payload: true,
    });
    this.setState({
      path: [
        getIntl().formatMessage({
          id: 'layout.worktable',
        }),
        '',
      ],
      url: '/worktable',
    });
    // console.log('this.state.ccpKey:' + this.state.ccpKey);
    // if (this.state.ccpKey === 0) {
    //   this.setState({
    //     ccpKey: this.state.ccpKey + 1,
    //   });
    // }
  };
  /**
   * 切换国际化
   */
  changeLanguage = () => {
    let language = getLocale();
    if (language == 'en-US') {
      setLocale('zh-CN', true);
    } else {
      setLocale('en-US', true);
    }
  };
  // 接听来电点击事件
  // changeConnectStatus = payload => {
  //   this.props.dispatch({
  //     type: 'global/changeConnectState',
  //     payload: payload,
  //   });
  //   // history.push('/worktable');
  //   this.props.dispatch({
  //     type: 'layouts/workerTablePush',
  //     payload: true,
  //   });
  //   this.setState({
  //     path: [
  //       getIntl().formatMessage({
  //         id: 'layout.worktable',
  //       }),
  //       '',
  //     ],
  //     url: '/worktable',
  //   });
  // };
  /**
   *
   * @param {*} param0
   */
  //选中状态
  menuSelect = ({ item, key, keyPath, selectedKeys, domEvent }) => {
    this.setState({ selectID: keyPath[0] }, () => {
      sessionStorage.setItem('currentSelectID', this.state.selectID);
    });
  };

  handleMenuClick = key => {
    this.setState({ selectKeys: [key.keyPath[0]] }, () => {
      sessionStorage.setItem('currentSelectKeys', this.state.selectKeys);
    });
    if (key.keyPath[0] !== this.state.selectKeys[0]) {
      this.setState({ openKeys: [] }, () => {
        sessionStorage.setItem('currentOpenKeys', this.state.openKeys);
      });
    }
    sessionStorage.setItem('showWorkTable', false);
    this.props.dispatch({
      type: 'layouts/workerTablePush',
      payload: false,
    });
  };

  handleSubMenuOpenChange = keys => {
    //过滤已展开的二级菜单
    const latestOpenKey = keys.find(
      key => this.state.selectKeys.indexOf(key) === -1,
    );
    if (latestOpenKey && this.props.menuIdList.indexOf(latestOpenKey) === -1) {
      this.setState({ selectKeys: keys, openKeys: keys }, () => {
        sessionStorage.setItem('currentSelectKeys', this.state.selectKeys);
        sessionStorage.setItem('currentOpenKeys', this.state.openKeys);
      });
    } else {
      this.setState(
        {
          selectKeys: latestOpenKey ? [latestOpenKey] : [],
          openKeys: latestOpenKey ? [latestOpenKey] : [],
        },
        () => {
          sessionStorage.setItem('currentSelectKeys', this.state.selectKeys);
          sessionStorage.setItem('currentOpenKeys', this.state.openKeys);
        },
      );
    }
  };

  /**
   * 拖动aigc
   */
  handleDragStart = e => {
    // 记录鼠标相对于图标左上角的偏移量
    document.addEventListener('mousemove', this.handleDrag);
    document.addEventListener('mouseup', this.handleDragEnd);
  };
  handleDrag = e => {
    this.setState({
      isDragging: true,
    });

    const offsetX = window.innerWidth - e.clientX - 30;
    const offsetY = window.innerHeight - e.clientY - 30;
    // 更新图标位置
    // console.log(
    //   e.clientX,
    //   e.clientY,
    //   window.innerWidth,
    //   window.innerHeight,
    //   offsetX,
    //   offsetY,
    // );
    if (
      offsetX >= 0 &&
      offsetX <= window.innerWidth &&
      offsetY >= 0 &&
      offsetY <= window.innerHeight - 50
    ) {
      this.setState({ positionX: offsetX, positionY: offsetY });
    }
  };

  handleDragEnd = e => {
    document.removeEventListener('mousemove', this.handleDrag);
    document.removeEventListener('mouseup', this.handleDragEnd);
  };

  // 关闭工作台下线提示
  handleCloseErrorTips = () => {
    this.setState({
      showErrorTips: false,
    });
  };

  // 显示新手教程
  showBeginnerGuide = () => {
    this.setState({
      beginnerGuideNum: 1,
      // 关闭展开的一级菜单
      selectKeys: [],
      selectID: [],
      openKeys: [],
    });
  };
  // 新手提示下一步
  handleNext = () => {
    let { beginnerGuideNum } = this.state;
    this.setState({
      beginnerGuideNum: beginnerGuideNum + 1,
    });
    localStorage.setItem('clickBeginnerGuide', '1');
  };
  // 点击「知道了」事件
  handleComplete = () => {
    this.setState({
      beginnerGuideNum: 0,
    });
  };
  // 跳过教程
  handleSkipTutorial = () => {
    let { roleId } = this.state;
    localStorage.setItem('clickBeginnerGuide', '1');
    if (roleId == '1001') {
      this.setState({
        beginnerGuideNum: 7,
      });
      setTimeout(() => {
        this.setState({
          beginnerGuideNum: 0,
        });
      }, 2000);
    } else if (roleId == '1003') {
      this.setState({
        beginnerGuideNum: 6,
      });
      setTimeout(() => {
        this.setState({
          beginnerGuideNum: 0,
        });
      }, 2000);
    } else if (roleId == '1005') {
      this.setState({
        beginnerGuideNum: 6,
      });
      setTimeout(() => {
        this.setState({
          beginnerGuideNum: 0,
        });
      }, 2000);
    }
  };
  // 新手提示不再提示
  handleNoPrompt = e => {
    let value = e.target.checked;
    this.setState({
      promptCheck: value,
    });
    if (value) {
      this.queryUpdateWelcomeRemind(0);
    } else {
      this.queryUpdateWelcomeRemind(1);
    }
  };
  // 是否不再提示新手引导
  queryUpdateWelcomeRemind = promptNum => {
    let params = {
      remindFlag: promptNum,
    };
    // let connect = JSON.parse(value);
    this.props.dispatch({
      type: 'layouts/updateWelcomeRemind',
      payload: params,
      callback: response => {
        if (response.code == 200) {
          // console.log(response);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  /**
   * 控制agentStatus，websocketStatus和agentStatus同时为true才上线
   */
  handleAgentStatusChange = (e, option) => {
    //如果已达上线人数上限，则不允许上线
    if (!this.props.allowOnlineCountFlag && e.split('_')?.[0] === '1') {
      notification.error({
        message: getIntl().formatMessage({
          id: 'workerOffers.agent.status.limit',
        }),
      });
      return;
    }

    this.props.dispatch({
      type: 'layouts/setAgentStatus',
      payload: e,
    });
    this.props.dispatch({
      type: 'layouts/setAgentStatusName',
      payload: option.label,
    });
    this.setState({
      agentStatusCurrent: e,
    });
    if (e.split('_')?.[0] === '1') {
      this.props.dispatch({
        type: 'global/changeConnectState',
        payload: 'topLine',
      });
    } else {
      this.props.dispatch({
        type: 'global/changeConnectState',
        payload: 'downLine',
      });
    }
  };
  render() {
    const {
      menuList,
      user: { userName, roleList, awsAccountConfigType },
      connectList,
      selectedConnect,
      userAvatar,
      open,
    } = this.props;
    const {
      collapsed,
      showErrorTips,
      roleId,
      beginnerGuideNum,
      promptCheck,
    } = this.state;
    let currentLanguage = getLocale();
    let language = currentLanguage === 'zh-CN';
    //判断是否展示工作台
    let bannerWorker = false;
    //存在菜单权限才展示工作台
    menuList &&
      menuList.forEach(item => {
        if (item.menuId === '100000') {
          bannerWorker = true;
        }
      });
    //管理员才有的菜单awsAccountSetting
    const menu1 = (
      <Menu>
        {this.props.user.awsAccountConfigType ? (
          <Menu.Item
            key={1}
            onClick={() => {
              this.setState({
                path: [
                  getIntl().formatMessage({
                    id: 'layout.setting.aws.account',
                  }),
                  '',
                ],
                url: '/awsAccountSetting',
                // showWorkTable: false,
              }); // 更新 path
              sessionStorage.setItem('showWorkTable', false);
              this.props.dispatch({
                type: 'layouts/workerTablePush',
                payload: false,
              });
            }}
          >
            <Link to={'/awsAccountSetting'}>
              <div className="AwsAccountIcon"></div>
              <div>
                <FormattedMessage id="layout.setting.aws.account" />
              </div>
            </Link>
          </Menu.Item>
        ) : (
          ''
        )}
      </Menu>
    );
    //计量计费菜单
    const setMenuItem = (
      <Menu>
        <Menu.Item
          key={1}
          onClick={() => {
            this.setState({
              path: [
                getIntl().formatMessage({
                  id: 'metering.billing',
                }),
                getIntl().formatMessage({
                  id: 'metering.billing.1',
                }),
              ],
              url: '/overview',
              // showWorkTable: false,
            }); // 更新 path
            sessionStorage.setItem('showWorkTable', false);
            this.props.dispatch({
              type: 'layouts/workerTablePush',
              payload: false,
            });
          }}
        >
          <Link to={'/overview'}>
            <div>
              <FormattedMessage id="metering.billing.1" />
            </div>
          </Link>
        </Menu.Item>
        <Menu.Item
          key={2}
          onClick={() => {
            this.setState({
              path: [
                getIntl().formatMessage({
                  id: 'metering.billing',
                }),
                getIntl().formatMessage({
                  id: 'metering.billing.2',
                }),
              ],
              url: '/externalIntelligentAgentAIGCPackage',
              // showWorkTable: false,
            }); // 更新 path
            sessionStorage.setItem('showWorkTable', false);
            this.props.dispatch({
              type: 'layouts/workerTablePush',
              payload: false,
            });
          }}
        >
          <Link to={'/externalIntelligentAgentAIGCPackage'}>
            <div>
              <FormattedMessage id="metering.billing.2" />
            </div>
          </Link>
        </Menu.Item>
        <Menu.Item
          key={3}
          onClick={() => {
            this.setState({
              path: [
                getIntl().formatMessage({
                  id: 'metering.billing',
                }),
                getIntl().formatMessage({
                  id: 'metering.billing.3',
                }),
              ],
              url: '/agentAIGCPackage',
              // showWorkTable: false,
            }); // 更新 path
            sessionStorage.setItem('showWorkTable', false);
            this.props.dispatch({
              type: 'layouts/workerTablePush',
              payload: false,
            });
          }}
        >
          <Link to={'/agentAIGCPackage'}>
            <div>
              <FormattedMessage id="metering.billing.3" />
            </div>
          </Link>
        </Menu.Item>
        <Menu.Item
          key={4}
          onClick={() => {
            this.setState({
              path: [
                getIntl().formatMessage({
                  id: 'metering.billing',
                }),
                getIntl().formatMessage({
                  id: 'metering.billing.4',
                }),
              ],
              url: '/phoneAIGCPackage',
              // showWorkTable: false,
            }); // 更新 path
            sessionStorage.setItem('showWorkTable', false);
            this.props.dispatch({
              type: 'layouts/workerTablePush',
              payload: false,
            });
          }}
        >
          <Link to={'/phoneAIGCPackage'}>
            <div>
              <FormattedMessage id="metering.billing.4" />
            </div>
          </Link>
        </Menu.Item>
        <Menu.Item
          key={5}
          onClick={() => {
            this.setState({
              path: [
                getIntl().formatMessage({
                  id: 'metering.billing',
                }),
                getIntl().formatMessage({
                  id: 'metering.billing.5',
                }),
              ],
              url: '/telephoneExpenses',
              // showWorkTable: false,
            }); // 更新 path
            sessionStorage.setItem('showWorkTable', false);
            this.props.dispatch({
              type: 'layouts/workerTablePush',
              payload: false,
            });
          }}
        >
          <Link to={'/telephoneExpenses'}>
            <div>
              <FormattedMessage id="metering.billing.5" />
            </div>
          </Link>
        </Menu.Item>
        <Menu.Item
          key={6}
          onClick={() => {
            this.setState({
              path: [
                getIntl().formatMessage({
                  id: 'metering.billing',
                }),
                getIntl().formatMessage({
                  id: 'alarm.rule.title.step.1',
                }),
              ],
              url: '/alarmRule',
              // showWorkTable: false,
            }); // 更新 path
            sessionStorage.setItem('showWorkTable', false);
            this.props.dispatch({
              type: 'layouts/workerTablePush',
              payload: false,
            });
          }}
        >
          <Link to={'/alarmRule'}>
            <div>
              <FormattedMessage id="alarm.rule.title.step.1" />
            </div>
          </Link>
        </Menu.Item>
      </Menu>
    );
    //拿到connectId
    let selectedConnectFindValue = connectList.find(
      item => JSON.parse(item.value).connectId === selectedConnect?.connectId,
    )?.value;

    return (
      <Layout className={styles.layoutContent}>
        <div className={styles.layoutContentAlert}>
          {['1001', '1005'].includes(roleId) &&
            this.state.alertList?.map(alert => {
              if (alert.alarmColor === '#FCBE44') {
                return (
                  <>
                    <Alert
                      message={alert.alarmContent}
                      type="warning"
                      showIcon
                      closable
                    />
                    <br />
                  </>
                );
              } else if (alert.alarmColor === '#F22417') {
                return (
                  <>
                    <Alert
                      message={alert.alarmContent}
                      type="error"
                      showIcon
                      closable
                    />
                    <br />
                  </>
                );
              }
            })}
          {this.state.showReconnectAlert && (
            <Alert
              message={getIntl().formatMessage({
                id: 'im.chat.translation.retry.re',
              })}
              type="error"
              showIcon
              closable
            />
          )}
        </div>
        {/*<div hidden={false}>*/}
        {/*  {this.state.ccpKey !== '0'} ? <WorktableContent key={this.state.ccpKey} />{' '}: ""*/}
        {/*</div>*/}
        <Header className="header">
          <img src={bannerTop} className={styles.bannerTop} />

          {/*<Button*/}
          {/*  type="text"*/}
          {/*  onClick={this.toggleCollapsed}*/}
          {/*  className={styles.bannerButton}*/}
          {/*>*/}
          {/*  {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}*/}
          {/*</Button>*/}
          <div
            // type="text"
            onClick={this.toggleCollapsed}
            className={styles.bannerButton}
          >
            {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          </div>

          <Breadcrumb
            style={{
              // margin: '16px 0',
              lineHeight: '60px',
              marginLeft: '40px',
              // margin: '20px 26px',
            }}
          >
            {this.state.path.map((item, index) => {
              return (
                <Breadcrumb.Item key={index}>
                  <Link to={this.state.url}>{item}</Link>
                </Breadcrumb.Item>
              );
            })}
          </Breadcrumb>

          <div className={styles.headerContent}>
            <div className={styles.logout} onClick={this.logout}>
              <img src={LogoutIcon} />
              <span>
                <FormattedMessage id="logout" />
              </span>
            </div>
            <div className={styles.line}></div>
            <Dropdown
              menu={{ items: this.itemsPerson }}
              placement="bottom"
              overlayClassName="connectDownPersonal"
            >
              <div className={styles.userName}>
                <img src={userAvatar} />
                <p title={userName}>{userName}</p>
              </div>
            </Dropdown>

            <div
              className={styles.helpIconContent}
              onClick={this.showBeginnerGuide}
            >
              <img src={HelpIcon} />
            </div>

            <img src={NotificationBanner} className={styles.settingOutlined} />
            {/* 套餐包 */}
            <Dropdown
              overlay={setMenuItem}
              placement="bottom"
              overlayClassName="setMenuDownMenu"
            >
              <img
                style={{
                  display:
                    roleList?.map(item => item.roleId).indexOf('1001') === -1
                      ? 'none'
                      : 'block',
                }}
                className={styles.settingOutlined}
                src={SetMenu}
              />
            </Dropdown>
            {/* 设置 */}
            {useAuthAccess().results({
              auth: this.props.authAccess,
              authKey: 'aws_account_configuration',
            }) && this.props.user.awsAccountConfigType ? (
              <Dropdown
                overlay={menu1}
                placement="bottom"
                overlayClassName="settingDownMenu"
              >
                <img
                  style={{
                    display:
                      roleList?.map(item => item.roleId).indexOf('1001') === -1
                        ? 'none'
                        : 'block',
                  }}
                  className={styles.settingOutlined}
                  src={SettingBanner}
                />
              </Dropdown>
            ) : null}
            {/* 重连按钮 */}
            {/* {this.state.showReconnectAlert && (
              <Button
                type="text"
                onClick={this.handleAgentStatusChange('1')}
                style={{
                  float: 'right',
                  border: 'none',
                  color: '#f22417',
                  marginLeft: '-30px',
                  marginTop: 20,
                  background: 'transparent',
                  zIndex: 999,
                }}
              >
                <FormattedMessage id="im.agent.status.reconnect" />
              </Button>
            )} */}
            {/* 工作台入口 */}
            {bannerWorker && (
              <>
                <div
                  className={styles.workTableBtn1}
                  onClick={this.workerPush}
                  style={{
                    display:
                      this.props.connectionState == 'downLine'
                        ? // this.state.agentStatusCurrent == '0'
                          'block'
                        : 'none',
                  }}
                >
                  <img
                    style={{
                      display:
                        this.props.connectionState == 'downLine'
                          ? // this.state.agentStatusCurrent == '0'
                            'block'
                          : 'none',
                    }}
                    src={Call1}
                    className={styles.settingOutlined1}
                  />
                  <span>
                    <FormattedMessage
                      id="layout.worktable"
                      defaultMessage="工作台"
                    />
                  </span>
                </div>
                <div
                  className={styles.workTableBtn}
                  onClick={this.workerPush}
                  style={{
                    display:
                      this.props.connectionState == 'topLine'
                        ? // this.state.agentStatusCurrent == '1'
                          'block'
                        : 'none',
                  }}
                >
                  <img
                    style={{
                      display:
                        this.props.connectionState == 'topLine'
                          ? // this.state.agentStatusCurrent == '1'
                            'block'
                          : 'none',
                    }}
                    src={CallLine}
                    className={styles.settingOutlined1}
                  />
                  <span>
                    <FormattedMessage
                      id="layout.worktable"
                      defaultMessage="工作台"
                    />
                  </span>
                </div>
                <div
                  className={styles.workTableBtn}
                  onClick={this.workerPush}
                  style={{
                    display:
                      this.props.connectionState == 'telegram'
                        ? 'block'
                        : 'none',
                  }}
                >
                  <img
                    // onClick={() => this.changeConnectStatus('topLine')}
                    style={{
                      display:
                        this.props.connectionState == 'telegram'
                          ? 'block'
                          : 'none',
                    }}
                    src={Telegram}
                    className={styleCss.settingOutlined}
                  />
                  <span>
                    <FormattedMessage
                      id="layout.worktable"
                      defaultMessage="工作台"
                    />
                  </span>
                </div>
                <div
                  className={styles.workTableBtn2}
                  onClick={this.workerPush}
                  style={{
                    display:
                      this.props.connectionState == 'offline'
                        ? 'block'
                        : 'none',
                  }}
                >
                  <img
                    // onClick={() => this.changeConnectStatus('topLine')}
                    style={{
                      display:
                        this.props.connectionState == 'offline'
                          ? 'block'
                          : 'none',
                    }}
                    src={OffLine}
                    className={styles.settingOutlined1}
                  />
                  <span>
                    <FormattedMessage
                      id="layout.worktable"
                      defaultMessage="工作台"
                    />
                  </span>
                </div>
              </>
            )}
            {/* 选择联络线路，用于旧版工作台 */}
            {this.props.user.openSelfChat == '0' ? (
              <Select
                value={selectedConnectFindValue}
                dropdownClassName="selectInput"
                style={{
                  width: 212,
                  height: 32,
                  display: roleId == '1003' ? 'block' : 'none',
                }}
                onChange={e => this.onChangeConnect(e)}
                placeholder={getIntl().formatMessage({
                  id: 'input.select',
                })}
              >
                {connectList.map(({ value, label }, idx) => (
                  <Select.Option key={idx} value={value}>
                    {label}
                    <a
                      // href={JSON.parse(value).connectUrl}
                      target="_blank"
                      style={{ float: 'right' }}
                    >
                      {/* <PaperClipOutlined className={styles.attachmentIcon} /> */}
                    </a>
                  </Select.Option>
                ))}
              </Select>
            ) : (
              ''
            )}
            {/* 在线状态控制 */}
            {bannerWorker &&
            this.props.user.openSelfChat != '0' &&
            this.props.user.roleList?.[0]?.roleId === '1003' ? (
              <div className={styles.headerAgent}>
                {/* websocketStatus和agentStatus同时为true才上线,即agentStatusCurrent*/}
                <span>
                  {this.state.agentStatusCurrent?.split('_')?.[0] == '1'
                    ? AgentIconAvailable()
                    : AgentIconOffline()}
                </span>
                <Select
                  defaultValue={this.state.agentStatusCurrent}
                  style={{ width: 'auto' }}
                  value={this.state.agentStatusCurrent}
                  bordered={false}
                  onChange={(e, option) =>
                    this.handleAgentStatusChange(e, option)
                  }
                  popupMatchSelectWidth={false}
                  dropdownStyle={{ minWidth: 150, width: 150 }}
                  options={this.state.agentStatusList.map(item => ({
                    label: item.agentStatusName,
                    value: item.type + '_' + item.agentStatusId,
                    key: item.agentStatusId,
                  }))}
                ></Select>
              </div>
            ) : null}
          </div>

          {/* <div
            className={styles.downLineContent}
            style={{
              display:
                this.props.connectionState == 'downLine' &&
                showErrorTips &&
                roleId == '1003'
                  ? 'block'
                  : 'none',
            }}
          >
            <img src={ErrorIcon} />
            <span>
              <FormattedMessage
                id="work.table.down.line.tips"
                defaultMessage="工作台连接失败，尝试刷新或重新连接到互联网。"
              />
            </span>
            <img
              onClick={this.handleCloseErrorTips}
              className={styles.closeIcon}
              src={CloseIcon}
            />
          </div>

          <div
            className={styles.offLineContent}
            style={{
              display:
                this.props.connectionState == 'offline' &&
                showErrorTips &&
                roleId == '1003'
                  ? 'block'
                  : 'none',
            }}
          >
            <img src={WaringIcon} />
            <span>
              <FormattedMessage
                id="work.table.down.line.tips.1"
                defaultMessage="请注意，您的工作台当前处于下线状态，无法接收客户消息"
              />
            </span>
            <img
              onClick={this.handleCloseErrorTips}
              className={styles.closeIcon}
              src={CloseIcon}
            />
          </div> */}
        </Header>
        <Layout>
          <Sider
            width={208}
            style={{ overflowY: 'scroll' }}
            trigger={null}
            collapsible
            collapsed={collapsed}
          >
            {/* <img src={LogoImg} className={styles.logo} /> */}

            <div className={styles.menuContent}>
              <Menu
                mode="inline"
                inlineCollapsed={collapsed}
                openKeys={this.state.openKeys}
                onOpenChange={this.handleSubMenuOpenChange}
                onClick={this.handleMenuClick}
                selectedKeys={this.state.selectKeys}
                onSelect={this.menuSelect}
                style={{ height: '100%', borderRight: 0 }}
              >
                {menuList.map(menu => {
                  const menuName = menu.menuName;
                  const shortMenuName = menu.menuNameShort;

                  // 获取菜单的简称
                  if (menu.children && menu.children.length > 0) {
                    let children = menu.children;
                    let id = menu.menuId;
                    return (
                      <>
                        <SubMenu
                          key={menu.menuId}
                          disabled={menu.displayStatus !== 1}
                          icon={
                            collapsed ? (
                              <div
                                style={{
                                  display: 'flex',
                                  flexDirection: 'column',
                                  justifyContent: 'center',
                                  alignItems: 'center',
                                }}
                              >
                                <div style={{ width: 30, height: 30 }}>
                                  {IconsShrink[menu.icon]}
                                </div>
                                {menu.closeBetaFlag === 1 ? (
                                  <div
                                    style={{
                                      position: 'absolute',
                                      right: '-18px',
                                      // top: '10px',
                                    }}
                                  >
                                    <img
                                      src={BetaPng}
                                      style={{ width: 30, height: 30 }}
                                    />
                                  </div>
                                ) : null}
                                <div
                                  style={{
                                    height: 15,
                                    fontSize: 12,
                                    marginTop: -10,
                                    textAlign: 'center',
                                  }}
                                >
                                  {shortMenuName}
                                </div>
                              </div>
                            ) : (
                              Icons[menu.icon]
                            )
                          }
                          className={
                            collapsed && id == this.state.selectID
                              ? styles.selectStyle
                              : ''
                          }
                          title={
                            <span
                              style={{
                                display: collapsed ? 'none' : 'flex',
                                alignItems: 'center',
                                opacity: collapsed ? 0 : 1,
                              }}
                            >
                              <span
                              // style={{
                              //   opacity: collapsed ? 0 : 0.6,
                              // }}
                              >
                                {menu.menuName}
                              </span>
                              {menu.closeBetaFlag === 1 ? (
                                <Beta
                                  width="24"
                                  height="24"
                                  style={{
                                    marginLeft: 5,
                                    opacity: 1,
                                  }}
                                />
                              ) : null}
                            </span>
                          }
                          onTitleClick={() => {
                            this.setState({
                              path: [menu.menuName, ''],
                              // showWorkTable: false,
                            }); // 更新 path
                          }}
                        >
                          <Tabs
                            style={{
                              paddingTop: 8,
                              paddingBottom: 8,
                              width: collapsed
                                ? menu.menuId === '130000' ||
                                  menu.menuId === '810000' ||
                                  menu.menuId === '910000' ||
                                  menu.menuId === '920000'
                                  ? getLocale() === 'zh-CN'
                                    ? 200
                                    : 400
                                  : getLocale() === 'zh-CN'
                                  ? 200
                                  : 250
                                : '',
                            }}
                            tabPosition="right"
                            activeKey={sessionStorage.getItem(
                              'currentActiveKey',
                            )}
                            items={children.map((sub, i) => {
                              const id = sub.menuId;
                              return {
                                key: id,
                                label: (
                                  <Menu.Item
                                    key={sub.menuId}
                                    disabled={sub.displayStatus !== 1}
                                    onClick={() => {
                                      this.setState(
                                        {
                                          path: [
                                            menu.menuName,
                                            sub.menuName,
                                            '',
                                          ],
                                          url: sub.component,
                                          // showWorkTable: false,
                                        },
                                        () => {
                                          sessionStorage.setItem(
                                            'currentMenu',
                                            this.state.path,
                                          );
                                          sessionStorage.setItem(
                                            'currentUrl',
                                            this.state.url,
                                          );
                                          sessionStorage.setItem(
                                            'currentActiveKey',
                                            sub.menuId,
                                          );
                                        },
                                      ); // 更新 path
                                      sessionStorage.setItem(
                                        'showWorkTable',
                                        false,
                                      );
                                    }}
                                    title={sub.menuName}
                                  >
                                    {/* closeBetaPermission为0没有beta权限，跳beta页 */}
                                    <Link
                                      to={
                                        sub.displayStatus !== 1
                                          ? ''
                                          : this.props.user
                                              .closeBetaPermission === 0 &&
                                            sub.closeBetaFlag === 1
                                          ? '/beta'
                                          : sub.component
                                      }
                                      title={sub.menuName}
                                    >
                                      <span
                                        style={{
                                          display: 'flex',
                                          alignItems: 'center',
                                          opacity: 1,
                                        }}
                                      >
                                        <span
                                          className={styles.layoutSubMenuName}
                                          style={{
                                            // opacity:
                                            //   sessionStorage.getItem(
                                            //     'currentActiveKey',
                                            //   ) === sub.menuId
                                            //     ? 1
                                            //     : 0.6,
                                            color:
                                              sessionStorage.getItem(
                                                'currentActiveKey',
                                              ) === sub.menuId
                                                ? '#3463fc'
                                                : '',
                                          }}
                                        >
                                          {sub.menuName}
                                        </span>
                                        {sub.closeBetaFlag === 1 ? (
                                          <Beta
                                            width="24"
                                            height="24"
                                            style={{
                                              marginLeft: 5,
                                              opacity: 1,
                                            }}
                                          />
                                        ) : null}
                                      </span>
                                    </Link>
                                  </Menu.Item>
                                ),
                              };
                            })}
                          />
                        </SubMenu>
                        {(['900000'].includes(menu.menuId) &&
                          menuList[menuList.length - 1].menuId !== '900000') ||
                        (['920000'].includes(menu.menuId) &&
                          menuList[menuList.length - 1].menuId !== '920000') ? (
                          <div
                            style={{
                              width: collapsed ? 70 : 170,
                              borderBottom: '1px #e6e6e6 solid',
                              marginLeft: collapsed ? 4 : 24,
                            }}
                          ></div>
                        ) : (
                          ''
                        )}
                      </>
                    );
                  } else {
                    //过滤工作台
                    if (menu.menuId !== '100000') {
                      return (
                        <>
                          <Menu.Item
                            key={menu.menuId}
                            // icon={<PrinterOutlined />}
                            icon={
                              collapsed ? (
                                <div
                                  style={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                  }}
                                >
                                  <div style={{ width: 30, height: 30 }}>
                                    {IconsShrink[menu.icon]}
                                  </div>
                                  {menu.closeBetaFlag === 1 ? (
                                    <div
                                      style={{
                                        position: 'absolute',
                                        right: '-18px',
                                        top: '10px',
                                      }}
                                    >
                                      <img
                                        src={BetaPng}
                                        style={{ width: 30, height: 30 }}
                                      />
                                    </div>
                                  ) : null}
                                  <div
                                    style={{
                                      height: 15,
                                      fontSize: 12,
                                      marginTop: -10,
                                      textAlign: 'center',
                                    }}
                                  >
                                    {shortMenuName}
                                  </div>
                                </div>
                              ) : (
                                Icons[menu.icon]
                              )
                            }
                            disabled={menu.displayStatus !== 1}
                            className={
                              collapsed && this.state.selectID == menu.menuId
                                ? styles.selectStyle
                                : ''
                            }
                            // icon={Icons[menu.icon]}
                            onClick={() => {
                              this.setState(
                                {
                                  path: [menu.menuName, ''],
                                  url: menu.component,
                                  // showWorkTable: false,
                                },
                                () => {
                                  sessionStorage.setItem(
                                    'currentMenu',
                                    this.state.path,
                                  );
                                  sessionStorage.setItem(
                                    'currentUrl',
                                    this.state.url,
                                  );
                                },
                              ); // 更新 path
                              sessionStorage.setItem('showWorkTable', false);
                              this.props.dispatch({
                                type: 'layouts/workerTablePush',
                                payload: false,
                              });
                            }}
                            title={collapsed ? shortMenuName : menuName}
                          >
                            <Link
                              to={
                                menu.displayStatus !== 1
                                  ? ''
                                  : this.props.user.closeBetaPermission === 0 &&
                                    menu.closeBetaFlag === 1
                                  ? '/beta'
                                  : menu.component
                              }
                              title={collapsed ? shortMenuName : menuName}
                            >
                              {collapsed ? (
                                shortMenuName
                              ) : (
                                <span
                                  style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    opacity: 1,
                                  }}
                                >
                                  <span
                                  // style={{
                                  //   opacity: 0.6,
                                  // }}
                                  >
                                    {menuName}
                                  </span>
                                  {menu.closeBetaFlag === 1 ? (
                                    <Beta
                                      width="24"
                                      height="24"
                                      style={{
                                        marginLeft: 5,
                                        opacity: 1,
                                      }}
                                    />
                                  ) : null}
                                </span>
                              )}
                              {/* {language ? menu.menuName : menu.menuNameEn} */}
                            </Link>
                          </Menu.Item>
                          {/* 判断分区的横线是否展示 */}
                          {(['900000'].includes(menu.menuId) &&
                            menuList[menuList.length - 1].menuId !==
                              '900000') ||
                          (['920000'].includes(menu.menuId) &&
                            menuList[menuList.length - 1].menuId !==
                              '920000') ? (
                            <div
                              style={{
                                width: collapsed ? 70 : 170,
                                borderBottom: '1px #e6e6e6 solid',
                                marginLeft: collapsed ? 4 : 24,
                              }}
                            ></div>
                          ) : (
                            ''
                          )}
                        </>
                      );
                    } else {
                      return null;
                    }
                  }
                })}
              </Menu>
            </div>
          </Sider>
          <Layout style={{ overflowY: 'scroll' }}>
            <Content
              style={{
                // padding: 24,
                margin: 0,
                minHeight: 'auto',
              }}
            >
              {(!bannerWorker || (bannerWorker && !this.state.showWorkTable)) &&
                this.props.children}
              {bannerWorker && (
                <div
                  style={{
                    display:
                      this.state.showWorkTable || this.props.showWorkTabl
                        ? 'block'
                        : 'none',
                  }}
                >
                  {this.props.user.openSelfChat == '0' ? (
                    <MemoizedWorktableContentOld
                      ccpKey={this.state.ccpKey}
                      // showWorkTable={this.props.showWorkTable}
                    />
                  ) : this.props.user.userId ? (
                    <MemoizedWorktableContentNew
                      ccpKey={this.state.ccpKey}
                      // showWorkTable={this.props.showWorkTable}
                    />
                  ) : (
                    ''
                  )}
                </div>
              )}
              {!open ? (
                <div
                  style={{
                    position: 'absolute',
                    zIndex: 1001,
                    bottom: `${this.state.positionY}px`,
                    right: `${this.state.positionX}px`,
                  }}
                  onMouseDown={this.handleDragStart}
                  onClick={this.openAIGC}
                  className={styles.draggableBox}
                >
                  <img src={AIGCGif} style={{ width: 50, height: 50 }} />
                </div>
              ) : (
                ''
              )}
              <AIGC
                visible={this.state.parentStateValue}
                onParentStateChange={this.handleParentStateChange}
              />
            </Content>
          </Layout>
        </Layout>

        {/*管理员角色新手引导*/}
        {/*帮助*/}
        <div
          style={{
            display:
              roleId == '1001' && beginnerGuideNum == 7 ? 'block' : 'none',
          }}
          className={styles.helpBeginnerGuide}
        >
          <img
            className={styles.helpBannerImg}
            src={HelpIcon}
            style={{ right: currentLanguage == 'zh-CN' ? '242px' : '257px' }}
          />
          <img
            className={styleCss.settingVerticalBeginnerGuideIcon}
            // src={verticalBeginnerGuide}
            src={BeginnerGuidanceArrow}
            style={{ right: currentLanguage == 'zh-CN' ? '215px' : '230px' }}
          />
          <div
            className={styleCss.helpDetailContent}
            style={{ right: currentLanguage == 'zh-CN' ? '130px' : '160px' }}
          >
            <p className={styleCss.beginnerGuideContent}>
              <FormattedMessage
                id="connect.beginner.guide.help.content"
                defaultMessage="点击这里可以打开当前页面的教程哦～"
              />
            </p>
            {/*<div className={styleCss.beginnerGuideFooter}>*/}
            {/*  <Button*/}
            {/*    className={styleCss.nextBtn}*/}
            {/*    onClick={this.handleComplete}*/}
            {/*  >*/}
            {/*    <FormattedMessage*/}
            {/*      id="connect.beginner.guide.know.btn"*/}
            {/*      defaultMessage="知道了"*/}
            {/*    />*/}
            {/*  </Button>*/}
            {/*</div>*/}
          </div>
        </div>

        <div
          style={{
            display:
              roleId == '1001' &&
              beginnerGuideNum !== 0 &&
              beginnerGuideNum !== 7
                ? 'block'
                : 'none',
          }}
          className={styles.homepageBeginnerGuide}
        >
          {/*用户头像*/}
          <div
            style={{ display: beginnerGuideNum == 6 ? 'block' : 'none' }}
            className={styles.userBeginnerGuide}
          >
            <img
              className={styles.userBannerImg}
              src={UserBanner}
              style={{ right: currentLanguage == 'zh-CN' ? '190px' : '205px' }}
            />
            <img
              className={styles.settingVerticalBeginnerGuideIcon}
              src={verticalBeginnerGuide}
              style={{ right: currentLanguage == 'zh-CN' ? '200px' : '215px' }}
            />
            <div
              className={styles.userDetailContent}
              style={{ right: currentLanguage == 'zh-CN' ? '135px' : '165px' }}
            >
              <p className={styles.beginnerGuideTitle}>
                <FormattedMessage
                  id="connect.beginner.guide.title.1"
                  defaultMessage="用户"
                />
              </p>
              <p className={styles.beginnerGuideContent}>
                <FormattedMessage
                  id="connect.beginner.guide.user.content"
                  defaultMessage="您可以在这里维护自己的个人信息，设置语言及时区"
                />
              </p>
              <div className={styles.beginnerGuideFooter}>
                <Checkbox onChange={this.handleNoPrompt} checked={promptCheck}>
                  <FormattedMessage
                    id="connect.beginner.guide.not.prompt"
                    defaultMessage="不再提示"
                  />
                </Checkbox>
                <Button
                  className={styles.nextBtn}
                  onClick={this.handleSkipTutorial}
                >
                  <FormattedMessage
                    id="connect.beginner.guide.complete.btn"
                    defaultMessage="完成"
                  />
                </Button>
                <span
                  onClick={this.handleSkipTutorial}
                  className={styles.jumpBeginnerGuide}
                >
                  <FormattedMessage
                    id="connect.beginner.guide.skip.tutorial"
                    defaultMessage="跳过教程"
                  />
                </span>
              </div>
            </div>
          </div>
          {/*设置*/}
          <div
            style={{ display: beginnerGuideNum == 1 ? 'block' : 'none' }}
            className={styles.settingBeginnerGuide}
          >
            <img
              className={styles.settingBannerImg}
              src={SettingBanner}
              style={{ right: currentLanguage == 'zh-CN' ? '325px' : '340px' }}
            />
            <img
              className={styles.settingVerticalBeginnerGuideIcon}
              src={verticalBeginnerGuide}
              style={{ right: currentLanguage == 'zh-CN' ? '335px' : '350px' }}
            />
            <div
              className={styles.settingDetailContent}
              style={{ right: currentLanguage == 'zh-CN' ? '445px' : '460px' }}
            >
              <p className={styles.beginnerGuideTitle}>
                <FormattedMessage
                  id="connect.beginner.guide.title.2"
                  defaultMessage="设置"
                />
              </p>
              <p className={styles.beginnerGuideContent}>
                <FormattedMessage
                  id="connect.beginner.guide.setting.content"
                  defaultMessage="您可以在这里对渠道（支持电话、邮箱、在线聊天、WhatsApp），客户资料扩展信息，工单扩展字段等信息进行设置"
                />
              </p>
              <div className={styles.beginnerGuideFooter}>
                <Checkbox onChange={this.handleNoPrompt} checked={promptCheck}>
                  <FormattedMessage
                    id="connect.beginner.guide.not.prompt"
                    defaultMessage="不再提示"
                  />
                </Checkbox>
                <Button className={styles.nextBtn} onClick={this.handleNext}>
                  <FormattedMessage
                    id="connect.beginner.guide.next.btn"
                    defaultMessage="下一步"
                  />
                </Button>
                <span
                  onClick={this.handleSkipTutorial}
                  className={styles.jumpBeginnerGuide}
                >
                  <FormattedMessage
                    id="connect.beginner.guide.skip.tutorial"
                    defaultMessage="跳过教程"
                  />
                </span>
              </div>
            </div>
          </div>
          {/*平台用户管理*/}
          <div
            style={{
              display: !collapsed && beginnerGuideNum == 2 ? 'block' : 'none',
            }}
            className={styles.platformUserManagementBeginnerGuide}
          >
            <div className={styles.platformUserManagementIcon}>
              <p>
                <FormattedMessage
                  id="connect.beginner.guide.user.management.management"
                  defaultMessage="平台用户管理"
                />
              </p>
            </div>
            <img
              className={styles.transverseVerticalBeginnerGuideIcon}
              src={TransverseVerticalBeginnerGuideIcon}
            />
            <div className={styles.platformUserManagementContent}>
              <p className={styles.beginnerGuideTitle}>
                <FormattedMessage
                  id="connect.beginner.guide.user.management.management"
                  defaultMessage="平台用户管理"
                />
              </p>
              <p className={styles.beginnerGuideContent}>
                <FormattedMessage
                  id="connect.beginner.guide.platform.user.management.content"
                  defaultMessage="在这里维护您的组织结构及平台用户"
                />
              </p>
              <div className={styles.beginnerGuideFooter}>
                <Checkbox onChange={this.handleNoPrompt} checked={promptCheck}>
                  <FormattedMessage
                    id="connect.beginner.guide.not.prompt"
                    defaultMessage="不再提示"
                  />
                </Checkbox>
                <Button className={styles.nextBtn} onClick={this.handleNext}>
                  <FormattedMessage
                    id="connect.beginner.guide.next.btn"
                    defaultMessage="下一步"
                  />
                </Button>
                <span
                  onClick={this.handleSkipTutorial}
                  className={styles.jumpBeginnerGuide}
                >
                  <FormattedMessage
                    id="connect.beginner.guide.skip.tutorial"
                    defaultMessage="跳过教程"
                  />
                </span>
              </div>
            </div>
          </div>
          {/*工单中心*/}
          <div
            style={{
              display: !collapsed && beginnerGuideNum == 3 ? 'block' : 'none',
            }}
            className={styles.workOrderCenterBeginnerGuide}
          >
            <div className={styles.workOrderCenterIcon}>
              <p>
                <FormattedMessage
                  id="connect.beginner.guide.work.order.center"
                  defaultMessage="工单中心"
                />
              </p>
            </div>
            <img
              className={styles.transverseVerticalBeginnerGuideIcon}
              src={TransverseVerticalBeginnerGuideIcon}
            />
            <div className={styles.workOrderCenterContent}>
              <p className={styles.beginnerGuideTitle}>
                <FormattedMessage
                  id="connect.beginner.guide.work.order.center"
                  defaultMessage="工单中心"
                />
              </p>
              <p className={styles.beginnerGuideContent}>
                <FormattedMessage
                  id="connect.beginner.guide.work.order.center.content"
                  defaultMessage="在这里浏览公司内所有工单的信息"
                />
              </p>
              <div className={styles.beginnerGuideFooter}>
                <Checkbox onChange={this.handleNoPrompt} checked={promptCheck}>
                  <FormattedMessage
                    id="connect.beginner.guide.not.prompt"
                    defaultMessage="不再提示"
                  />
                </Checkbox>
                <Button className={styles.nextBtn} onClick={this.handleNext}>
                  <FormattedMessage
                    id="connect.beginner.guide.next.btn"
                    defaultMessage="下一步"
                  />
                </Button>
                <span
                  onClick={this.handleSkipTutorial}
                  className={styles.jumpBeginnerGuide}
                >
                  <FormattedMessage
                    id="connect.beginner.guide.skip.tutorial"
                    defaultMessage="跳过教程"
                  />
                </span>
              </div>
            </div>
          </div>
          {/*客户中心*/}
          <div
            style={{
              display: !collapsed && beginnerGuideNum == 4 ? 'block' : 'none',
            }}
            className={styles.customerCenterBeginnerGuide}
          >
            <div className={styles.customerCenterIcon}>
              <p>
                <FormattedMessage
                  id="connect.beginner.guide.customer.center"
                  defaultMessage="客户中心"
                />
              </p>
            </div>
            <img
              className={styles.transverseVerticalBeginnerGuideIcon}
              src={TransverseVerticalBeginnerGuideIcon}
            />
            <div className={styles.customerCenterContent}>
              <p className={styles.beginnerGuideTitle}>
                <FormattedMessage
                  id="connect.beginner.guide.customer.center"
                  defaultMessage="客户中心"
                />
              </p>
              <p className={styles.beginnerGuideContent}>
                <FormattedMessage
                  id="connect.beginner.guide.customer.center.content"
                  defaultMessage="在这里查看您公司所有的客户资料信息"
                />
              </p>
              <div className={styles.beginnerGuideFooter}>
                <Checkbox onChange={this.handleNoPrompt} checked={promptCheck}>
                  <FormattedMessage
                    id="connect.beginner.guide.not.prompt"
                    defaultMessage="不再提示"
                  />
                </Checkbox>
                <Button className={styles.nextBtn} onClick={this.handleNext}>
                  <FormattedMessage
                    id="connect.beginner.guide.next.btn"
                    defaultMessage="下一步"
                  />
                </Button>
                <span
                  onClick={this.handleSkipTutorial}
                  className={styles.jumpBeginnerGuide}
                >
                  <FormattedMessage
                    id="connect.beginner.guide.skip.tutorial"
                    defaultMessage="跳过教程"
                  />
                </span>
              </div>
            </div>
          </div>
          {/*知识库*/}
          <div
            style={{
              display: !collapsed && beginnerGuideNum == 5 ? 'block' : 'none',
            }}
            className={styles.knowledgeBeginnerGuide}
          >
            <div className={styles.knowledgeCenterIcon}>
              <p>
                <FormattedMessage
                  id="connect.beginner.guide.knowledge"
                  defaultMessage="知识库"
                />
              </p>
            </div>
            <img
              className={styles.transverseVerticalBeginnerGuideIcon}
              src={TransverseVerticalBeginnerGuideIcon}
            />
            <div className={styles.knowledgeContent}>
              <p className={styles.beginnerGuideTitle}>
                <FormattedMessage
                  id="connect.beginner.guide.knowledge"
                  defaultMessage="知识库"
                />
              </p>
              <p className={styles.beginnerGuideContent}>
                <FormattedMessage
                  id="connect.beginner.guide.knowledge.content"
                  defaultMessage="您可以在这里维护公司内部沉淀的知识库。支持问答知识库及文档知识库"
                />
              </p>
              <div className={styles.beginnerGuideFooter}>
                <Checkbox onChange={this.handleNoPrompt} checked={promptCheck}>
                  <FormattedMessage
                    id="connect.beginner.guide.not.prompt"
                    defaultMessage="不再提示"
                  />
                </Checkbox>
                <Button className={styles.nextBtn} onClick={this.handleNext}>
                  <FormattedMessage
                    id="connect.beginner.guide.next.btn"
                    defaultMessage="下一步"
                  />
                </Button>
                <span
                  onClick={this.handleSkipTutorial}
                  className={styles.jumpBeginnerGuide}
                >
                  <FormattedMessage
                    id="connect.beginner.guide.skip.tutorial"
                    defaultMessage="跳过教程"
                  />
                </span>
              </div>
            </div>
          </div>

          {/*平台用户管理--菜单收缩*/}
          <div
            style={{
              display: collapsed && beginnerGuideNum == 2 ? 'block' : 'none',
            }}
            className={styles.collapsedPlatformUserManagementBeginnerGuide}
          >
            <div className={styles.collapsedPlatformUserManagementIcon}>
              <p>
                <FormattedMessage
                  id="connect.beginner.guide.user.management"
                  defaultMessage="平台用户"
                />
              </p>
            </div>
            <img
              className={styles.transverseVerticalBeginnerGuideIcon}
              src={TransverseVerticalBeginnerGuideIcon}
            />
            <div className={styles.collapsedPlatformUserManagementContent}>
              <p className={styles.beginnerGuideTitle}>
                <FormattedMessage
                  id="connect.beginner.guide.user.management"
                  defaultMessage="平台用户"
                />
              </p>
              <p className={styles.beginnerGuideContent}>
                <FormattedMessage
                  id="connect.beginner.guide.platform.user.management.content"
                  defaultMessage="在这里维护您的组织结构及平台用户"
                />
              </p>
              <div className={styles.beginnerGuideFooter}>
                <Checkbox onChange={this.handleNoPrompt} checked={promptCheck}>
                  <FormattedMessage
                    id="connect.beginner.guide.not.prompt"
                    defaultMessage="不再提示"
                  />
                </Checkbox>
                <Button className={styles.nextBtn} onClick={this.handleNext}>
                  <FormattedMessage
                    id="connect.beginner.guide.next.btn"
                    defaultMessage="下一步"
                  />
                </Button>
                <span
                  onClick={this.handleSkipTutorial}
                  className={styles.jumpBeginnerGuide}
                >
                  <FormattedMessage
                    id="connect.beginner.guide.skip.tutorial"
                    defaultMessage="跳过教程"
                  />
                </span>
              </div>
            </div>
          </div>
          {/*工单中心--菜单收缩*/}
          <div
            style={{
              display: collapsed && beginnerGuideNum == 3 ? 'block' : 'none',
            }}
            className={styles.collapsedWorkOrderCenterBeginnerGuide}
          >
            <div className={styles.workOrderCenterIcon}>
              <p>
                <FormattedMessage
                  id="connect.beginner.guide.work.record"
                  defaultMessage="工单"
                />
              </p>
            </div>
            <img
              className={styles.transverseVerticalBeginnerGuideIcon}
              src={TransverseVerticalBeginnerGuideIcon}
            />
            <div className={styles.workOrderCenterContent}>
              <p className={styles.beginnerGuideTitle}>
                <FormattedMessage
                  id="connect.beginner.guide.work.record"
                  defaultMessage="工单"
                />
              </p>
              <p className={styles.beginnerGuideContent}>
                <FormattedMessage
                  id="connect.beginner.guide.work.order.center.content"
                  defaultMessage="在这里浏览公司内所有工单的信息"
                />
              </p>
              <div className={styles.beginnerGuideFooter}>
                <Checkbox onChange={this.handleNoPrompt} checked={promptCheck}>
                  <FormattedMessage
                    id="connect.beginner.guide.not.prompt"
                    defaultMessage="不再提示"
                  />
                </Checkbox>
                <Button className={styles.nextBtn} onClick={this.handleNext}>
                  <FormattedMessage
                    id="connect.beginner.guide.next.btn"
                    defaultMessage="下一步"
                  />
                </Button>
                <span
                  onClick={this.handleSkipTutorial}
                  className={styles.jumpBeginnerGuide}
                >
                  <FormattedMessage
                    id="connect.beginner.guide.skip.tutorial"
                    defaultMessage="跳过教程"
                  />
                </span>
              </div>
            </div>
          </div>
          {/*客户中心--菜单收缩*/}
          <div
            style={{
              display: collapsed && beginnerGuideNum == 4 ? 'block' : 'none',
            }}
            className={styles.collapsedCustomerCenterBeginnerGuide}
          >
            <div className={styles.customerCenterIcon}>
              <p>
                <FormattedMessage
                  id="connect.beginner.guide.custom"
                  defaultMessage="客户"
                />
              </p>
            </div>
            <img
              className={styles.transverseVerticalBeginnerGuideIcon}
              src={TransverseVerticalBeginnerGuideIcon}
            />
            <div className={styles.customerCenterContent}>
              <p className={styles.beginnerGuideTitle}>
                <FormattedMessage
                  id="connect.beginner.guide.custom"
                  defaultMessage="客户"
                />
              </p>
              <p className={styles.beginnerGuideContent}>
                <FormattedMessage
                  id="connect.beginner.guide.customer.center.content"
                  defaultMessage="在这里查看您公司所有的客户资料信息"
                />
              </p>
              <div className={styles.beginnerGuideFooter}>
                <Checkbox onChange={this.handleNoPrompt} checked={promptCheck}>
                  <FormattedMessage
                    id="connect.beginner.guide.not.prompt"
                    defaultMessage="不再提示"
                  />
                </Checkbox>
                <Button className={styles.nextBtn} onClick={this.handleNext}>
                  <FormattedMessage
                    id="connect.beginner.guide.next.btn"
                    defaultMessage="下一步"
                  />
                </Button>
                <span
                  onClick={this.handleSkipTutorial}
                  className={styles.jumpBeginnerGuide}
                >
                  <FormattedMessage
                    id="connect.beginner.guide.skip.tutorial"
                    defaultMessage="跳过教程"
                  />
                </span>
              </div>
            </div>
          </div>
          {/*知识库--菜单收缩*/}
          <div
            style={{
              display: collapsed && beginnerGuideNum == 5 ? 'block' : 'none',
            }}
            className={styles.collapsedKnowledgeBeginnerGuide}
          >
            <div className={styles.knowledgeCenterIcon}>
              <p>
                <FormattedMessage
                  id="connect.beginner.guide.knowledge1"
                  defaultMessage="知识库"
                />
              </p>
            </div>
            <img
              className={styles.transverseVerticalBeginnerGuideIcon}
              src={TransverseVerticalBeginnerGuideIcon}
            />
            <div className={styles.knowledgeContent}>
              <p className={styles.beginnerGuideTitle}>
                <FormattedMessage
                  id="connect.beginner.guide.knowledge1"
                  defaultMessage="知识库"
                />
              </p>
              <p className={styles.beginnerGuideContent}>
                <FormattedMessage
                  id="connect.beginner.guide.knowledge.content"
                  defaultMessage="您可以在这里维护公司内部沉淀的知识库。支持问答知识库及文档知识库"
                />
              </p>
              <div className={styles.beginnerGuideFooter}>
                <Checkbox onChange={this.handleNoPrompt} checked={promptCheck}>
                  <FormattedMessage
                    id="connect.beginner.guide.not.prompt"
                    defaultMessage="不再提示"
                  />
                </Checkbox>
                <Button className={styles.nextBtn} onClick={this.handleNext}>
                  <FormattedMessage
                    id="connect.beginner.guide.next.btn"
                    defaultMessage="下一步"
                  />
                </Button>
                <span
                  onClick={this.handleSkipTutorial}
                  className={styles.jumpBeginnerGuide}
                >
                  <FormattedMessage
                    id="connect.beginner.guide.skip.tutorial"
                    defaultMessage="跳过教程"
                  />
                </span>
              </div>
            </div>
          </div>
        </div>

        {/*客服管理角色新手引导*/}
        {/*帮助*/}
        <div
          style={{
            display:
              roleId == '1005' && beginnerGuideNum == 6 ? 'block' : 'none',
          }}
          className={styles.helpBeginnerGuide}
        >
          <img
            className={styles.helpBannerImg}
            src={HelpIcon}
            style={{ right: currentLanguage == 'zh-CN' ? '242px' : '257px' }}
          />
          <img
            className={styleCss.settingVerticalBeginnerGuideIcon}
            src={BeginnerGuidanceArrow}
            style={{ right: currentLanguage == 'zh-CN' ? '215px' : '230px' }}
          />
          <div
            className={styleCss.helpDetailContent}
            style={{ right: currentLanguage == 'zh-CN' ? '130px' : '160px' }}
          >
            <p className={styleCss.beginnerGuideContent}>
              <FormattedMessage
                id="connect.beginner.guide.help.content"
                defaultMessage="点击这里可以打开当前页面的教程哦～"
              />
            </p>
            {/*<div className={styles.beginnerGuideFooter}>*/}
            {/*  <Button*/}
            {/*    className={styles.nextBtn}*/}
            {/*    onClick={this.handleComplete}*/}
            {/*  >*/}
            {/*    <FormattedMessage*/}
            {/*      id="connect.beginner.guide.know.btn"*/}
            {/*      defaultMessage="知道了"*/}
            {/*    />*/}
            {/*  </Button>*/}
            {/*</div>*/}
          </div>
        </div>
        <div
          style={{
            display:
              roleId == '1005' &&
              beginnerGuideNum !== 0 &&
              beginnerGuideNum !== 6
                ? 'block'
                : 'none',
          }}
          className={styles.customerServiceSupervisorBeginnerGuide}
        >
          {/*用户头像*/}
          <div
            style={{ display: beginnerGuideNum == 5 ? 'block' : 'none' }}
            className={styles.userBeginnerGuide}
          >
            <img
              className={styles.userBannerImg}
              src={UserBanner}
              style={{ right: currentLanguage == 'zh-CN' ? '190px' : '205px' }}
            />
            <img
              className={styles.settingVerticalBeginnerGuideIcon}
              src={verticalBeginnerGuide}
              style={{ right: currentLanguage == 'zh-CN' ? '200px' : '215px' }}
            />
            <div
              className={styles.userDetailContent}
              style={{ right: currentLanguage == 'zh-CN' ? '135px' : '165px' }}
            >
              <p className={styles.beginnerGuideTitle}>
                <FormattedMessage
                  id="connect.beginner.guide.title.1"
                  defaultMessage="用户"
                />
              </p>
              <p className={styles.beginnerGuideContent}>
                <FormattedMessage
                  id="connect.beginner.guide.user.content"
                  defaultMessage="您可以在这里维护自己的个人信息，设置语言及时区"
                />
              </p>
              <div className={styles.beginnerGuideFooter}>
                <Checkbox onChange={this.handleNoPrompt} checked={promptCheck}>
                  <FormattedMessage
                    id="connect.beginner.guide.not.prompt"
                    defaultMessage="不再提示"
                  />
                </Checkbox>
                <Button
                  className={styles.nextBtn}
                  onClick={this.handleSkipTutorial}
                >
                  <FormattedMessage
                    id="connect.beginner.guide.complete.btn"
                    defaultMessage="完成"
                  />
                </Button>
                <span
                  onClick={this.handleSkipTutorial}
                  className={styles.jumpBeginnerGuide}
                >
                  <FormattedMessage
                    id="connect.beginner.guide.skip.tutorial"
                    defaultMessage="跳过教程"
                  />
                </span>
              </div>
            </div>
          </div>
          {/*工单中心*/}
          <div
            style={{
              display: !collapsed && beginnerGuideNum == 2 ? 'block' : 'none',
            }}
            className={styles.workOrderCenterBeginnerGuide}
          >
            <div className={styles.workOrderCenterIcon}>
              <p>
                <FormattedMessage
                  id="connect.beginner.guide.work.order.center"
                  defaultMessage="工单中心"
                />
              </p>
            </div>
            <img
              className={styles.transverseVerticalBeginnerGuideIcon}
              src={TransverseVerticalBeginnerGuideIcon}
            />
            <div className={styles.workOrderCenterContent}>
              <p className={styles.beginnerGuideTitle}>
                <FormattedMessage
                  id="connect.beginner.guide.work.order.center"
                  defaultMessage="工单中心"
                />
              </p>
              <p className={styles.beginnerGuideContent}>
                <FormattedMessage
                  id="connect.beginner.guide.work.order.center.content1"
                  defaultMessage="您可以在这里浏览您组内所有工单的信息，查看超出SLA标准的工单，并进行派单、催单等操作"
                />
              </p>
              <div className={styles.beginnerGuideFooter}>
                <Checkbox onChange={this.handleNoPrompt} checked={promptCheck}>
                  <FormattedMessage
                    id="connect.beginner.guide.not.prompt"
                    defaultMessage="不再提示"
                  />
                </Checkbox>
                <Button className={styles.nextBtn} onClick={this.handleNext}>
                  <FormattedMessage
                    id="connect.beginner.guide.next.btn"
                    defaultMessage="下一步"
                  />
                </Button>
                <span
                  onClick={this.handleSkipTutorial}
                  className={styles.jumpBeginnerGuide}
                >
                  <FormattedMessage
                    id="connect.beginner.guide.skip.tutorial"
                    defaultMessage="跳过教程"
                  />
                </span>
              </div>
            </div>
          </div>
          {/*客户中心*/}
          <div
            style={{
              display: !collapsed && beginnerGuideNum == 3 ? 'block' : 'none',
            }}
            className={styles.customerCenterBeginnerGuide}
          >
            <div className={styles.customerCenterIcon}>
              <p>
                <FormattedMessage
                  id="connect.beginner.guide.customer.center"
                  defaultMessage="客户中心"
                />
              </p>
            </div>
            <img
              className={styles.transverseVerticalBeginnerGuideIcon}
              src={TransverseVerticalBeginnerGuideIcon}
            />
            <div className={styles.customerCenterContent}>
              <p className={styles.beginnerGuideTitle}>
                <FormattedMessage
                  id="connect.beginner.guide.customer.center"
                  defaultMessage="客户中心"
                />
              </p>
              <p className={styles.beginnerGuideContent}>
                <FormattedMessage
                  id="connect.beginner.guide.customer.center.content"
                  defaultMessage="在这里查看您公司所有的客户资料信息"
                />
              </p>
              <div className={styles.beginnerGuideFooter}>
                <Checkbox onChange={this.handleNoPrompt} checked={promptCheck}>
                  <FormattedMessage
                    id="connect.beginner.guide.not.prompt"
                    defaultMessage="不再提示"
                  />
                </Checkbox>
                <Button className={styles.nextBtn} onClick={this.handleNext}>
                  <FormattedMessage
                    id="connect.beginner.guide.next.btn"
                    defaultMessage="下一步"
                  />
                </Button>
                <span
                  onClick={this.handleSkipTutorial}
                  className={styles.jumpBeginnerGuide}
                >
                  <FormattedMessage
                    id="connect.beginner.guide.skip.tutorial"
                    defaultMessage="跳过教程"
                  />
                </span>
              </div>
            </div>
          </div>
          {/*客服管理*/}
          <div
            style={{
              display: !collapsed && beginnerGuideNum == 1 ? 'block' : 'none',
            }}
            className={styles.customerServiceManagingBeginnerGuide}
          >
            <div className={styles.customerServiceManagingIcon}>
              <p>
                <FormattedMessage
                  id="connect.beginner.guide.customer.service.management"
                  defaultMessage="客服管理"
                />
              </p>
            </div>
            <img
              className={styles.transverseVerticalBeginnerGuideIcon}
              src={TransverseVerticalBeginnerGuideIcon}
            />
            <div className={styles.customerServiceManagingContent}>
              <p className={styles.beginnerGuideTitle}>
                <FormattedMessage
                  id="connect.beginner.guide.customer.service.management"
                  defaultMessage="客服管理"
                />
              </p>
              <p className={styles.beginnerGuideContent}>
                <FormattedMessage
                  id="connect.beginner.guide.customer.service.management.content"
                  defaultMessage="您可以在这里维护您组内的客服人员"
                />
              </p>
              <div className={styles.beginnerGuideFooter}>
                <Checkbox onChange={this.handleNoPrompt} checked={promptCheck}>
                  <FormattedMessage
                    id="connect.beginner.guide.not.prompt"
                    defaultMessage="不再提示"
                  />
                </Checkbox>
                <Button className={styles.nextBtn} onClick={this.handleNext}>
                  <FormattedMessage
                    id="connect.beginner.guide.next.btn"
                    defaultMessage="下一步"
                  />
                </Button>
                <span
                  onClick={this.handleSkipTutorial}
                  className={styles.jumpBeginnerGuide}
                >
                  <FormattedMessage
                    id="connect.beginner.guide.skip.tutorial"
                    defaultMessage="跳过教程"
                  />
                </span>
              </div>
            </div>
          </div>
          {/*知识库*/}
          <div
            style={{
              display: !collapsed && beginnerGuideNum == 4 ? 'block' : 'none',
            }}
            className={styles.knowledgeBeginnerGuide}
          >
            <div className={styles.knowledgeCenterIcon}>
              <p>
                <FormattedMessage
                  id="connect.beginner.guide.knowledge"
                  defaultMessage="知识库"
                />
              </p>
            </div>
            <img
              className={styles.transverseVerticalBeginnerGuideIcon}
              src={TransverseVerticalBeginnerGuideIcon}
            />
            <div className={styles.knowledgeContent}>
              <p className={styles.beginnerGuideTitle}>
                <FormattedMessage
                  id="connect.beginner.guide.knowledge"
                  defaultMessage="知识库"
                />
              </p>
              <p className={styles.beginnerGuideContent}>
                <FormattedMessage
                  id="connect.beginner.guide.knowledge.content"
                  defaultMessage="您可以在这里维护公司内部沉淀的知识库。支持问答知识库及文档知识库"
                />
              </p>
              <div className={styles.beginnerGuideFooter}>
                <Checkbox onChange={this.handleNoPrompt} checked={promptCheck}>
                  <FormattedMessage
                    id="connect.beginner.guide.not.prompt"
                    defaultMessage="不再提示"
                  />
                </Checkbox>
                <Button className={styles.nextBtn} onClick={this.handleNext}>
                  <FormattedMessage
                    id="connect.beginner.guide.next.btn"
                    defaultMessage="下一步"
                  />
                </Button>
                <span
                  onClick={this.handleSkipTutorial}
                  className={styles.jumpBeginnerGuide}
                >
                  <FormattedMessage
                    id="connect.beginner.guide.skip.tutorial"
                    defaultMessage="跳过教程"
                  />
                </span>
              </div>
            </div>
          </div>

          {/*客服管理--菜单收缩*/}
          <div
            style={{
              display: collapsed && beginnerGuideNum == 1 ? 'block' : 'none',
            }}
            className={styles.collapsedCustomerServiceManagingBeginnerGuide}
          >
            <div className={styles.collapsedCustomerServiceManagingIcon}>
              <p>
                <FormattedMessage
                  id="connect.beginner.guide.custom.service"
                  defaultMessage="客服"
                />
              </p>
            </div>
            <img
              className={styles.transverseVerticalBeginnerGuideIcon}
              src={TransverseVerticalBeginnerGuideIcon}
            />
            <div className={styles.collapsedCustomerServiceManagingContent}>
              <p className={styles.beginnerGuideTitle}>
                <FormattedMessage
                  id="connect.beginner.guide.custom.service"
                  defaultMessage="客服"
                />
              </p>
              <p className={styles.beginnerGuideContent}>
                <FormattedMessage
                  id="connect.beginner.guide.customer.service.management.content"
                  defaultMessage="您可以在这里维护您组内的客服人员"
                />
              </p>
              <div className={styles.beginnerGuideFooter}>
                <Checkbox onChange={this.handleNoPrompt} checked={promptCheck}>
                  <FormattedMessage
                    id="connect.beginner.guide.not.prompt"
                    defaultMessage="不再提示"
                  />
                </Checkbox>
                <Button className={styles.nextBtn} onClick={this.handleNext}>
                  <FormattedMessage
                    id="connect.beginner.guide.next.btn"
                    defaultMessage="下一步"
                  />
                </Button>
                <span
                  onClick={this.handleSkipTutorial}
                  className={styles.jumpBeginnerGuide}
                >
                  <FormattedMessage
                    id="connect.beginner.guide.skip.tutorial"
                    defaultMessage="跳过教程"
                  />
                </span>
              </div>
            </div>
          </div>
          {/*工单中心--菜单收缩*/}
          <div
            style={{
              display: collapsed && beginnerGuideNum == 2 ? 'block' : 'none',
            }}
            className={styles.collapsedWorkOrderCenterBeginnerGuide}
          >
            <div className={styles.workOrderCenterIcon}>
              <p>
                <FormattedMessage
                  id="connect.beginner.guide.work.record"
                  defaultMessage="工单"
                />
              </p>
            </div>
            <img
              className={styles.transverseVerticalBeginnerGuideIcon}
              src={TransverseVerticalBeginnerGuideIcon}
            />
            <div className={styles.workOrderCenterContent}>
              <p className={styles.beginnerGuideTitle}>
                <FormattedMessage
                  id="connect.beginner.guide.work.record"
                  defaultMessage="工单"
                />
              </p>
              <p className={styles.beginnerGuideContent}>
                <FormattedMessage
                  id="connect.beginner.guide.work.order.center.content1"
                  defaultMessage="您可以在这里浏览您组内所有工单的信息，查看超出SLA标准的工单，并进行派单、催单等操作"
                />
              </p>
              <div className={styles.beginnerGuideFooter}>
                <Checkbox onChange={this.handleNoPrompt} checked={promptCheck}>
                  <FormattedMessage
                    id="connect.beginner.guide.not.prompt"
                    defaultMessage="不再提示"
                  />
                </Checkbox>
                <Button className={styles.nextBtn} onClick={this.handleNext}>
                  <FormattedMessage
                    id="connect.beginner.guide.next.btn"
                    defaultMessage="下一步"
                  />
                </Button>
                <span
                  onClick={this.handleSkipTutorial}
                  className={styles.jumpBeginnerGuide}
                >
                  <FormattedMessage
                    id="connect.beginner.guide.skip.tutorial"
                    defaultMessage="跳过教程"
                  />
                </span>
              </div>
            </div>
          </div>
          {/*客户中心--菜单收缩*/}
          <div
            style={{
              display: collapsed && beginnerGuideNum == 3 ? 'block' : 'none',
            }}
            className={styles.collapsedCustomerCenterBeginnerGuide}
          >
            <div className={styles.customerCenterIcon}>
              <p>
                <FormattedMessage
                  id="connect.beginner.guide.custom"
                  defaultMessage="客户"
                />
              </p>
            </div>
            <img
              className={styles.transverseVerticalBeginnerGuideIcon}
              src={TransverseVerticalBeginnerGuideIcon}
            />
            <div className={styles.customerCenterContent}>
              <p className={styles.beginnerGuideTitle}>
                <FormattedMessage
                  id="connect.beginner.guide.custom"
                  defaultMessage="客户"
                />
              </p>
              <p className={styles.beginnerGuideContent}>
                <FormattedMessage
                  id="connect.beginner.guide.customer.center.content"
                  defaultMessage="在这里查看您公司所有的客户资料信息"
                />
              </p>
              <div className={styles.beginnerGuideFooter}>
                <Checkbox onChange={this.handleNoPrompt} checked={promptCheck}>
                  <FormattedMessage
                    id="connect.beginner.guide.not.prompt"
                    defaultMessage="不再提示"
                  />
                </Checkbox>
                <Button className={styles.nextBtn} onClick={this.handleNext}>
                  <FormattedMessage
                    id="connect.beginner.guide.next.btn"
                    defaultMessage="下一步"
                  />
                </Button>
                <span
                  onClick={this.handleSkipTutorial}
                  className={styles.jumpBeginnerGuide}
                >
                  <FormattedMessage
                    id="connect.beginner.guide.skip.tutorial"
                    defaultMessage="跳过教程"
                  />
                </span>
              </div>
            </div>
          </div>
          {/*知识库--菜单收缩*/}
          <div
            style={{
              display: collapsed && beginnerGuideNum == 4 ? 'block' : 'none',
            }}
            className={styles.collapsedKnowledgeBeginnerGuide}
          >
            <div className={styles.knowledgeCenterIcon}>
              <p>
                <FormattedMessage
                  id="connect.beginner.guide.knowledge1"
                  defaultMessage="知识库"
                />
              </p>
            </div>
            <img
              className={styles.transverseVerticalBeginnerGuideIcon}
              src={TransverseVerticalBeginnerGuideIcon}
            />
            <div className={styles.knowledgeContent}>
              <p className={styles.beginnerGuideTitle}>
                <FormattedMessage
                  id="connect.beginner.guide.knowledge1"
                  defaultMessage="知识库"
                />
              </p>
              <p className={styles.beginnerGuideContent}>
                <FormattedMessage
                  id="connect.beginner.guide.knowledge.content"
                  defaultMessage="您可以在这里维护公司内部沉淀的知识库。支持问答知识库及文档知识库"
                />
              </p>
              <div className={styles.beginnerGuideFooter}>
                <Checkbox onChange={this.handleNoPrompt} checked={promptCheck}>
                  <FormattedMessage
                    id="connect.beginner.guide.not.prompt"
                    defaultMessage="不再提示"
                  />
                </Checkbox>
                <Button className={styles.nextBtn} onClick={this.handleNext}>
                  <FormattedMessage
                    id="connect.beginner.guide.next.btn"
                    defaultMessage="下一步"
                  />
                </Button>
                <span
                  onClick={this.handleSkipTutorial}
                  className={styles.jumpBeginnerGuide}
                >
                  <FormattedMessage
                    id="connect.beginner.guide.skip.tutorial"
                    defaultMessage="跳过教程"
                  />
                </span>
              </div>
            </div>
          </div>
        </div>

        {/*客服角色新手引导*/}
        {/*帮助*/}
        <div
          style={{
            display:
              roleId == '1003' && beginnerGuideNum == 6 ? 'block' : 'none',
          }}
          className={styles.helpBeginnerGuide}
        >
          <img
            className={styles.helpBannerImg}
            src={HelpIcon}
            style={{ right: currentLanguage == 'zh-CN' ? '242px' : '257px' }}
          />
          <img
            className={styleCss.settingVerticalBeginnerGuideIcon}
            src={BeginnerGuidanceArrow}
            style={{ right: currentLanguage == 'zh-CN' ? '215px' : '230px' }}
          />
          <div
            className={styleCss.helpDetailContent}
            style={{ right: currentLanguage == 'zh-CN' ? '130px' : '160px' }}
          >
            <p className={styleCss.beginnerGuideContent}>
              <FormattedMessage
                id="connect.beginner.guide.help.content"
                defaultMessage="点击这里可以打开当前页面的教程哦～"
              />
            </p>
            {/*<div className={styles.beginnerGuideFooter}>*/}
            {/*  <Button*/}
            {/*    className={styles.nextBtn}*/}
            {/*    onClick={this.handleComplete}*/}
            {/*  >*/}
            {/*    <FormattedMessage*/}
            {/*      id="connect.beginner.guide.know.btn"*/}
            {/*      defaultMessage="知道了"*/}
            {/*    />*/}
            {/*  </Button>*/}
            {/*</div>*/}
          </div>
        </div>
        <div
          style={{
            display:
              roleId == '1003' &&
              beginnerGuideNum !== 0 &&
              beginnerGuideNum !== 6
                ? 'block'
                : 'none',
          }}
          className={styles.customerServiceBeginnerGuide}
        >
          {/*工作台*/}
          <div
            style={{ display: beginnerGuideNum == 1 ? 'block' : 'none' }}
            className={styles.workspaceBeginnerGuide}
          >
            <div
              className={styles.workspaceIcon}
              style={{ right: currentLanguage == 'zh-CN' ? '320px' : '345px' }}
            >
              <p>
                <FormattedMessage
                  id="layout.worktable"
                  defaultMessage="工作台"
                />
              </p>
            </div>
            <img
              className={styles.settingVerticalBeginnerGuideIcon}
              src={verticalBeginnerGuide}
              style={{ right: currentLanguage == 'zh-CN' ? '375px' : '390px' }}
            />
            <div
              className={styles.workspaceContent}
              style={{ right: currentLanguage == 'zh-CN' ? '285px' : '300px' }}
            >
              <p className={styles.beginnerGuideTitle}>
                <FormattedMessage
                  id="layout.worktable"
                  defaultMessage="工作台"
                />
              </p>
              <p className={styles.beginnerGuideContent}>
                <FormattedMessage
                  id="connect.beginner.guide.workspace"
                  defaultMessage="这里是您的主要工作面板，实时接收及回复客户信息都在这里。"
                />
              </p>
              <div className={styles.beginnerGuideFooter}>
                <Checkbox onChange={this.handleNoPrompt} checked={promptCheck}>
                  <FormattedMessage
                    id="connect.beginner.guide.not.prompt"
                    defaultMessage="不再提示"
                  />
                </Checkbox>
                <Button className={styles.nextBtn} onClick={this.handleNext}>
                  <FormattedMessage
                    id="connect.beginner.guide.next.btn"
                    defaultMessage="下一步"
                  />
                </Button>
                <span
                  onClick={this.handleSkipTutorial}
                  className={styles.jumpBeginnerGuide}
                >
                  <FormattedMessage
                    id="connect.beginner.guide.skip.tutorial"
                    defaultMessage="跳过教程"
                  />
                </span>
              </div>
            </div>
          </div>
          {/*用户头像*/}
          <div
            style={{ display: beginnerGuideNum == 5 ? 'block' : 'none' }}
            className={styles.userBeginnerGuide}
          >
            <img
              className={styles.userBannerImg}
              src={UserBanner}
              style={{ right: currentLanguage == 'zh-CN' ? '190px' : '205px' }}
            />
            <img
              className={styles.settingVerticalBeginnerGuideIcon}
              src={verticalBeginnerGuide}
              style={{ right: currentLanguage == 'zh-CN' ? '200px' : '215px' }}
            />
            <div
              className={styles.userDetailContent}
              style={{ right: currentLanguage == 'zh-CN' ? '135px' : '165px' }}
            >
              <p className={styles.beginnerGuideTitle}>
                <FormattedMessage
                  id="connect.beginner.guide.title.1"
                  defaultMessage="用户"
                />
              </p>
              <p className={styles.beginnerGuideContent}>
                <FormattedMessage
                  id="connect.beginner.guide.user.content"
                  defaultMessage="您可以在这里维护自己的个人信息，设置语言及时区"
                />
              </p>
              <div className={styles.beginnerGuideFooter}>
                <Checkbox onChange={this.handleNoPrompt} checked={promptCheck}>
                  <FormattedMessage
                    id="connect.beginner.guide.not.prompt"
                    defaultMessage="不再提示"
                  />
                </Checkbox>
                <Button
                  className={styles.nextBtn}
                  onClick={this.handleSkipTutorial}
                >
                  <FormattedMessage
                    id="connect.beginner.guide.complete.btn"
                    defaultMessage="完成"
                  />
                </Button>
                <span
                  onClick={this.handleSkipTutorial}
                  className={styles.jumpBeginnerGuide}
                >
                  <FormattedMessage
                    id="connect.beginner.guide.skip.tutorial"
                    defaultMessage="跳过教程"
                  />
                </span>
              </div>
            </div>
          </div>

          {/*工单中心*/}
          <div
            style={{
              display: !collapsed && beginnerGuideNum == 2 ? 'block' : 'none',
            }}
            className={styles.workOrderCenterBeginnerGuide}
          >
            <div className={styles.workOrderCenterIcon}>
              <p>
                <FormattedMessage
                  id="connect.beginner.guide.work.order.center"
                  defaultMessage="工单中心"
                />
              </p>
            </div>
            <img
              className={styles.transverseVerticalBeginnerGuideIcon}
              src={TransverseVerticalBeginnerGuideIcon}
            />
            <div className={styles.workOrderCenterContent}>
              <p className={styles.beginnerGuideTitle}>
                <FormattedMessage
                  id="connect.beginner.guide.work.order.center"
                  defaultMessage="工单中心"
                />
              </p>
              <p className={styles.beginnerGuideContent}>
                <FormattedMessage
                  id="connect.beginner.guide.work.order.center.content2"
                  defaultMessage="您可以在这里查看您个人的工单或者组内的工单，也可创建新的工单"
                />
              </p>
              <div className={styles.beginnerGuideFooter}>
                <Checkbox onChange={this.handleNoPrompt} checked={promptCheck}>
                  <FormattedMessage
                    id="connect.beginner.guide.not.prompt"
                    defaultMessage="不再提示"
                  />
                </Checkbox>
                <Button className={styles.nextBtn} onClick={this.handleNext}>
                  <FormattedMessage
                    id="connect.beginner.guide.next.btn"
                    defaultMessage="下一步"
                  />
                </Button>
                <span
                  onClick={this.handleSkipTutorial}
                  className={styles.jumpBeginnerGuide}
                >
                  <FormattedMessage
                    id="connect.beginner.guide.skip.tutorial"
                    defaultMessage="跳过教程"
                  />
                </span>
              </div>
            </div>
          </div>
          {/*客户中心*/}
          <div
            style={{
              display: !collapsed && beginnerGuideNum == 3 ? 'block' : 'none',
            }}
            className={styles.customerCenterBeginnerGuide}
          >
            <div className={styles.customerCenterIcon}>
              <p>
                <FormattedMessage
                  id="connect.beginner.guide.customer.center"
                  defaultMessage="客户中心"
                />
              </p>
            </div>
            <img
              className={styles.transverseVerticalBeginnerGuideIcon}
              src={TransverseVerticalBeginnerGuideIcon}
            />
            <div className={styles.customerCenterContent}>
              <p className={styles.beginnerGuideTitle}>
                <FormattedMessage
                  id="connect.beginner.guide.customer.center"
                  defaultMessage="客户中心"
                />
              </p>
              <p className={styles.beginnerGuideContent}>
                <FormattedMessage
                  id="connect.beginner.guide.customer.center.content1"
                  defaultMessage="您可以在这里查看您公司所有的客户资料信息，并可在这边直接联系客户"
                />
              </p>
              <div className={styles.beginnerGuideFooter}>
                <Checkbox onChange={this.handleNoPrompt} checked={promptCheck}>
                  <FormattedMessage
                    id="connect.beginner.guide.not.prompt"
                    defaultMessage="不再提示"
                  />
                </Checkbox>
                <Button className={styles.nextBtn} onClick={this.handleNext}>
                  <FormattedMessage
                    id="connect.beginner.guide.next.btn"
                    defaultMessage="下一步"
                  />
                </Button>
                <span
                  onClick={this.handleSkipTutorial}
                  className={styles.jumpBeginnerGuide}
                >
                  <FormattedMessage
                    id="connect.beginner.guide.skip.tutorial"
                    defaultMessage="跳过教程"
                  />
                </span>
              </div>
            </div>
          </div>
          {/*知识库*/}
          <div
            style={{
              display: !collapsed && beginnerGuideNum == 4 ? 'block' : 'none',
            }}
            className={styles.knowledgeBeginnerGuide}
          >
            <div className={styles.knowledgeCenterIcon}>
              <p>
                <FormattedMessage
                  id="connect.beginner.guide.knowledge"
                  defaultMessage="知识库"
                />
              </p>
            </div>
            <img
              className={styles.transverseVerticalBeginnerGuideIcon}
              src={TransverseVerticalBeginnerGuideIcon}
            />
            <div className={styles.knowledgeContent}>
              <p className={styles.beginnerGuideTitle}>
                <FormattedMessage
                  id="connect.beginner.guide.knowledge"
                  defaultMessage="知识库"
                />
              </p>
              <p className={styles.beginnerGuideContent}>
                <FormattedMessage
                  id="connect.beginner.guide.knowledge.content1"
                  defaultMessage="您可以在这里搜索并查看公司内部的问答知识库及文档知识库"
                />
              </p>
              <div className={styles.beginnerGuideFooter}>
                <Checkbox onChange={this.handleNoPrompt} checked={promptCheck}>
                  <FormattedMessage
                    id="connect.beginner.guide.not.prompt"
                    defaultMessage="不再提示"
                  />
                </Checkbox>
                <Button className={styles.nextBtn} onClick={this.handleNext}>
                  <FormattedMessage
                    id="connect.beginner.guide.next.btn"
                    defaultMessage="下一步"
                  />
                </Button>
                <span
                  onClick={this.handleSkipTutorial}
                  className={styles.jumpBeginnerGuide}
                >
                  <FormattedMessage
                    id="connect.beginner.guide.skip.tutorial"
                    defaultMessage="跳过教程"
                  />
                </span>
              </div>
            </div>
          </div>

          {/*工单中心--菜单收缩*/}
          <div
            style={{
              display: collapsed && beginnerGuideNum == 2 ? 'block' : 'none',
            }}
            className={styles.collapsedWorkOrderCenterBeginnerGuide}
          >
            <div className={styles.workOrderCenterIcon}>
              <p>
                <FormattedMessage
                  id="connect.beginner.guide.work.record"
                  defaultMessage="工单"
                />
              </p>
            </div>
            <img
              className={styles.transverseVerticalBeginnerGuideIcon}
              src={TransverseVerticalBeginnerGuideIcon}
            />
            <div className={styles.workOrderCenterContent}>
              <p className={styles.beginnerGuideTitle}>
                <FormattedMessage
                  id="connect.beginner.guide.work.record"
                  defaultMessage="工单"
                />
              </p>
              <p className={styles.beginnerGuideContent}>
                <FormattedMessage
                  id="connect.beginner.guide.work.order.center.content2"
                  defaultMessage="您可以在这里查看您个人的工单或者组内的工单，也可创建新的工单"
                />
              </p>
              <div className={styles.beginnerGuideFooter}>
                <Checkbox onChange={this.handleNoPrompt} checked={promptCheck}>
                  <FormattedMessage
                    id="connect.beginner.guide.not.prompt"
                    defaultMessage="不再提示"
                  />
                </Checkbox>
                <Button className={styles.nextBtn} onClick={this.handleNext}>
                  <FormattedMessage
                    id="connect.beginner.guide.next.btn"
                    defaultMessage="下一步"
                  />
                </Button>
                <span
                  onClick={this.handleSkipTutorial}
                  className={styles.jumpBeginnerGuide}
                >
                  <FormattedMessage
                    id="connect.beginner.guide.skip.tutorial"
                    defaultMessage="跳过教程"
                  />
                </span>
              </div>
            </div>
          </div>
          {/*客户中心--菜单收缩*/}
          <div
            style={{
              display: collapsed && beginnerGuideNum == 3 ? 'block' : 'none',
            }}
            className={styles.collapsedCustomerCenterBeginnerGuide}
          >
            <div className={styles.customerCenterIcon}>
              <p>
                <FormattedMessage
                  id="connect.beginner.guide.custom"
                  defaultMessage="客户"
                />
              </p>
            </div>
            <img
              className={styles.transverseVerticalBeginnerGuideIcon}
              src={TransverseVerticalBeginnerGuideIcon}
            />
            <div className={styles.customerCenterContent}>
              <p className={styles.beginnerGuideTitle}>
                <FormattedMessage
                  id="connect.beginner.guide.custom"
                  defaultMessage="客户"
                />
              </p>
              <p className={styles.beginnerGuideContent}>
                <FormattedMessage
                  id="connect.beginner.guide.customer.center.content1"
                  defaultMessage="您可以在这里查看您公司所有的客户资料信息，并可在这边直接联系客户"
                />
              </p>
              <div className={styles.beginnerGuideFooter}>
                <Checkbox onChange={this.handleNoPrompt} checked={promptCheck}>
                  <FormattedMessage
                    id="connect.beginner.guide.not.prompt"
                    defaultMessage="不再提示"
                  />
                </Checkbox>
                <Button className={styles.nextBtn} onClick={this.handleNext}>
                  <FormattedMessage
                    id="connect.beginner.guide.next.btn"
                    defaultMessage="下一步"
                  />
                </Button>
                <span
                  onClick={this.handleSkipTutorial}
                  className={styles.jumpBeginnerGuide}
                >
                  <FormattedMessage
                    id="connect.beginner.guide.skip.tutorial"
                    defaultMessage="跳过教程"
                  />
                </span>
              </div>
            </div>
          </div>
          {/*知识库--菜单收缩*/}
          <div
            style={{
              display: collapsed && beginnerGuideNum == 4 ? 'block' : 'none',
            }}
            className={styles.collapsedKnowledgeBeginnerGuide}
          >
            <div className={styles.knowledgeCenterIcon}>
              <p>
                <FormattedMessage
                  id="connect.beginner.guide.knowledge1"
                  defaultMessage="知识库"
                />
              </p>
            </div>
            <img
              className={styles.transverseVerticalBeginnerGuideIcon}
              src={TransverseVerticalBeginnerGuideIcon}
            />
            <div className={styles.knowledgeContent}>
              <p className={styles.beginnerGuideTitle}>
                <FormattedMessage
                  id="connect.beginner.guide.knowledge1"
                  defaultMessage="知识库"
                />
              </p>
              <p className={styles.beginnerGuideContent}>
                <FormattedMessage
                  id="connect.beginner.guide.knowledge.content1"
                  defaultMessage="您可以在这里搜索并查看公司内部的问答知识库及文档知识库"
                />
              </p>
              <div className={styles.beginnerGuideFooter}>
                <Checkbox onChange={this.handleNoPrompt} checked={promptCheck}>
                  <FormattedMessage
                    id="connect.beginner.guide.not.prompt"
                    defaultMessage="不再提示"
                  />
                </Checkbox>
                <Button className={styles.nextBtn} onClick={this.handleNext}>
                  <FormattedMessage
                    id="connect.beginner.guide.next.btn"
                    defaultMessage="下一步"
                  />
                </Button>
                <span
                  onClick={this.handleSkipTutorial}
                  className={styles.jumpBeginnerGuide}
                >
                  <FormattedMessage
                    id="connect.beginner.guide.skip.tutorial"
                    defaultMessage="跳过教程"
                  />
                </span>
              </div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }
}

export default connect(({ layouts, global, aigc }) => ({
  menuList: layouts.menuList,
  user: layouts.user,
  open: aigc.open,
  allowOnlineCountFlag: layouts.allowOnlineCountFlag,
  reconnect: layouts.reconnect,
  agentStatus: layouts.agentStatus,
  websocketStatus: layouts.websocketStatus,
  authAccess: layouts.auth,
  userAvatar: layouts.userAvatar,
  menuIdList: layouts.menuIdList,
  connectList: layouts.connectList,
  selectedConnect: layouts.selectedConnect,
  showWorkTable: layouts.showWorkTable,
  ...global,
}))(LayoutContent);
