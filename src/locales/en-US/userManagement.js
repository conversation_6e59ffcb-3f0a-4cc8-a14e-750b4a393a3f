export default {
  'user.management.department': 'Department',
  'user.management.department.select': 'Please select a department',
  'user.management.department.input': 'Please enter a department',
  'user.management.role.select': 'Role',
  'user.management.set.leader': 'Set as manager',
  'user.management.connect': 'Contact lines',
  'user.management.connect.add': 'Add contact lines',
  'user.management.connect.add.user': 'Creating users',
  'user.management.connect.add.user.bind': 'Binding existing users',
  'user.management.connect.add.user.bind.no': 'Do not bind user',

  'user.management.connect.select': 'Please select a Connect',
  'user.management.contact.selsect': 'Selecting contact lines：',
  'user.management.contact.selsect.user': 'Selecting binding user：',
  'user.management.contact.selsect.please': 'Please select contact lines',
  'user.management.contact.selsect.user.please': 'Please select binding user',
  'user.management.contact.selsect.connectLines.please':
    'Contact lines cannot be duplicated',
  'user.management.contact.Tooltip.radio3':
    'After logging into the system, you still need to log in to the workbench manually.',
  'user.management.operation.success': 'Operation succeeded',
  'user.management.operation.failure': 'Operation failed',
  'user.management.create.success': 'Added succeeded',
  'user.management.agentAccessChannel.no':
    'The access channel for the agent cannot be empty!',
  'user.management.receiveTicketType.no':
    'The chat ticket method cannot be empty!',
  'user.management.create.error': 'Add failed',
  'user.management.update.success': 'Update succeeded',
  'user.management.update.error': 'Update failed',
  'user.management.btn.add': ' Add',
  'user.management.btn.confirm': 'Confirm',
  'user.management.btn.more': 'More',
  'user.management.btn.update': 'Update',
  'user.management.btn.cancel': 'Cancel',
  'user.management.add.user': 'Add user',
  'user.management.add.dept': 'Creat team',
  'user.management.update.deptModel': 'Update team',
  'user.management.update.user': 'Update user',
  'user.management.forbidden.success': 'Disable succeeded',
  'user.management.enable.success': 'Enable succeeded',
  'user.management.remove.success': 'Delete succeeded',
  'user.management.invite.success': 'Invite succeeded',
  'user.management.operation.submit': 'Submit successfully',
  'user.management.remove': 'Long press and drag',
  'user.management.invite.fail.msg':
    'Please select the department to join in team management, and ensure it is a sub-department',
  'user.management.forbidden.self.error':
    'The currently logged in user cannot disable themselves',
  'user.management.remove.self.error':
    'The currently logged in user cannot delete themselves',
  'user.management.user.name': 'User name',
  'user.management.user.name.placeholder':
    'Please enter a username and press Enter to search',
  'user.management.user.name.placeholder.1': 'Please enter a username',
  'user.management.email.tags.placeholder':
    'Enter info and press Enter to generate tags',
  'user.management.user.Info.placeholder':
    'Please enter user information and press Enter to search',
  'user.management.user.default.placeholder': 'Please enter',
  'user.management.user.parent.placeholder': 'Please select a parent team',
  'user.management.user.deptName.placeholder': 'Please select an team',
  'user.management.user.deptName': 'Group',
  'user.management.user.person.placeholder':
    'Please select the person in charge',
  'user.management.user.type': 'User type',
  'user.management.user.status': 'Status',
  'user.management.operation': 'Action',
  'user.management.operation.forbidden': 'Disable',
  'user.management.operation.enable': 'Enable',
  'user.management.operation.normal': 'Normal',
  'user.management.operation.noApply': 'Pending review',
  'user.management.operation.remove': 'Remove',
  'user.management.operation.setLeader': 'Set as group leader',
  'user.management.operation.applyY': 'Passed',
  'user.management.operation.applyN': 'Rejected',
  'user.management.operation.update': 'Update',
  'user.management.operation.btn.yes': 'Yes',
  'user.management.operation.btn.no': 'No',
  'user.management.management': 'Platform user management',
  'user.management.operation.table.sex.0': 'Female',
  'user.management.operation.table.sex.1': 'Male',
  'user.management.operation.table.sex.2': 'Other',
  'user.management.operation.table.user.1': 'VIP',
  'user.management.operation.table.user.2': 'domestic',
  'user.management.create.user': 'Create user',

  'user.management.update.dept': 'Change team',
  'user.management.batch.confirmation': 'Batch confirm',
  'user.management.batch.confirmation.ids.not.null': 'Select at least one user',
  'user.management.invite': 'Invite',
  'user.management.invite.title': 'Invite user',
  'user.management.invite.apply': 'Start review',
  'user.management.notice.forbidden.user':
    'Do you confirm that you want to disable the current user?',
  'user.management.notice.enable.user':
    'Do you confirm that you want to enable the current user?',
  'user.management.notice.remove.user':
    'Do you confirm that you want to delete the current user?',
  'user.management.radio.user.1': 'Administrator',
  'user.management.radio.user.2': 'Marketing',
  'user.management.radio.user.3': 'Agent',
  'user.management.radio.user.4': 'Operations manager',
  'user.management.radio.user.5': 'Agent manager',
  'user.management.tabs.user.1': 'All',
  'user.management.tabs.user.2': 'Pending review',
  'user.management.tabs.user.3': 'Approved',
  'user.management.tabs.user.4': 'Disabled',
  'user.management.tabs.user.5': 'Rejected',
  'user.management.tree.title': 'Team',
  'user.information.username': 'Name:',
  'user.information.deptName': 'Department:',
  'user.information.phoneNumber': 'Phone:',
  'user.information.email': 'Email:',
  'user.information.workNum': 'Employee ID:',
  'user.information.deptLeader': 'Team leader:',
  'user.information.connectList': 'Contact line:',
  'user.option.edit': 'Edit',
  'user.option.delete': 'Delete',

  'create.team.step.title.1': 'Team basic information',
  'create.team.step.title.2': 'Chat capacity configuration',
  'team.deptName': 'Team name',
  'team.deptId': 'Team code',
  'team.parentDept': 'Parent team',
  'team.userName': 'Team leader',
  'team.userName.set': 'set as team leader',

  'team.deptName.p': 'Please enter the team name',
  'team.deptId.p': 'Please enter the team code',
  'team.parentDept.p': 'Please select the parent team',
  'team.userName.p': 'Please select the team leader',
  'team.step.title.2.tips': 'Maximum active chats per ageat',
  'team.step.title.2.tips.p':
    'Please enter a number between 1 and 500, default is 50',
  'team.step.title.2.tips.p.tips':
    'This configuration determines the number of active contacts that each agent can handle simultaneously.',
  'team.step.title.2.modal': 'Create agent',

  'team.step.title.2.modal.tip.1':
    'Congratulations! You have successfully created ',
  'team.step.title.2.modal.tip.1.num.1': ' your an ',
  'team.step.title.2.modal.tip.1.num.2': ' an ',
  'team.step.title.2.modal.tip.1.1':
    'team. Next, you can create agents in your current team.',
  'team.step.title.2.modal.tip.2':
    'By default, the system will automatically route customer inquiries to the current team; if you want to change the routing mechanism for assigning ageats, please go to',
  'team.step.title.2.modal.tip.3': ' "Intelligent allocation rule" ',
  'team.step.title.2.modal.tip.4': 'make settings',
  'create.user.step.title.1': 'Basic user information',
  'create.user.step.title.2': 'User team information',
  'create.user.step.title.3': 'Agent access channels',
  'create.user.step.title.4': 'Chat to receive ticket',
  'create.user.step.title.5': 'Add telephone contact lines',
  'create.user.step.title.1.label.1': 'Last name:',
  'create.user.step.title.1.label.1.p': 'Please enter your last name',
  'create.user.step.title.1.label.2': 'First name:',
  'create.user.step.title.1.label.2.p': 'Please enter your first name',
  'create.user.step.title.1.label.3': 'Phone number',
  'create.user.step.title.1.label.3.p': 'Please enter your phone number',
  'create.user.step.title.2.label.1': 'Team',
  'create.user.step.title.2.label.1.p': 'Please select an team',
  'create.user.step.title.2.label.2.p': 'Please select a role',
  'create.user.step.title.2.label.3.1': 'Telephone',
  'create.user.step.title.2.label.3.2': 'Chat',
  'create.user.step.title.2.label.4.1': 'Automatic assignment',
  'create.user.step.title.2.label.4.2': 'Claim manually',
  'create.user.step.title.2.label.4.tips':
    'After you enable the permission to manually claim tickets for a specific agent, the agent can choose whether to receive tickets automatically assigned by the system or manually claim tickets from their workspace.',
  'create.management.connect.add': 'Contact line',
  'create.management.connect.auto.generation': 'Automatic generation',

  'user.management.user.validity.period': '用户是否有效',

  'create.user.step.title.6': '选择线路区域',
  'create.user.step.title.6.placeholder': '请选择线路区域',
  'create.user.step.title.7': '北美线路',
  'create.user.step.title.8': '中东线路',
};
