import React from 'react';
export const HotspotIssuesIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="12"
    height="13"
    viewBox="0 0 12 13"
    fill="none"
  >
    <g clip-path="url(#clip0_3537_19966)">
      <path
        d="M7.50892 11.3524C7.59138 11.3081 7.66787 11.2699 7.73718 11.2185C8.35982 10.8349 8.7542 10.2911 8.94064 9.67803C9.26212 8.62396 8.96335 7.46711 8.39448 6.55884C7.86984 5.72706 7.6105 4.75187 7.60333 3.77906C7.15876 4.31924 7.12649 5.06259 6.99264 5.72587C6.85281 6.43455 6.38314 6.81579 5.85611 6.92693C5.62068 6.96876 5.37927 6.96159 5.151 6.90542C4.92274 6.84806 4.70046 6.73094 4.51641 6.57199C4.09096 6.20032 3.84955 5.58723 4.09096 4.79967C4.40288 3.80296 4.24393 2.90067 3.55795 2.12147C3.68463 3.65597 3.16357 4.97654 2.33776 6.25768C1.98521 6.80384 1.74739 7.41333 1.63266 8.02044C1.41037 9.19283 1.6458 10.523 2.71302 11.2089C2.77636 11.2567 2.85284 11.295 2.92216 11.3332C2.82655 10.7644 2.91857 10.1608 3.15759 9.63023C3.36434 9.17251 3.68463 8.77215 4.09096 8.4925C4.40288 8.28934 4.75782 8.14951 5.14503 8.12441C5.39361 8.10171 5.60872 8.28934 5.62785 8.53433C5.63741 8.69328 5.56451 8.83908 5.44978 8.92751C5.38644 8.98129 5.33863 9.03866 5.30398 9.09602C5.22749 9.23943 5.19283 9.39719 5.20239 9.55255C5.24661 10.0772 5.61589 10.2003 6.06047 10.2445C6.74645 10.3162 7.44558 10.5003 7.50892 11.3524ZM8.21402 11.975C7.89613 12.1782 7.53999 12.3407 7.14083 12.4638C7.07032 12.4889 6.98786 12.4925 6.91257 12.4769C6.67714 12.4255 6.51819 12.1913 6.56599 11.9499C6.70582 11.2735 6.47397 11.1934 5.96845 11.1337C5.09484 11.044 4.38973 10.5875 4.31325 9.62784C4.31325 9.58003 4.30727 9.53223 4.30727 9.47845C4.17342 9.63142 4.05989 9.8083 3.97025 10.0019C3.72168 10.5516 3.6906 11.2567 4.03359 11.7778C4.10291 11.8794 4.13876 12.0001 4.11606 12.1339C4.07184 12.3754 3.8364 12.5343 3.595 12.4937C3.06079 12.3957 2.61024 12.208 2.22542 11.963C0.841506 11.0691 0.463857 9.38643 0.749484 7.85791C0.88931 7.13966 1.16896 6.41902 1.58844 5.77367C2.57917 4.22483 2.90662 2.86482 2.47759 1.04708C2.4489 0.922795 2.47759 0.786554 2.55407 0.681386C2.69987 0.481806 2.97952 0.443563 3.17671 0.583389C4.7136 1.70199 5.52746 3.16957 4.94186 5.06259C4.82474 5.45697 4.92274 5.73901 5.10081 5.89437C5.17969 5.96369 5.27529 6.01269 5.38046 6.03778C5.47607 6.07244 5.58124 6.07244 5.67923 6.04735C5.87642 6.01269 6.06047 5.85733 6.11783 5.55258C6.39748 4.14835 6.50504 3.20422 7.90927 2.41307C7.99771 2.35571 8.11244 2.33659 8.22717 2.35332C8.46499 2.39515 8.62752 2.63536 8.58331 2.87796C8.37297 3.97984 8.55104 5.12952 9.15217 6.08917C9.86086 7.22331 10.1979 8.62755 9.79632 9.93617C9.54535 10.7512 9.02071 11.4671 8.21402 11.975Z"
        fill="#AD30E5"
      />
    </g>
    <defs>
      <clipPath id="clip0_3537_19966">
        <rect
          width="12"
          height="12"
          fill="white"
          transform="translate(0 0.5)"
        />
      </clipPath>
    </defs>
  </svg>
);
export const SendFail = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_6355_35700)">
      <path
        d="M7.99995 15.9593C3.58947 15.9593 0 12.3716 0 7.96113C0 3.55065 3.58947 -0.0388184 7.99995 -0.0388184C12.4104 -0.0388184 15.9999 3.55065 15.9999 7.96113C15.9999 12.3716 12.4104 15.9593 7.99995 15.9593ZM7.99995 0.791179C4.04758 0.791179 0.829997 4.00697 0.829997 7.96113C0.829997 11.9153 4.04758 15.1311 7.99995 15.1311C11.9523 15.1311 15.1699 11.9153 15.1699 7.96113C15.1699 4.00697 11.9523 0.791179 7.99995 0.791179Z"
        fill="#F22417"
      />
      <path
        d="M8.0002 10.0055C7.77204 10.0055 7.58521 9.82044 7.58521 9.59049V3.90267C7.58521 3.67451 7.77025 3.48767 8.0002 3.48767C8.23016 3.48767 8.4152 3.67271 8.4152 3.90267V9.59049C8.4152 9.81865 8.22836 10.0055 8.0002 10.0055Z"
        fill="#F22417"
      />
      <path
        d="M7.28857 11.308C7.28857 11.4967 7.36353 11.6776 7.49695 11.811C7.63036 11.9445 7.81132 12.0194 8 12.0194C8.18868 12.0194 8.36964 11.9445 8.50305 11.811C8.63647 11.6776 8.71143 11.4967 8.71143 11.308C8.71143 11.1193 8.63647 10.9383 8.50305 10.8049C8.36964 10.6715 8.18868 10.5966 8 10.5966C7.81132 10.5966 7.63036 10.6715 7.49695 10.8049C7.36353 10.9383 7.28857 11.1193 7.28857 11.308Z"
        fill="#F22417"
      />
    </g>
    <defs>
      <clipPath id="clip0_6355_35700">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
export const TransFerIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
  >
    <g clip-path="url(#clip0_11215_45484)">
      <path
        d="M4.25 8L4.79688 6.75M4.79688 6.75L6 4L7.20312 6.75M4.79688 6.75H7.20312M7.75 8L7.20312 6.75"
        stroke="#3463FC"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M10.8 5C10.3367 2.71775 8.31895 1 5.89997 1C3.48102 1 1.46327 2.71775 1 5L2.5 4.5"
        stroke="#3463FC"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M1 7C1.46327 9.28225 3.48102 11 5.89997 11C8.31895 11 10.3367 9.28225 10.8 7L9.5 7.5"
        stroke="#3463FC"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_11215_45484">
        <rect width="12" height="12" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const BeautifyIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
  >
    <path
      d="M6.00048 1.32007C8.84208 1.32007 11.1605 3.40663 11.1605 6.00007C11.1605 8.59351 8.84208 10.6801 6.00048 10.6801H1.20048C0.854876 10.6801 0.707996 10.2394 0.984476 10.0321C1.46112 9.67495 1.78272 9.37927 1.94448 9.15703L1.97808 9.10855C2.00736 9.06247 2.02608 9.02551 2.03616 8.99575L2.05632 9.01783L1.94976 8.89975C1.84199 8.77614 1.74073 8.64701 1.6464 8.51287C1.1232 7.76887 0.839996 6.90343 0.839996 6.00007C0.839996 3.40663 3.1584 1.32007 6 1.32007H6.00048ZM6.00048 2.04007C3.54048 2.04007 1.56048 3.82183 1.56048 6.00007C1.56048 6.75271 1.79616 7.47367 2.23536 8.09911C2.30592 8.19991 2.38176 8.29735 2.4624 8.39191L2.62896 8.57959C2.91936 8.93767 2.78256 9.35239 2.29632 9.85639L2.1936 9.95959L6.00048 9.96007C8.46096 9.96007 10.4405 8.17831 10.4405 6.00007C10.4405 3.82183 8.46096 2.04007 6.00048 2.04007ZM7.68 6.36007C7.77121 6.3601 7.85901 6.39475 7.92566 6.45702C7.9923 6.51928 8.03283 6.60453 8.03905 6.69553C8.04527 6.78653 8.01671 6.8765 7.95915 6.94725C7.90159 7.01801 7.81932 7.06428 7.72896 7.07671L7.68 7.08007H4.32C4.22879 7.08004 4.14098 7.04539 4.07434 6.98312C4.00769 6.92085 3.96716 6.83561 3.96094 6.74461C3.95473 6.65361 3.98328 6.56364 4.04084 6.49288C4.0984 6.42213 4.18068 6.37586 4.27104 6.36343L4.32 6.36007H7.68ZM7.68 4.44007C7.77121 4.4401 7.85901 4.47475 7.92566 4.53702C7.9923 4.59928 8.03283 4.68453 8.03905 4.77553C8.04527 4.86653 8.01671 4.9565 7.95915 5.02725C7.90159 5.09801 7.81932 5.14428 7.72896 5.15671L7.68 5.16007H4.32C4.22879 5.16004 4.14098 5.12539 4.07434 5.06312C4.00769 5.00085 3.96716 4.91561 3.96094 4.82461C3.95473 4.73361 3.98328 4.64364 4.04084 4.57288C4.0984 4.50213 4.18068 4.45586 4.27104 4.44343L4.32 4.44007H7.68Z"
      fill="#3463FC"
    />
  </svg>
);

export const BeautifyIconChecked = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
  >
    <path
      d="M6.00045 1.32007C8.84204 1.32007 11.1604 3.40663 11.1604 6.00007C11.1604 8.59351 8.84204 10.6801 6.00045 10.6801H1.20045C0.854846 10.6801 0.707966 10.2394 0.984446 10.0321C1.46109 9.67495 1.78269 9.37927 1.94445 9.15703L1.97805 9.10855C2.00733 9.06247 2.02605 9.02551 2.03613 8.99575L2.05629 9.01783L1.94973 8.89975C1.84196 8.77614 1.7407 8.64701 1.64637 8.51287C1.12317 7.76887 0.839966 6.90343 0.839966 6.00007C0.839966 3.40663 3.15837 1.32007 5.99997 1.32007H6.00045ZM6.00045 2.04007C3.54045 2.04007 1.56045 3.82183 1.56045 6.00007C1.56045 6.75271 1.79613 7.47367 2.23533 8.09911C2.30589 8.19991 2.38173 8.29735 2.46237 8.39191L2.62893 8.57959C2.91933 8.93767 2.78253 9.35239 2.29629 9.85639L2.19357 9.95959L6.00045 9.96007C8.46093 9.96007 10.4404 8.17831 10.4404 6.00007C10.4404 3.82183 8.46093 2.04007 6.00045 2.04007ZM7.67997 6.36007C7.77118 6.3601 7.85898 6.39475 7.92563 6.45702C7.99227 6.51928 8.0328 6.60453 8.03902 6.69553C8.04523 6.78653 8.01668 6.8765 7.95912 6.94725C7.90156 7.01801 7.81928 7.06428 7.72893 7.07671L7.67997 7.08007H4.31997C4.22875 7.08004 4.14095 7.04539 4.07431 6.98312C4.00766 6.92085 3.96713 6.83561 3.96091 6.74461C3.9547 6.65361 3.98325 6.56364 4.04081 6.49288C4.09837 6.42213 4.18065 6.37586 4.27101 6.36343L4.31997 6.36007H7.67997ZM7.67997 4.44007C7.77118 4.4401 7.85898 4.47475 7.92563 4.53702C7.99227 4.59928 8.0328 4.68453 8.03902 4.77553C8.04523 4.86653 8.01668 4.9565 7.95912 5.02725C7.90156 5.09801 7.81928 5.14428 7.72893 5.15671L7.67997 5.16007H4.31997C4.22875 5.16004 4.14095 5.12539 4.07431 5.06312C4.00766 5.00085 3.96713 4.91561 3.96091 4.82461C3.9547 4.73361 3.98325 4.64364 4.04081 4.57288C4.09837 4.50213 4.18065 4.45586 4.27101 4.44343L4.31997 4.44007H7.67997Z"
      fill="white"
    />
  </svg>
);

export const CollectIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
  >
    <path
      d="M11.2118 4.92039C11.1504 4.73053 10.9871 4.59162 10.7895 4.56301L7.80206 4.03811L6.46608 1.34939C6.37884 1.1707 6.19664 1.05762 5.99703 1.05762C5.79739 1.05762 5.61591 1.17069 5.52657 1.34939L4.19127 4.03811L1.20451 4.58115C1.00696 4.60978 0.842952 4.74868 0.782213 4.93853C0.720095 5.12769 0.771744 5.3364 0.914843 5.4753L3.07587 7.4723L2.51119 10.3746C2.47768 10.5707 2.55725 10.7711 2.7199 10.8869C2.87511 11.0422 3.19524 10.9679 3.27131 10.9281L5.99703 9.59633L8.7053 10.9281C8.78137 10.9679 9.08225 11.0614 9.2567 10.8869C9.41795 10.7711 9.5003 10.5707 9.46611 10.3746L8.91888 7.4723L11.0806 5.45645C11.223 5.31826 11.2739 5.10955 11.2118 4.92039Z"
      fill="#3463FC"
    />
  </svg>
);
export const CollectIconChecked = () => (
  <svg
    width="13"
    height="12"
    viewBox="0 0 13 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.9042 4.92063C11.8428 4.73078 11.6795 4.59187 11.482 4.56325L8.49449 4.03836L7.15851 1.34964C7.07127 1.17094 6.88907 1.05786 6.68946 1.05786C6.48982 1.05786 6.30834 1.17093 6.219 1.34964L4.8837 4.03836L1.89694 4.5814C1.69939 4.61002 1.53538 4.74892 1.47464 4.93878C1.41252 5.12794 1.46417 5.33664 1.60727 5.47555L3.7683 7.47254L3.20362 10.3748C3.17011 10.571 3.24968 10.7713 3.41232 10.8872C3.56753 11.0424 3.88767 10.9681 3.96373 10.9284L6.68946 9.59658L9.39773 10.9284C9.47379 10.9681 9.77468 11.0616 9.94913 10.8872C10.1104 10.7713 10.1927 10.571 10.1585 10.3748L9.61131 7.47254L11.773 5.45669C11.9154 5.3185 11.9664 5.10979 11.9042 4.92063Z"
      fill="white"
    />
  </svg>
);
export const AiIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <rect
      x="7.65799"
      y="2.67379"
      width="0.472509"
      height="3.30756"
      fill="white"
      stroke="#020272"
      stroke-width="0.214777"
    />
    <circle
      cx="7.86826"
      cy="2.40445"
      r="0.720895"
      fill="white"
      stroke="#020272"
      stroke-width="0.214777"
    />
    <g filter="url(#filter0_i_11215_45509)">
      <ellipse
        cx="2.13832"
        cy="8.81785"
        rx="1.13832"
        ry="1.6323"
        fill="white"
      />
    </g>
    <path
      d="M3.16924 8.81785C3.16924 9.24935 3.0471 9.63538 2.85514 9.91063C2.66293 10.1863 2.40781 10.3428 2.13832 10.3428C1.86882 10.3428 1.6137 10.1863 1.42149 9.91063C1.22953 9.63538 1.10739 9.24935 1.10739 8.81785C1.10739 8.38635 1.22953 8.00032 1.42149 7.72506C1.6137 7.44944 1.86882 7.29294 2.13832 7.29294C2.40781 7.29294 2.66293 7.44944 2.85514 7.72506C3.0471 8.00032 3.16924 8.38635 3.16924 8.81785Z"
      stroke="#030178"
      stroke-width="0.214777"
    />
    <g filter="url(#filter1_i_11215_45509)">
      <ellipse
        cx="13.6504"
        cy="8.81785"
        rx="1.13832"
        ry="1.6323"
        fill="white"
      />
    </g>
    <path
      d="M14.6813 8.81785C14.6813 9.24935 14.5591 9.63538 14.3672 9.91063C14.175 10.1863 13.9199 10.3428 13.6504 10.3428C13.3809 10.3428 13.1257 10.1863 12.9335 9.91063C12.7416 9.63538 12.6194 9.24935 12.6194 8.81785C12.6194 8.38635 12.7416 8.00032 12.9335 7.72506C13.1257 7.44944 13.3809 7.29294 13.6504 7.29294C13.9199 7.29294 14.175 7.44944 14.3672 7.72506C14.5591 8.00032 14.6813 8.38635 14.6813 8.81785Z"
      stroke="#030178"
      stroke-width="0.214777"
    />
    <g filter="url(#filter2_i_11215_45509)">
      <ellipse
        cx="7.93731"
        cy="8.66743"
        rx="5.82045"
        ry="4.83247"
        fill="white"
      />
    </g>
    <path
      d="M13.6504 8.66744C13.6504 11.2592 11.1121 13.3925 7.93731 13.3925C4.76251 13.3925 2.22425 11.2592 2.22425 8.66744C2.22425 6.07568 4.76251 3.94235 7.93731 3.94235C11.1121 3.94235 13.6504 6.07568 13.6504 8.66744Z"
      stroke="#030178"
      stroke-width="0.214777"
    />
    <g filter="url(#filter3_i_11215_45509)">
      <path
        d="M12.512 9.1081C12.512 11.1903 10.4639 10.6467 7.93731 10.6467C5.41075 10.6467 3.36256 11.1903 3.36256 9.1081C3.36256 7.02587 5.41075 5.33789 7.93731 5.33789C10.4639 5.33789 12.512 7.02587 12.512 9.1081Z"
        fill="#020272"
      />
    </g>
    <path
      d="M12.4047 9.1081C12.4047 9.61139 12.2812 9.93726 12.075 10.1527C11.867 10.3699 11.5593 10.4915 11.1562 10.552C10.753 10.6126 10.269 10.6102 9.72162 10.5918C9.57246 10.5868 9.41865 10.5806 9.26087 10.5742C8.84308 10.5573 8.39753 10.5393 7.93731 10.5393C7.47708 10.5393 7.03154 10.5573 6.61374 10.5742C6.45597 10.5806 6.30215 10.5868 6.153 10.5918C5.60559 10.6102 5.12159 10.6126 4.71843 10.552C4.31527 10.4915 4.00759 10.3699 3.79965 10.1527C3.59344 9.93726 3.46995 9.61139 3.46995 9.1081C3.46995 7.10368 5.44969 5.44528 7.93731 5.44528C10.4249 5.44528 12.4047 7.10368 12.4047 9.1081Z"
      stroke="#030178"
      stroke-width="0.214777"
    />
    <g filter="url(#filter4_d_11215_45509)">
      <path
        d="M6.60635 8.93366C6.72904 8.95511 6.84888 8.87202 6.83932 8.74784C6.83502 8.69197 6.8255 8.63646 6.81082 8.58204C6.77646 8.45474 6.71461 8.33652 6.62963 8.23571C6.54464 8.13489 6.43859 8.05393 6.31894 7.99853C6.19929 7.94313 6.06895 7.91463 5.9371 7.91504C5.80524 7.91546 5.67508 7.94477 5.55578 8.00092C5.43648 8.05707 5.33094 8.13869 5.24659 8.24003C5.16224 8.34138 5.10113 8.45998 5.06757 8.5875C5.05322 8.642 5.04406 8.69757 5.0401 8.75347C5.03132 8.87771 5.15168 8.96005 5.27423 8.93782L5.30229 8.93273C5.40934 8.91332 5.47605 8.80751 5.50374 8.7023C5.52052 8.63854 5.55108 8.57924 5.59325 8.52857C5.63543 8.4779 5.6882 8.43708 5.74785 8.40901C5.8075 8.38094 5.87258 8.36628 5.93851 8.36607C6.00443 8.36587 6.06961 8.38011 6.12943 8.40782C6.18926 8.43552 6.24228 8.476 6.28477 8.5264C6.32726 8.57681 6.35819 8.63592 6.37537 8.69957C6.40372 8.80461 6.47108 8.91 6.57825 8.92874L6.60635 8.93366Z"
        fill="#01E3FB"
      />
      <path
        d="M10.5582 8.93366C10.6809 8.95511 10.8008 8.87202 10.7912 8.74784C10.7869 8.69197 10.7774 8.63646 10.7627 8.58204C10.7283 8.45474 10.6665 8.33652 10.5815 8.23571C10.4965 8.13489 10.3905 8.05393 10.2708 7.99853C10.1512 7.94313 10.0208 7.91463 9.88899 7.91504C9.75713 7.91546 9.62697 7.94477 9.50767 8.00092C9.38837 8.05707 9.28283 8.13869 9.19848 8.24003C9.11413 8.34138 9.05302 8.45998 9.01946 8.5875C9.00511 8.642 8.99595 8.69757 8.99199 8.75347C8.98321 8.87771 9.10357 8.96005 9.22612 8.93782L9.25418 8.93273C9.36123 8.91332 9.42794 8.80751 9.45563 8.7023C9.47241 8.63854 9.50297 8.57924 9.54514 8.52857C9.58732 8.4779 9.64009 8.43708 9.69974 8.40901C9.75939 8.38094 9.82447 8.36628 9.8904 8.36607C9.95632 8.36587 10.0215 8.38011 10.0813 8.40782C10.1411 8.43552 10.1942 8.476 10.2367 8.5264C10.2792 8.57681 10.3101 8.63592 10.3273 8.69957C10.3556 8.80461 10.423 8.91 10.5301 8.92874L10.5582 8.93366Z"
        fill="#01E3FB"
      />
    </g>
    <defs>
      <filter
        id="filter0_i_11215_45509"
        x="1"
        y="7.09964"
        width="2.27663"
        height="3.35056"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="-1.11684" />
        <feGaussianBlur stdDeviation="0.0429553" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.592157 0 0 0 0 0.588235 0 0 0 0 0.682353 0 0 0 1 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_11215_45509"
        />
      </filter>
      <filter
        id="filter1_i_11215_45509"
        x="12.512"
        y="7.09964"
        width="2.27663"
        height="3.35056"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="-1.11684" />
        <feGaussianBlur stdDeviation="0.0429553" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.592157 0 0 0 0 0.588235 0 0 0 0 0.682353 0 0 0 1 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_11215_45509"
        />
      </filter>
      <filter
        id="filter2_i_11215_45509"
        x="2.11686"
        y="3.74905"
        width="11.6409"
        height="9.75095"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="-1.11684" />
        <feGaussianBlur stdDeviation="0.0429553" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.592157 0 0 0 0 0.588235 0 0 0 0 0.682353 0 0 0 1 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_11215_45509"
        />
      </filter>
      <filter
        id="filter3_i_11215_45509"
        x="3.36256"
        y="5.33789"
        width="9.14948"
        height="5.41551"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="0.773196" />
        <feGaussianBlur stdDeviation="0.0214777" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.501961 0 0 0 0 0.517647 0 0 0 0 0.752941 0 0 0 1 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_11215_45509"
        />
      </filter>
      <filter
        id="filter4_d_11215_45509"
        x="3.75099"
        y="6.66933"
        width="8.32941"
        height="3.60369"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="0.0429553" />
        <feGaussianBlur stdDeviation="0.64433" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.00392157 0 0 0 0 0.890196 0 0 0 0 0.984314 0 0 0 1 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_11215_45509"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_11215_45509"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);

export const LinkIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M17.7831 4.1705C15.7063 1.6055 11.9431 1.20988 9.37751 3.28738L7.40938 4.88113L8.39251 6.0955L10.3606 4.50175C10.8111 4.13696 11.329 3.86448 11.8848 3.69984C12.4406 3.53521 13.0234 3.48167 13.5998 3.54226C14.1763 3.60285 14.7352 3.77639 15.2446 4.05298C15.754 4.32956 16.204 4.70378 16.5688 5.15425C16.9335 5.60473 17.206 6.12264 17.3707 6.67842C17.5353 7.23421 17.5888 7.81697 17.5283 8.39345C17.4677 8.96993 17.2941 9.52883 17.0175 10.0382C16.7409 10.5476 16.3667 10.9976 15.9163 11.3624L13.8075 13.0699L14.7906 14.2843L16.8994 12.5767C17.5094 12.0829 18.0162 11.4736 18.3908 10.7839C18.7654 10.0941 19.0004 9.3373 19.0825 8.55668C19.1646 7.77607 19.0921 6.98693 18.8691 6.23434C18.6462 5.48176 18.2771 4.78046 17.7831 4.1705ZM10.085 16.0849C9.63453 16.4497 9.11662 16.7222 8.56084 16.8868C8.00505 17.0514 7.42229 17.105 6.84581 17.0444C6.26933 16.9838 5.71043 16.8102 5.20102 16.5337C4.69161 16.2571 4.24167 15.8828 3.87688 15.4324C3.51209 14.9819 3.23961 14.464 3.07498 13.9082C2.91035 13.3524 2.8568 12.7697 2.91739 12.1932C2.97798 11.6167 3.15152 11.0578 3.42811 10.5484C3.70469 10.039 4.07891 9.58904 4.52938 9.22425L6.69688 7.46925L5.71376 6.25488L3.54626 8.00988C0.981256 10.0868 0.585631 13.8505 2.66251 16.4155C4.73938 18.9805 8.50313 19.3761 11.0681 17.2993L13.1213 15.6367L12.1381 14.4224L10.085 16.0849Z"
      fill="#999999"
    />
    <path
      d="M13.2063 6.8999L14.2069 8.0999L7.49689 13.6918L6.49689 12.4918L13.2063 6.8999Z"
      fill="#999999"
    />
  </svg>
);

export const SmileIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M17.0469 14.7655H17.0481V14.7583L17.0469 14.7655Z"
      fill="#999999"
    />
    <path
      d="M15.7671 4.23296C12.5872 1.05256 7.41283 1.05256 4.23298 4.23296C1.05258 7.41278 1.05258 12.5872 4.23298 15.767C7.41283 18.9474 12.5872 18.9474 15.7671 15.767C18.9475 12.5872 18.9475 7.41278 15.7671 4.23296ZM14.9393 14.9399C12.2158 17.6634 7.78365 17.6628 5.06044 14.9399C2.33663 12.2158 2.33663 7.78449 5.06044 5.06042C7.78365 2.33718 12.2158 2.33659 14.9393 5.06042C17.6628 7.78363 17.6628 12.2163 14.9393 14.9399Z"
      fill="#999999"
    />
    <path
      d="M7.45556 9.14101C7.93961 9.14101 8.33356 8.74708 8.33356 8.26274C8.33356 7.77781 7.93961 7.3833 7.45556 7.3833C6.97035 7.3833 6.57639 7.77781 6.57639 8.26274C6.57639 8.74708 6.97035 9.14101 7.45556 9.14101Z"
      fill="#999999"
    />
    <path
      d="M12.5133 9.14227C12.9979 9.14227 13.3924 8.74747 13.3924 8.26312C13.3924 7.77821 12.9979 7.38428 12.5133 7.38428C12.0292 7.38428 11.6353 7.77821 11.6353 8.26312C11.6353 8.74747 12.0292 9.14227 12.5133 9.14227Z"
      fill="#999999"
    />
    <path
      d="M12.5439 12.091L12.4959 12.1066C11.7086 12.463 10.8641 12.6443 9.9879 12.6443C9.08275 12.6443 8.22669 12.4612 7.44286 12.1014L7.3955 12.0869C7.36604 12.0788 7.32618 12.0673 7.2765 12.0673C6.98827 12.0673 6.75433 12.3012 6.75433 12.5883C6.75433 12.7246 6.81845 12.8615 6.92415 12.9643L6.9178 12.9961L7.05123 13.0562C7.98699 13.4796 9.00476 13.7026 9.99423 13.7026C11.0022 13.7026 11.9899 13.4854 12.943 13.051C13.1105 12.9591 13.2151 12.783 13.2151 12.5918C13.2151 12.255 12.9107 11.9783 12.5439 12.091Z"
      fill="#999999"
    />
  </svg>
);

export const SendIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M17.4898 8.9061L4.36484 1.4147C4.1436 1.29059 3.88984 1.23673 3.63727 1.26028C3.38469 1.28383 3.14526 1.38366 2.95077 1.54653C2.75628 1.70939 2.61595 1.92757 2.54841 2.17209C2.48088 2.4166 2.48933 2.67588 2.57266 2.91548L4.99453 9.98345C4.99422 9.98604 4.99422 9.98866 4.99453 9.99126C4.9941 9.99384 4.9941 9.99648 4.99453 9.99907L2.57266 17.0827C2.50593 17.2711 2.48538 17.4729 2.51275 17.6709C2.54011 17.869 2.61459 18.0576 2.72992 18.2209C2.84526 18.3842 2.99808 18.5175 3.17558 18.6095C3.35308 18.7016 3.55006 18.7497 3.75 18.7499C3.96693 18.7493 4.18005 18.6928 4.36875 18.5858L17.4867 11.0819C17.6802 10.9735 17.8414 10.8156 17.9537 10.6243C18.066 10.4331 18.1254 10.2154 18.1258 9.99357C18.1262 9.77178 18.0676 9.55388 17.956 9.36222C17.8443 9.17057 17.6837 9.01206 17.4906 8.90298L17.4898 8.9061ZM3.75 17.4999V17.4928L6.10469 10.6249H10.625C10.7908 10.6249 10.9497 10.559 11.0669 10.4418C11.1842 10.3246 11.25 10.1656 11.25 9.99985C11.25 9.83409 11.1842 9.67512 11.0669 9.55791C10.9497 9.4407 10.7908 9.37485 10.625 9.37485H6.11094L3.75469 2.50923L3.75 2.49985L16.875 9.98657L3.75 17.4999Z"
      fill="#999999"
    />
  </svg>
);

export const ReadIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <circle cx="8" cy="8" r="7.5" stroke="#00B900" />
    <path
      d="M4 8L7.03552 11.1273L11.9328 6.27861"
      stroke="#00B900"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

export const LeftUserIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
  >
    <circle cx="12" cy="12" r="12" fill="#24DFB7" />
    <path
      d="M17.7405 7.80005L15.5008 16.2H13.5107L12.079 10.723C12.0031 10.4265 11.9579 10.1174 11.9434 9.79566H11.9218C11.8856 10.1716 11.835 10.4807 11.7699 10.723L10.3057 16.2H8.23964L6 7.80005H7.96307L9.21033 13.494C9.26456 13.7435 9.30252 14.058 9.32421 14.4376H9.36217C9.38025 14.1412 9.43628 13.8176 9.53028 13.4669L11.0921 7.80005H12.9955L14.4163 13.5374C14.4741 13.7652 14.5193 14.0616 14.5518 14.4268H14.579C14.5934 14.1122 14.635 13.805 14.7037 13.5049L15.9238 7.80005H17.7405Z"
      fill="white"
    />
  </svg>
);
export const AgentPhoto = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_1797_22301)">
      <circle cx="12" cy="12" r="12" fill="#3463FC" />
      <g clipPath="url(#clip1_1797_22301)">
        <path
          d="M19.3248 9.97185C18.9719 6.17773 15.8836 3.17773 12.0013 3.17773C8.11891 3.17773 4.94244 6.17773 4.67773 9.97185C3.79538 10.3248 3.17773 11.2954 3.17773 12.3542V14.0307C3.17773 15.4424 4.32479 16.5895 5.73656 16.5895H6.70715C7.06009 16.5895 7.32479 16.3248 7.32479 15.9719V10.413C7.32479 10.0601 7.06009 9.79538 6.70715 9.79538H5.91303C6.3542 6.79538 8.82479 4.50126 12.0013 4.50126C15.0895 4.50126 17.6483 6.79538 18.0895 9.79538H17.2954C16.9424 9.79538 16.6777 10.0601 16.6777 10.413V15.8836C16.6777 16.2366 16.9424 16.5013 17.2954 16.5013H17.5601C17.2954 18.0013 16.1483 18.8836 13.9424 19.0601C13.766 18.7954 13.413 18.6189 13.0601 18.6189H11.8248C11.2071 18.6189 10.766 19.0601 10.766 19.6777C10.766 20.2954 11.2071 20.7366 11.8248 20.7366H13.0601C13.413 20.7366 13.6777 20.6483 13.8542 20.3836C16.8542 20.2071 18.5307 18.8836 18.7954 16.413C19.9424 16.2366 20.8248 15.1777 20.8248 13.9424V12.266C20.8248 11.2954 20.2071 10.3248 19.3248 9.97185ZM6.0895 11.2954V15.0013C6.0895 15.1777 6.00126 15.266 5.82479 15.266H5.73656C5.03068 15.266 4.50126 14.7366 4.50126 14.0307V12.3542C4.50126 11.6483 5.03068 11.1189 5.73656 11.1189H5.82479C6.00126 11.0307 6.0895 11.2071 6.0895 11.2954ZM19.5895 14.0307C19.5895 14.7366 19.0601 15.266 18.3542 15.266H18.1777C18.0013 15.266 17.913 15.1777 17.913 15.0013V11.2954C17.913 11.1189 18.0013 11.0307 18.1777 11.0307H18.266C18.9719 11.0307 19.5013 11.5601 19.5013 12.266V14.0307H19.5895Z"
          fill="white"
        />
        <path
          d="M12.0006 16.6778C10.8536 16.6778 9.88298 16.2367 8.9124 15.3543C8.64769 15.0896 8.64769 14.7367 8.9124 14.472C9.1771 14.2073 9.53004 14.2073 9.79475 14.472C11.2065 15.7955 12.883 15.7955 14.2065 14.472C14.4712 14.2073 14.8242 14.2073 15.0889 14.472C15.3536 14.7367 15.3536 15.0896 15.0889 15.3543C14.1183 16.2367 13.1477 16.6778 12.0006 16.6778Z"
          fill="white"
        />
      </g>
    </g>
    <defs>
      <clipPath id="clip0_1797_22301">
        <rect width="24" height="24" fill="white" />
      </clipPath>
      <clipPath id="clip1_1797_22301">
        <rect
          width="17.6471"
          height="17.6471"
          fill="white"
          transform="translate(3.17676 3.17773)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const Unread = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <circle cx="8" cy="8" r="7.5" stroke="#999999" />
  </svg>
);
export const Read = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <circle cx="8" cy="8" r="7.5" stroke="#00B900" />
    <path
      d="M4 8L7.03552 11.1273L11.9328 6.27861"
      stroke="#00B900"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

export const CheckedIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
  >
    <g clip-path="url(#clip0_11215_45120)">
      <path
        d="M4.25 8L4.79688 6.75M4.79688 6.75L6 4L7.20312 6.75M4.79688 6.75H7.20312M7.75 8L7.20312 6.75"
        stroke="white"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M10.8 5C10.3367 2.71775 8.31895 1 5.89997 1C3.48102 1 1.46327 2.71775 1 5L2.5 4.5"
        stroke="white"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M1 7C1.46327 9.28225 3.48102 11 5.89997 11C8.31895 11 10.3367 9.28225 10.8 7L9.5 7.5"
        stroke="white"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_11215_45120">
        <rect width="12" height="12" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const HistoryLineLeft = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="136"
    height="2"
    viewBox="0 0 136 2"
    fill="none"
  >
    <path
      d="M1 1H135"
      stroke="url(#paint0_linear_11178_50649)"
      stroke-linecap="round"
    />
    <defs>
      <linearGradient
        id="paint0_linear_11178_50649"
        x1="1"
        y1="1.5"
        x2="135"
        y2="1.5"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#999999" stop-opacity="0" />
        <stop offset="1" stop-color="#999999" />
      </linearGradient>
    </defs>
  </svg>
);
export const HistoryLineRight = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="136"
    height="2"
    viewBox="0 0 136 2"
    fill="none"
  >
    <path
      d="M1 1H135"
      stroke="url(#paint0_linear_11178_50651)"
      stroke-linecap="round"
    />
    <defs>
      <linearGradient
        id="paint0_linear_11178_50651"
        x1="1"
        y1="1.5"
        x2="135"
        y2="1.5"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#999999" />
        <stop offset="1" stop-color="#999999" stop-opacity="0" />
      </linearGradient>
    </defs>
  </svg>
);

export const OneMoodIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_836_10931)">
      <path
        d="M19.1037 6.11244C18.1064 3.7262 16.2206 1.82111 13.8446 0.799711C12.6279 0.284519 11.32 0.019043 9.99868 0.019043C8.67736 0.019043 7.36951 0.284519 6.15277 0.799711C4.97711 1.30233 3.91036 2.02868 3.01186 2.93835C1.15241 4.81708 0.112246 7.35548 0.118682 9.9988C0.115018 11.9543 0.684753 13.8679 1.75739 15.5029C2.83003 17.1379 4.3585 18.4226 6.15368 19.1979C7.36955 19.7167 8.67812 19.9831 10 19.9811C11.3002 19.9832 12.5877 19.7257 13.7871 19.2237C14.9864 18.7218 16.0735 17.9854 16.9846 17.0579C17.891 16.1488 18.611 15.0715 19.1032 13.8861C19.6167 12.6546 19.8811 11.3336 19.8812 9.9993C19.8813 8.66504 19.617 7.34399 19.1037 6.11244ZM13.9032 6.16108C14.6705 6.16108 15.2955 6.7938 15.2955 7.57335C15.2955 8.34926 14.6705 8.98198 13.9032 8.98198C13.1337 8.98198 12.506 8.34926 12.506 7.57335C12.506 6.79335 13.1337 6.16108 13.9032 6.16108ZM6.09868 6.13471C6.86868 6.13471 7.49641 6.76744 7.49641 7.54471C7.49641 8.32198 6.86868 8.95471 6.09868 8.95471C5.91421 8.9543 5.73165 8.91736 5.56153 8.84601C5.39141 8.77466 5.23711 8.67032 5.10754 8.53901C4.97797 8.40771 4.87569 8.25204 4.8066 8.08099C4.73752 7.90994 4.703 7.7269 4.70505 7.54244C4.7055 6.76608 5.32914 6.13471 6.09868 6.13471ZM15.1896 13.1206C14.6019 13.9149 13.8379 14.5621 12.9578 15.0111C12.0408 15.4763 11.0271 15.7188 9.99891 15.7188C8.9707 15.7188 7.95698 15.4763 7.04005 15.0111C6.1609 14.5625 5.39798 13.9158 4.81141 13.122C4.70043 12.9721 4.65312 12.7845 4.67976 12.6C4.70641 12.4154 4.80485 12.2489 4.95368 12.1365C5.10404 12.0288 5.29044 11.984 5.47333 12.0115C5.65621 12.039 5.82119 12.1367 5.93323 12.2838C6.86777 13.5515 8.38959 14.3065 10.0005 14.3065C11.6114 14.3065 13.1337 13.5515 14.0696 12.2815C14.1819 12.1352 14.3467 12.0384 14.5292 12.0114C14.7117 11.9844 14.8975 12.0294 15.0473 12.137C15.3537 12.3715 15.4169 12.8083 15.1896 13.1206Z"
        fill="#13C825"
      />
    </g>
    <defs>
      <clipPath id="clip0_836_10931">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const TwoMoodIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_836_10942)">
      <path
        d="M10 20C4.477 20 0 15.523 0 10C0 4.477 4.477 0 10 0C15.523 0 20 4.477 20 10C20 15.523 15.523 20 10 20ZM6 12.5V14H14V12.5H6ZM6 9C6.39782 9 6.77936 8.84196 7.06066 8.56066C7.34196 8.27936 7.5 7.89782 7.5 7.5C7.5 7.10217 7.34196 6.72064 7.06066 6.43934C6.77936 6.15803 6.39782 6 6 6C5.60217 6 5.22064 6.15803 4.93934 6.43934C4.65803 6.72064 4.5 7.10217 4.5 7.5C4.5 7.89782 4.65803 8.27936 4.93934 8.56066C5.22064 8.84196 5.60217 9 6 9ZM14 9C14.3978 9 14.7794 8.84196 15.0607 8.56066C15.342 8.27936 15.5 7.89782 15.5 7.5C15.5 7.10217 15.342 6.72064 15.0607 6.43934C14.7794 6.15803 14.3978 6 14 6C13.6022 6 13.2206 6.15803 12.9393 6.43934C12.658 6.72064 12.5 7.10217 12.5 7.5C12.5 7.89782 12.658 8.27936 12.9393 8.56066C13.2206 8.84196 13.6022 9 14 9Z"
        fill="#3463FC"
      />
    </g>
    <defs>
      <clipPath id="clip0_836_10942">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const ThreeMoodIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_836_10951)">
      <path
        d="M18.987 6.11488C17.9897 3.72864 16.1039 1.82355 13.7279 0.802153C12.5111 0.286961 11.2033 0.0214844 9.88198 0.0214844C8.56066 0.0214844 7.25281 0.286961 6.03607 0.802153C4.86052 1.30472 3.79391 2.03108 2.89562 2.94079C1.03587 4.81937 -0.00448098 7.35783 0.00198307 10.0012C-0.0016807 11.9567 0.568054 13.8703 1.64069 15.5053C2.71333 17.1403 4.2418 18.425 6.03698 19.2003C7.25282 19.7193 8.56139 19.9859 9.88335 19.984C11.1835 19.9857 12.4709 19.7281 13.6702 19.2262C14.8695 18.7242 15.9566 17.9881 16.8679 17.0608C17.7743 16.1517 18.4938 15.0744 18.9865 13.889C19.5 12.6574 19.7645 11.3363 19.7645 10.002C19.7646 8.66764 19.5003 7.34651 18.987 6.11488ZM13.7865 6.16352C14.5538 6.16352 15.1788 6.79624 15.1788 7.57579C15.1788 8.3517 14.5538 8.98443 13.7865 8.98443C13.017 8.98443 12.3893 8.3517 12.3893 7.57579C12.3888 6.79624 13.0165 6.16352 13.7865 6.16352ZM5.98198 6.13715C6.75153 6.13715 7.37971 6.76943 7.37971 7.5467C7.37971 8.32397 6.75198 8.95715 5.98198 8.95715C5.7975 8.95665 5.61494 8.9196 5.44486 8.84813C5.27478 8.77667 5.12055 8.67221 4.99107 8.54079C4.86142 8.40957 4.75907 8.25395 4.68995 8.08293C4.62082 7.91191 4.58629 7.72888 4.58835 7.54443C4.58835 6.76852 5.21244 6.13715 5.98198 6.13715ZM14.9306 15.5881C14.7807 15.6956 14.5948 15.7407 14.4123 15.7137C14.2297 15.6867 14.0648 15.5898 13.9524 15.4435C13.0165 14.1735 11.4947 13.4185 9.8838 13.4185C8.27289 13.4185 6.75062 14.1735 5.81653 15.4412C5.70434 15.5882 5.53939 15.6857 5.35657 15.7132C5.17376 15.7407 4.98742 15.696 4.83698 15.5885C4.68815 15.4762 4.58971 15.3096 4.56306 15.1251C4.53642 14.9405 4.58373 14.7529 4.69471 14.6031C5.28112 13.8093 6.04389 13.1626 6.92289 12.714C7.83982 12.2487 8.85354 12.0062 9.88175 12.0062C10.91 12.0062 11.9237 12.2487 12.8406 12.714C13.721 13.1629 14.4851 13.81 15.0729 14.6044C15.3002 14.9158 15.237 15.3526 14.9306 15.5881Z"
        fill="#F22417"
      />
    </g>
    <defs>
      <clipPath id="clip0_836_10951">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const CircleIcon = () => (
  <svg
    width="4"
    height="4"
    viewBox="0 0 4 4"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="1.75" cy="1.75" r="1.75" fill="#333333" />
  </svg>
);
export const BlueCircle = () => (
  <svg
    width="4"
    height="4"
    viewBox="0 0 4 4"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="1.75" cy="1.75" r="1.75" fill="#3463FC" />
  </svg>
);

export const CheckedBeautify = () => (
  <svg
    width="26"
    height="26"
    viewBox="0 0 26 26"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="0.5"
      y="0.5"
      width="25"
      height="25"
      rx="3.5"
      fill="white"
      fill-opacity="0.5"
    />
    <rect x="0.5" y="0.5" width="25" height="25" rx="3.5" stroke="#FEE315" />
    <path
      d="M20.8177 11.3808C20.7256 11.096 20.4806 10.8876 20.1843 10.8447L15.7031 10.0574L13.6991 6.02429C13.5683 5.75626 13.295 5.58663 12.9956 5.58663C12.6961 5.58663 12.4239 5.75624 12.2899 6.02429L10.2869 10.0574L5.80677 10.8719C5.51045 10.9149 5.26444 11.1232 5.17333 11.408C5.08015 11.6917 5.15762 12.0048 5.37227 12.2132L8.61382 15.2087L7.76679 19.5621C7.71653 19.8563 7.83588 20.1568 8.07985 20.3306C8.31267 20.5634 8.79287 20.4521 8.90697 20.3924L12.9956 18.3947L17.058 20.3924C17.1721 20.4521 17.6234 20.5923 17.8851 20.3306C18.1269 20.1568 18.2505 19.8563 18.1992 19.5621L17.3783 15.2087L20.6209 12.1849C20.8345 11.9776 20.9109 11.6645 20.8177 11.3808Z"
      fill="#FEE315"
    />
  </svg>
);

export const NodataBg = () => (
  <svg
    width="250"
    height="102"
    viewBox="0 0 250 102"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M249.283 102C219.863 65.4938 172.8 44 122.264 44C73.2903 44 29.4395 65.9347 0 100.516L249.283 102Z"
      fill="url(#paint0_linear_14685_40287)"
    />
    <ellipse cx="123.386" cy="79.2807" rx="19.386" ry="2.2807" fill="#798CDD" />
    <g clip-path="url(#clip0_14685_40287)">
      <path
        d="M96.4909 32L90 58.6077L120.291 45.6133L96.4909 32Z"
        fill="#5F7EFF"
      />
      <rect x="85" width="70" height="52" rx="26" fill="#5F7EFF" />
      <g filter="url(#filter0_d_14685_40287)">
        <circle cx="104.5" cy="25.5" r="4.5" fill="#C5CCF0" />
        <circle cx="121.5" cy="25.5" r="4.5" fill="#C5CCF0" />
        <circle cx="138.5" cy="25.5" r="4.5" fill="#C5CCF0" />
      </g>
    </g>
    <g filter="url(#filter1_bdi_14685_40287)">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M145.886 34C153.681 34 160 40.3192 160 48.1143C160 52.1994 158.264 55.8792 155.49 58.4566L157.286 65.8153L148.403 62.0047C147.586 62.1518 146.745 62.2286 145.886 62.2286H136.114C128.319 62.2286 122 55.9094 122 48.1143C122 40.3192 128.319 34 136.114 34H145.886Z"
        fill="#869DFF"
        fill-opacity="0.7"
        shape-rendering="crispEdges"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M145.886 34C153.681 34 160 40.3192 160 48.1143C160 52.1994 158.264 55.8792 155.49 58.4566L157.286 65.8153L148.403 62.0047C147.586 62.1518 146.745 62.2286 145.886 62.2286H136.114C128.319 62.2286 122 55.9094 122 48.1143C122 40.3192 128.319 34 136.114 34H145.886Z"
        fill="url(#paint1_linear_14685_40287)"
        fill-opacity="0.7"
        shape-rendering="crispEdges"
      />
    </g>
    <g filter="url(#filter2_d_14685_40287)">
      <circle
        cx="2.44286"
        cy="2.44286"
        r="2.44286"
        transform="matrix(-1 0 0 1 151.857 45.3999)"
        fill="#C5CCF0"
      />
      <circle
        cx="2.44286"
        cy="2.44286"
        r="2.44286"
        transform="matrix(-1 0 0 1 142.628 45.3999)"
        fill="#C5CCF0"
      />
      <circle
        cx="2.44286"
        cy="2.44286"
        r="2.44286"
        transform="matrix(-1 0 0 1 133.4 45.3999)"
        fill="#C5CCF0"
      />
    </g>
    <path
      d="M191.743 31.655C184.491 29.4985 173.581 20.4452 177.747 16.125C181.983 11.7315 186.867 20.9113 183.539 23.3369C180.21 25.7625 171.743 20.1551 170.243 16.6551"
      stroke="#798CDD"
      stroke-width="1.4"
      stroke-linecap="round"
    />
    <path
      d="M62.9301 92.4212H59.3327C59.1559 92.4212 59.019 92.2664 59.0406 92.091C59.7132 86.6164 59.7827 81.0846 59.2477 75.5949L59.1619 74.7144C59.1369 74.4574 59.4976 74.3734 59.5887 74.615C61.5251 79.7539 62.703 85.1474 63.0852 90.6257L63.191 92.1414C63.2016 92.2927 63.0817 92.4212 62.9301 92.4212Z"
      fill="#798CDD"
    />
    <path
      d="M66.044 92.4211H62.0772C61.7852 92.4211 61.5598 92.1644 61.5976 91.8749C62.3911 85.7913 63.6358 79.7751 65.3212 73.876L66.2776 70.5285C66.3578 70.2479 66.7749 70.327 66.7467 70.6175C66.0747 77.5616 65.996 84.5504 66.5114 91.5079L66.5395 91.8875C66.5608 92.1756 66.3328 92.4211 66.044 92.4211Z"
      fill="#798CDD"
    />
    <rect x="185.4" y="88.3999" width="1.6" height="13.6" fill="#798CDD" />
    <path
      d="M192.6 89.8081C192.6 92.1232 189.556 94 185.8 94C182.044 94 179 92.1232 179 89.8081C179 87.493 182.044 78 185.8 78C189.556 78 192.6 87.493 192.6 89.8081Z"
      fill="#798CDD"
    />
    <defs>
      <filter
        id="filter0_d_14685_40287"
        x="96.5789"
        y="19.8596"
        width="49.8421"
        height="15.8421"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="2.2807" />
        <feGaussianBlur stdDeviation="1.71053" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.772549 0 0 0 0 0.8 0 0 0 0 0.941176 0 0 0 0.19 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_14685_40287"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_14685_40287"
          result="shape"
        />
      </filter>
      <filter
        id="filter1_bdi_14685_40287"
        x="108.316"
        y="20.3158"
        width="65.3684"
        height="59.1839"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feGaussianBlur in="BackgroundImageFix" stdDeviation="6.84211" />
        <feComposite
          in2="SourceAlpha"
          operator="in"
          result="effect1_backgroundBlur_14685_40287"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dx="-2" dy="-2" />
        <feGaussianBlur stdDeviation="1.5" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.772549 0 0 0 0 0.8 0 0 0 0 0.941176 0 0 0 0.49 0"
        />
        <feBlend
          mode="normal"
          in2="effect1_backgroundBlur_14685_40287"
          result="effect2_dropShadow_14685_40287"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect2_dropShadow_14685_40287"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="2.2807" />
        <feGaussianBlur stdDeviation="2.2807" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect3_innerShadow_14685_40287"
        />
      </filter>
      <filter
        id="filter2_d_14685_40287"
        x="126.657"
        y="44.7809"
        width="27.0571"
        height="8.60003"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="1.2381" />
        <feGaussianBlur stdDeviation="0.928572" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.772549 0 0 0 0 0.8 0 0 0 0 0.941176 0 0 0 0.19 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_14685_40287"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_14685_40287"
          result="shape"
        />
      </filter>
      <linearGradient
        id="paint0_linear_14685_40287"
        x1="124.642"
        y1="44"
        x2="124.642"
        y2="102"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#C1DBFE" />
        <stop offset="1" stop-color="white" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_14685_40287"
        x1="141"
        y1="34"
        x2="141"
        y2="49.9076"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#C5CCF0" stop-opacity="0.58" />
        <stop offset="1" stop-color="#5A72D6" stop-opacity="0" />
      </linearGradient>
      <clipPath id="clip0_14685_40287">
        <rect width="70" height="59" fill="white" transform="translate(85)" />
      </clipPath>
    </defs>
  </svg>
);

// 工作台邮件
// 客服头像
export const AgentUserIcon = () => (
  <svg
    style={{ float: 'left', marginRight: '8px' }}
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
  >
    <circle cx="12" cy="12" r="12" fill="#3463FC" fill-opacity="0.2" />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M7.4267 12.1268V8.56303C7.37683 8.56303 7.32697 8.56476 7.27882 8.56911C8.08645 6.81559 9.88455 5.59415 11.9767 5.59415C14.0688 5.59415 15.8678 6.81559 16.6745 8.56911C16.6255 8.56564 16.5765 8.56303 16.5267 8.56303V12.1268C17.5311 12.1268 18.3467 11.3288 18.3467 10.3454C18.3467 9.67688 17.9704 9.09502 17.4148 8.7896C16.6325 6.58375 14.4967 5 11.9767 5C9.45671 5 7.32169 6.58375 6.53855 8.78959C5.98293 9.09412 5.60669 9.67686 5.60669 10.3445C5.60669 11.3288 6.42133 12.1268 7.4267 12.1268ZM11.9761 6.78503C9.79998 6.78503 8.036 8.51309 8.036 10.6446C8.036 12.7761 9.79998 14.5042 11.9761 14.5042C14.1522 14.5042 15.9162 12.7761 15.9162 10.6446C15.9153 8.51309 14.1522 6.78503 11.9761 6.78503ZM11.9764 15.6876C13.4962 15.6876 14.8778 15.1111 15.9077 14.1695C17.7688 15.0962 18.9999 16.7508 18.9999 18.9995H5C5 16.7551 6.20924 15.1023 8.05026 14.1748C9.07926 15.1137 10.4591 15.6876 11.9764 15.6876Z"
      fill="#3463FC"
    />
  </svg>
);
// 智能总结按钮图标
export const AiBtnIcon = () => (
  <svg
    style={{ float: 'left', marginRight: '3px', marginTop: '1px' }}
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="7.65817"
      y="2.67379"
      width="0.472509"
      height="3.30756"
      fill="white"
      stroke="#020272"
      stroke-width="0.214777"
    />
    <circle
      cx="7.86832"
      cy="2.40445"
      r="0.720895"
      fill="white"
      stroke="#020272"
      stroke-width="0.214777"
    />
    <g filter="url(#filter0_i_1304_12744)">
      <ellipse
        cx="2.13832"
        cy="8.81785"
        rx="1.13832"
        ry="1.6323"
        fill="white"
      />
    </g>
    <path
      d="M3.16924 8.81785C3.16924 9.24935 3.0471 9.63538 2.85514 9.91063C2.66293 10.1863 2.40781 10.3428 2.13832 10.3428C1.86882 10.3428 1.6137 10.1863 1.42149 9.91063C1.22953 9.63538 1.10739 9.24935 1.10739 8.81785C1.10739 8.38635 1.22953 8.00032 1.42149 7.72506C1.6137 7.44944 1.86882 7.29294 2.13832 7.29294C2.40781 7.29294 2.66293 7.44944 2.85514 7.72506C3.0471 8.00032 3.16924 8.38635 3.16924 8.81785Z"
      stroke="#030178"
      stroke-width="0.214777"
    />
    <g filter="url(#filter1_i_1304_12744)">
      <ellipse
        cx="13.6505"
        cy="8.81785"
        rx="1.13832"
        ry="1.6323"
        fill="white"
      />
    </g>
    <path
      d="M14.6815 8.81785C14.6815 9.24935 14.5593 9.63538 14.3673 9.91063C14.1751 10.1863 13.92 10.3428 13.6505 10.3428C13.381 10.3428 13.1259 10.1863 12.9337 9.91063C12.7417 9.63538 12.6196 9.24935 12.6196 8.81785C12.6196 8.38635 12.7417 8.00032 12.9337 7.72506C13.1259 7.44944 13.381 7.29294 13.6505 7.29294C13.92 7.29294 14.1751 7.44944 14.3673 7.72506C14.5593 8.00032 14.6815 8.38635 14.6815 8.81785Z"
      stroke="#030178"
      stroke-width="0.214777"
    />
    <g filter="url(#filter2_i_1304_12744)">
      <ellipse
        cx="7.93715"
        cy="8.66743"
        rx="5.82045"
        ry="4.83247"
        fill="white"
      />
    </g>
    <path
      d="M13.6502 8.66744C13.6502 11.2592 11.1119 13.3925 7.93715 13.3925C4.76235 13.3925 2.22409 11.2592 2.22409 8.66744C2.22409 6.07568 4.76235 3.94235 7.93715 3.94235C11.1119 3.94235 13.6502 6.07568 13.6502 8.66744Z"
      stroke="#030178"
      stroke-width="0.214777"
    />
    <g filter="url(#filter3_i_1304_12744)">
      <path
        d="M12.5118 9.1081C12.5118 11.1903 10.4636 10.6467 7.93705 10.6467C5.41049 10.6467 3.3623 11.1903 3.3623 9.1081C3.3623 7.02587 5.41049 5.33789 7.93705 5.33789C10.4636 5.33789 12.5118 7.02587 12.5118 9.1081Z"
        fill="#020272"
      />
    </g>
    <path
      d="M12.4044 9.1081C12.4044 9.61139 12.2809 9.93726 12.0747 10.1527C11.8668 10.3699 11.5591 10.4915 11.1559 10.552C10.7528 10.6126 10.2688 10.6102 9.72136 10.5918C9.5722 10.5868 9.41839 10.5806 9.26061 10.5742C8.84282 10.5573 8.39727 10.5393 7.93705 10.5393C7.47682 10.5393 7.03128 10.5573 6.61348 10.5742C6.45571 10.5806 6.30189 10.5868 6.15274 10.5918C5.60533 10.6102 5.12133 10.6126 4.71817 10.552C4.31501 10.4915 4.00733 10.3699 3.79939 10.1527C3.59318 9.93726 3.46969 9.61139 3.46969 9.1081C3.46969 7.10368 5.44943 5.44528 7.93705 5.44528C10.4247 5.44528 12.4044 7.10368 12.4044 9.1081Z"
      stroke="#030178"
      stroke-width="0.214777"
    />
    <g filter="url(#filter4_d_1304_12744)">
      <path
        d="M6.60658 8.93366C6.72926 8.95511 6.84911 8.87202 6.83955 8.74784C6.83524 8.69197 6.82573 8.63646 6.81105 8.58204C6.77669 8.45474 6.71484 8.33652 6.62986 8.23571C6.54487 8.13489 6.43882 8.05393 6.31917 7.99853C6.19952 7.94313 6.06918 7.91463 5.93733 7.91504C5.80547 7.91546 5.67531 7.94477 5.55601 8.00092C5.43671 8.05707 5.33117 8.13869 5.24682 8.24003C5.16247 8.34138 5.10136 8.45998 5.0678 8.5875C5.05345 8.642 5.04429 8.69757 5.04033 8.75347C5.03155 8.87771 5.15191 8.96005 5.27446 8.93782L5.30252 8.93273C5.40957 8.91332 5.47628 8.80751 5.50397 8.7023C5.52075 8.63854 5.55131 8.57924 5.59348 8.52857C5.63566 8.4779 5.68843 8.43708 5.74808 8.40901C5.80773 8.38094 5.87281 8.36628 5.93874 8.36607C6.00466 8.36587 6.06983 8.38011 6.12966 8.40782C6.18948 8.43552 6.24251 8.476 6.285 8.5264C6.32749 8.57681 6.35842 8.63592 6.3756 8.69957C6.40395 8.80461 6.47131 8.91 6.57848 8.92874L6.60658 8.93366Z"
        fill="#01E3FB"
      />
      <path
        d="M10.5582 8.93366C10.6809 8.95511 10.8008 8.87202 10.7912 8.74784C10.7869 8.69197 10.7774 8.63646 10.7627 8.58204C10.7283 8.45474 10.6665 8.33652 10.5815 8.23571C10.4965 8.13489 10.3905 8.05393 10.2708 7.99853C10.1512 7.94313 10.0208 7.91463 9.88899 7.91504C9.75713 7.91546 9.62697 7.94477 9.50767 8.00092C9.38837 8.05707 9.28283 8.13869 9.19848 8.24003C9.11413 8.34138 9.05302 8.45998 9.01946 8.5875C9.00511 8.642 8.99595 8.69757 8.99199 8.75347C8.98321 8.87771 9.10357 8.96005 9.22612 8.93782L9.25418 8.93273C9.36123 8.91332 9.42794 8.80751 9.45563 8.7023C9.47241 8.63854 9.50297 8.57924 9.54514 8.52857C9.58732 8.4779 9.64009 8.43708 9.69974 8.40901C9.75939 8.38094 9.82447 8.36628 9.8904 8.36607C9.95632 8.36587 10.0215 8.38011 10.0813 8.40782C10.1411 8.43552 10.1942 8.476 10.2367 8.5264C10.2792 8.57681 10.3101 8.63592 10.3273 8.69957C10.3556 8.80461 10.423 8.91 10.5301 8.92874L10.5582 8.93366Z"
        fill="#01E3FB"
      />
    </g>
    <defs>
      <filter
        id="filter0_i_1304_12744"
        x="1"
        y="7.09964"
        width="2.27686"
        height="3.35056"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="-1.11684" />
        <feGaussianBlur stdDeviation="0.0429553" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.592157 0 0 0 0 0.588235 0 0 0 0 0.682353 0 0 0 1 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_1304_12744"
        />
      </filter>
      <filter
        id="filter1_i_1304_12744"
        x="12.5122"
        y="7.09964"
        width="2.27686"
        height="3.35056"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="-1.11684" />
        <feGaussianBlur stdDeviation="0.0429553" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.592157 0 0 0 0 0.588235 0 0 0 0 0.682353 0 0 0 1 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_1304_12744"
        />
      </filter>
      <filter
        id="filter2_i_1304_12744"
        x="2.1167"
        y="3.74905"
        width="11.6411"
        height="9.75095"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="-1.11684" />
        <feGaussianBlur stdDeviation="0.0429553" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.592157 0 0 0 0 0.588235 0 0 0 0 0.682353 0 0 0 1 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_1304_12744"
        />
      </filter>
      <filter
        id="filter3_i_1304_12744"
        x="3.3623"
        y="5.33789"
        width="9.14941"
        height="5.416"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="0.773196" />
        <feGaussianBlur stdDeviation="0.0214777" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.501961 0 0 0 0 0.517647 0 0 0 0 0.752941 0 0 0 1 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_1304_12744"
        />
      </filter>
      <filter
        id="filter4_d_1304_12744"
        x="3.75138"
        y="6.66933"
        width="8.32878"
        height="3.60369"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="0.0429553" />
        <feGaussianBlur stdDeviation="0.64433" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.00392157 0 0 0 0 0.890196 0 0 0 0 0.984314 0 0 0 1 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_1304_12744"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_1304_12744"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);

// 邮件发送
export const EmailSendIcon = () => (
  <svg
    style={{ float: 'right', marginLeft: '5px', marginTop: '2.2px' }}
    xmlns="http://www.w3.org/2000/svg"
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
  >
    <path
      d="M12.2432 6.23476L3.05568 0.990775C2.90081 0.903901 2.72318 0.866202 2.54637 0.882685C2.36957 0.899168 2.20197 0.969053 2.06583 1.08306C1.92969 1.19706 1.83145 1.34979 1.78418 1.52095C1.7369 1.69211 1.74282 1.8736 1.80115 2.04132L3.49646 6.9889C3.49624 6.99072 3.49624 6.99255 3.49646 6.99437C3.49615 6.99618 3.49615 6.99803 3.49646 6.99984L1.80115 11.9584C1.75444 12.0903 1.74006 12.2315 1.75921 12.3701C1.77837 12.5088 1.8305 12.6408 1.91123 12.7551C1.99197 12.8695 2.09895 12.9627 2.22319 13.0272C2.34744 13.0916 2.48533 13.1253 2.62529 13.1254C2.77714 13.125 2.92632 13.0854 3.05841 13.0105L12.241 7.75781C12.3764 7.68193 12.4893 7.57138 12.5679 7.4375C12.6465 7.30363 12.6881 7.15124 12.6884 6.99599C12.6886 6.84073 12.6476 6.6882 12.5695 6.55404C12.4913 6.41989 12.3789 6.30893 12.2437 6.23257L12.2432 6.23476ZM2.62529 12.2504V12.2455L4.27357 7.43788H7.43779C7.55382 7.43788 7.6651 7.39179 7.74715 7.30974C7.8292 7.2277 7.87529 7.11642 7.87529 7.00038C7.87529 6.88435 7.8292 6.77307 7.74715 6.69103C7.6651 6.60898 7.55382 6.56288 7.43779 6.56288H4.27794L2.62857 1.75695L2.62529 1.75038L11.8128 6.99109L2.62529 12.2504Z"
      fill="white"
    />
  </svg>
);

// 抄送
export const MakeCopyIcon = () => (
  <svg
    style={{ marginTop: '4px', float: 'left', marginLeft: '4px' }}
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
  >
    <path
      d="M20.853 7.61913L16.3563 3.1455C16.2886 3.07812 16.2033 3.03319 16.1115 3.01269C16.1085 3.01204 16.1056 3.01074 16.1027 3.01009C16.07 3.00343 16.0368 3.00005 16.0034 3H8.93636C7.50131 3 6.33367 4.16765 6.33367 5.60255V12.5273C6.33367 12.8034 6.55746 13.0273 6.83366 13.0273C7.10987 13.0273 7.33366 12.8034 7.33366 12.5273V5.60253C7.33366 4.71875 8.05256 3.99999 8.93636 3.99999H15.5021V8.00455C15.5021 8.2806 15.7261 8.50455 16.0021 8.50455H20.0002V18.3857C20.0002 19.2757 19.2762 20 18.3862 20H8.93636C8.05258 20 7.33366 19.2757 7.33366 18.3857V16.694C7.33366 16.418 7.10987 16.194 6.83366 16.194C6.55746 16.194 6.33367 16.418 6.33367 16.694V18.3857C6.33367 19.8271 7.50132 21 8.93636 21H18.3862C19.8276 21 21.0002 19.8271 21.0002 18.3857V7.97362C21.0002 7.8405 20.9474 7.71288 20.853 7.61913ZM16.5021 4.70149L19.3198 7.50456H16.5021V4.70149ZM5.60271 20C4.71893 20 4.00001 19.2757 4.00001 18.3857V5.60253C4.00001 4.71875 4.71893 3.99999 5.60271 3.99999C5.8789 3.99999 6.10271 3.77603 6.10271 3.5C6.10271 3.22363 5.87892 3 5.60271 3C4.16765 2.99998 3 4.16764 3 5.60253V18.3857C3 19.8271 4.16764 21 5.60269 21C5.87888 21 6.10269 20.776 6.10269 20.5C6.10271 20.2236 5.8789 20 5.60271 20Z"
      fill="url(#paint0_linear_14701_100275)"
    />
    <path
      d="M20.853 7.61913L16.3563 3.1455C16.2886 3.07812 16.2033 3.03319 16.1115 3.01269C16.1085 3.01204 16.1056 3.01074 16.1027 3.01009C16.07 3.00343 16.0368 3.00005 16.0034 3H8.93636C7.50131 3 6.33367 4.16765 6.33367 5.60255V12.5273C6.33367 12.8034 6.55746 13.0273 6.83366 13.0273C7.10987 13.0273 7.33366 12.8034 7.33366 12.5273V5.60253C7.33366 4.71875 8.05256 3.99999 8.93636 3.99999H15.5021V8.00455C15.5021 8.2806 15.7261 8.50455 16.0021 8.50455H20.0002V18.3857C20.0002 19.2757 19.2762 20 18.3862 20H8.93636C8.05258 20 7.33366 19.2757 7.33366 18.3857V16.694C7.33366 16.418 7.10987 16.194 6.83366 16.194C6.55746 16.194 6.33367 16.418 6.33367 16.694V18.3857C6.33367 19.8271 7.50132 21 8.93636 21H18.3862C19.8276 21 21.0002 19.8271 21.0002 18.3857V7.97362C21.0002 7.8405 20.9474 7.71288 20.853 7.61913ZM16.5021 4.70149L19.3198 7.50456H16.5021V4.70149ZM5.60271 20C4.71893 20 4.00001 19.2757 4.00001 18.3857V5.60253C4.00001 4.71875 4.71893 3.99999 5.60271 3.99999C5.8789 3.99999 6.10271 3.77603 6.10271 3.5C6.10271 3.22363 5.87892 3 5.60271 3C4.16765 2.99998 3 4.16764 3 5.60253V18.3857C3 19.8271 4.16764 21 5.60269 21C5.87888 21 6.10269 20.776 6.10269 20.5C6.10271 20.2236 5.8789 20 5.60271 20Z"
      fill="#3463FC"
    />
    <path
      d="M4.74023 14.5702C4.74023 14.8466 4.96402 15.0702 5.24023 15.0702H12.6652L10.8626 16.873C10.6673 17.0683 10.6673 17.3847 10.8626 17.58C10.909 17.6265 10.9641 17.6634 11.0248 17.6885C11.0854 17.7137 11.1505 17.7266 11.2161 17.7265C11.2818 17.7266 11.3469 17.7137 11.4075 17.6885C11.4682 17.6634 11.5233 17.6265 11.5697 17.58L14.2258 14.9238C14.2264 14.9231 14.2264 14.9225 14.227 14.9218C14.2726 14.8762 14.3087 14.8216 14.3338 14.7617C14.3351 14.7578 14.3358 14.7538 14.3374 14.7496C14.3585 14.6949 14.3709 14.636 14.3716 14.5742V14.567C14.3712 14.5045 14.3585 14.4452 14.3371 14.3902C14.3358 14.3867 14.3351 14.3831 14.3338 14.3795C14.3087 14.319 14.2721 14.264 14.2261 14.2174C14.2257 14.2174 14.2257 14.2171 14.2257 14.2167C14.2251 14.2167 14.2251 14.2164 14.2247 14.2161L11.5695 11.5608C11.3742 11.3655 11.0578 11.3655 10.8625 11.5608C10.6671 11.7561 10.6671 12.0728 10.8625 12.2678L12.6649 14.0702H5.24023C4.96402 14.0702 4.74023 14.2942 4.74023 14.5702Z"
      fill="url(#paint1_linear_14701_100275)"
    />
    <path
      d="M4.74023 14.5702C4.74023 14.8466 4.96402 15.0702 5.24023 15.0702H12.6652L10.8626 16.873C10.6673 17.0683 10.6673 17.3847 10.8626 17.58C10.909 17.6265 10.9641 17.6634 11.0248 17.6885C11.0854 17.7137 11.1505 17.7266 11.2161 17.7265C11.2818 17.7266 11.3469 17.7137 11.4075 17.6885C11.4682 17.6634 11.5233 17.6265 11.5697 17.58L14.2258 14.9238C14.2264 14.9231 14.2264 14.9225 14.227 14.9218C14.2726 14.8762 14.3087 14.8216 14.3338 14.7617C14.3351 14.7578 14.3358 14.7538 14.3374 14.7496C14.3585 14.6949 14.3709 14.636 14.3716 14.5742V14.567C14.3712 14.5045 14.3585 14.4452 14.3371 14.3902C14.3358 14.3867 14.3351 14.3831 14.3338 14.3795C14.3087 14.319 14.2721 14.264 14.2261 14.2174C14.2257 14.2174 14.2257 14.2171 14.2257 14.2167C14.2251 14.2167 14.2251 14.2164 14.2247 14.2161L11.5695 11.5608C11.3742 11.3655 11.0578 11.3655 10.8625 11.5608C10.6671 11.7561 10.6671 12.0728 10.8625 12.2678L12.6649 14.0702H5.24023C4.96402 14.0702 4.74023 14.2942 4.74023 14.5702Z"
      fill="#3463FC"
    />
    <defs>
      <linearGradient
        id="paint0_linear_14701_100275"
        x1="12.0001"
        y1="3"
        x2="12.0001"
        y2="21"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#D169FF" />
        <stop offset="1" stop-color="#AD30E5" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_14701_100275"
        x1="9.5559"
        y1="11.4143"
        x2="9.5559"
        y2="17.7265"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#D169FF" />
        <stop offset="1" stop-color="#AD30E5" />
      </linearGradient>
    </defs>
  </svg>
);
// 密送
export const BccCopyIcon = () => (
  <svg
    style={{ marginTop: '4px', float: 'left', marginLeft: '4px' }}
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
  >
    <path
      d="M17.8646 9.96807H7.55045V8.2848C7.55045 5.99106 9.41762 4.12388 11.7139 4.12388C14.0102 4.12388 15.8774 5.99361 15.8774 8.28991C15.8774 8.60153 16.1277 8.85185 16.4393 8.85185C16.751 8.85185 17.0013 8.60153 17.0013 8.28991C17.0013 5.37292 14.6284 3 11.7139 3C10.3014 3 8.97318 3.54917 7.97446 4.54789C6.97573 5.54662 6.42656 6.87229 6.42656 8.2848V9.96807H5.24138C4.55172 9.96807 4 10.5198 4 11.2069V19.7407C4 20.4304 4.55428 21 5.24138 21H17.8621C18.5517 21 19.1188 20.4304 19.1188 19.7407V11.2069C19.1213 10.5198 18.5543 9.96807 17.8646 9.96807ZM17.9974 19.7535C17.9974 19.8225 17.9413 19.8787 17.8723 19.8787H5.24904C5.18008 19.8787 5.12388 19.8225 5.12388 19.7535V11.2171C5.12388 11.1481 5.18008 11.092 5.24904 11.092H17.8697C17.9387 11.092 17.9949 11.1481 17.9949 11.2171V19.7535H17.9974ZM11.4585 12.8799C10.8225 12.8799 10.3091 13.3934 10.3091 14.0294C10.3091 14.4483 10.5338 14.8161 10.871 15.0179V17.2478C10.871 17.5568 11.1239 17.8097 11.433 17.8097C11.742 17.8097 11.9949 17.5568 11.9949 17.2478V15.046C12.3602 14.8544 12.6079 14.4713 12.6079 14.0294C12.6079 13.3934 12.0945 12.8799 11.4585 12.8799Z"
      fill="url(#paint0_linear_14701_101005)"
    />
    <path
      d="M17.8646 9.96807H7.55045V8.2848C7.55045 5.99106 9.41762 4.12388 11.7139 4.12388C14.0102 4.12388 15.8774 5.99361 15.8774 8.28991C15.8774 8.60153 16.1277 8.85185 16.4393 8.85185C16.751 8.85185 17.0013 8.60153 17.0013 8.28991C17.0013 5.37292 14.6284 3 11.7139 3C10.3014 3 8.97318 3.54917 7.97446 4.54789C6.97573 5.54662 6.42656 6.87229 6.42656 8.2848V9.96807H5.24138C4.55172 9.96807 4 10.5198 4 11.2069V19.7407C4 20.4304 4.55428 21 5.24138 21H17.8621C18.5517 21 19.1188 20.4304 19.1188 19.7407V11.2069C19.1213 10.5198 18.5543 9.96807 17.8646 9.96807ZM17.9974 19.7535C17.9974 19.8225 17.9413 19.8787 17.8723 19.8787H5.24904C5.18008 19.8787 5.12388 19.8225 5.12388 19.7535V11.2171C5.12388 11.1481 5.18008 11.092 5.24904 11.092H17.8697C17.9387 11.092 17.9949 11.1481 17.9949 11.2171V19.7535H17.9974ZM11.4585 12.8799C10.8225 12.8799 10.3091 13.3934 10.3091 14.0294C10.3091 14.4483 10.5338 14.8161 10.871 15.0179V17.2478C10.871 17.5568 11.1239 17.8097 11.433 17.8097C11.742 17.8097 11.9949 17.5568 11.9949 17.2478V15.046C12.3602 14.8544 12.6079 14.4713 12.6079 14.0294C12.6079 13.3934 12.0945 12.8799 11.4585 12.8799Z"
      fill="#3463FC"
    />
    <defs>
      <linearGradient
        id="paint0_linear_14701_101005"
        x1="11.5594"
        y1="3"
        x2="11.5594"
        y2="21"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#D169FF" />
        <stop offset="1" stop-color="#AD30E5" />
      </linearGradient>
    </defs>
  </svg>
);
// 草稿
export const NewAiIcon = () => (
  <svg
    style={{ float: 'left' }}
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M4.03808 9.94214C4.03808 10.3507 3.70692 10.6818 3.29841 10.6818C2.88989 10.6818 2.55873 10.3507 2.55873 9.94214V5.72762L9.82693 1.6665L11.0988 2.45788C11.4457 2.6737 11.5519 3.12984 11.3361 3.47668C11.1203 3.8235 10.6642 3.92972 10.3173 3.71395L9.78654 3.38373L4.03808 6.59555V9.94214ZM15.577 14.9556C15.2247 15.1624 14.7715 15.0445 14.5647 14.6922C14.3579 14.3399 14.4759 13.8867 14.8281 13.6799L15.3884 13.351V6.71375L12.57 5.00254C12.2208 4.79054 12.1096 4.33565 12.3216 3.98648C12.5336 3.63726 12.9885 3.52603 13.3377 3.73805L16.8678 5.88132V14.1979L15.577 14.9556ZM9.62529 18.3332L2.5 14.0882L2.52839 12.5294C2.53583 12.1209 2.87296 11.7959 3.2814 11.8033C3.68983 11.8107 4.0149 12.1479 4.00747 12.5563L3.99474 13.2568L9.64808 16.6247L12.5723 14.9862C12.9287 14.7865 13.3795 14.9135 13.5792 15.2699C13.7788 15.6263 13.6518 16.077 13.2954 16.2767L9.62529 18.3332Z"
      fill="url(#paint0_linear_14701_100283)"
    />
    <path
      d="M4.03808 9.94214C4.03808 10.3507 3.70692 10.6818 3.29841 10.6818C2.88989 10.6818 2.55873 10.3507 2.55873 9.94214V5.72762L9.82693 1.6665L11.0988 2.45788C11.4457 2.6737 11.5519 3.12984 11.3361 3.47668C11.1203 3.8235 10.6642 3.92972 10.3173 3.71395L9.78654 3.38373L4.03808 6.59555V9.94214ZM15.577 14.9556C15.2247 15.1624 14.7715 15.0445 14.5647 14.6922C14.3579 14.3399 14.4759 13.8867 14.8281 13.6799L15.3884 13.351V6.71375L12.57 5.00254C12.2208 4.79054 12.1096 4.33565 12.3216 3.98648C12.5336 3.63726 12.9885 3.52603 13.3377 3.73805L16.8678 5.88132V14.1979L15.577 14.9556ZM9.62529 18.3332L2.5 14.0882L2.52839 12.5294C2.53583 12.1209 2.87296 11.7959 3.2814 11.8033C3.68983 11.8107 4.0149 12.1479 4.00747 12.5563L3.99474 13.2568L9.64808 16.6247L12.5723 14.9862C12.9287 14.7865 13.3795 14.9135 13.5792 15.2699C13.7788 15.6263 13.6518 16.077 13.2954 16.2767L9.62529 18.3332Z"
      fill="url(#paint1_linear_14701_100283)"
    />
    <path
      d="M11.5002 12.5645H10.3744L9.92659 11.4006H7.87843L7.45608 12.5645H6.3584L8.3539 7.43945H9.44862L11.5002 12.5645ZM9.59492 10.5368L8.88868 8.63506L8.19649 10.5368H9.59492ZM12.0175 12.5645V7.43945H13.0523V12.5645H12.0175Z"
      fill="url(#paint2_linear_14701_100283)"
    />
    <path
      d="M11.5002 12.5645H10.3744L9.92659 11.4006H7.87843L7.45608 12.5645H6.3584L8.3539 7.43945H9.44862L11.5002 12.5645ZM9.59492 10.5368L8.88868 8.63506L8.19649 10.5368H9.59492ZM12.0175 12.5645V7.43945H13.0523V12.5645H12.0175Z"
      fill="url(#paint3_linear_14701_100283)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_14701_100283"
        x1="9.68388"
        y1="1.6665"
        x2="9.68388"
        y2="18.3332"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#D169FF" />
        <stop offset="1" stop-color="#AD30E5" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_14701_100283"
        x1="2.5"
        y1="9.99984"
        x2="13.233"
        y2="3.75388"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3463FC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <linearGradient
        id="paint2_linear_14701_100283"
        x1="9.70536"
        y1="7.43945"
        x2="9.70536"
        y2="12.5645"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#D169FF" />
        <stop offset="1" stop-color="#AD30E5" />
      </linearGradient>
      <linearGradient
        id="paint3_linear_14701_100283"
        x1="6.3584"
        y1="10.002"
        x2="10.1246"
        y2="6.68138"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3463FC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
    </defs>
  </svg>
);
// 优化
export const OptimizeIcon = () => (
  <svg
    style={{ float: 'left' }}
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <g clip-path="url(#clip0_14701_101019)">
      <path
        d="M8.5 7C8.776 7 9 6.776 9 6.5C9 5.122 10.122 4 11.5 4C11.776 4 12 3.776 12 3.5C12 3.224 11.776 3 11.5 3C10.122 3 9 1.878 9 0.5C9 0.224 8.776 0 8.5 0C8.224 0 8 0.224 8 0.5C8 1.878 6.878 3 5.5 3C5.224 3 5 3.224 5 3.5C5 3.776 5.224 4 5.5 4C6.878 4 8 5.122 8 6.5C8 6.776 8.224 7 8.5 7ZM9.699 3.5C9.209 3.796 8.796 4.208 8.5 4.699C8.204 4.209 7.792 3.796 7.301 3.5C7.791 3.204 8.204 2.792 8.5 2.301C8.796 2.791 9.208 3.204 9.699 3.5Z"
        fill="url(#paint0_linear_14701_101019)"
      />
      <path
        d="M18.5 10C18.776 10 19 9.776 19 9.5C19 9.224 19.224 9 19.5 9C19.776 9 20 8.776 20 8.5C20 8.224 19.776 8 19.5 8C19.224 8 19 7.776 19 7.5C19 7.224 18.776 7 18.5 7C18.224 7 18 7.224 18 7.5C18 7.776 17.776 8 17.5 8C17.224 8 17 8.224 17 8.5C17 8.776 17.224 9 17.5 9C17.776 9 18 9.224 18 9.5C18 9.776 18.224 10 18.5 10Z"
        fill="url(#paint1_linear_14701_101019)"
      />
      <path
        d="M1.85296 15.939L12.439 5.35304C12.722 5.07004 13.098 4.91504 13.5 4.91504C13.902 4.91504 14.278 5.07104 14.561 5.35304L15.147 5.93904C15.43 6.22204 15.585 6.59804 15.585 7.00004C15.585 7.40204 15.429 7.77804 15.147 8.06104L4.56096 18.647C4.27796 18.93 3.90196 19.085 3.49996 19.085C3.09796 19.085 2.72196 18.929 2.43896 18.647L1.85296 18.061C1.56996 17.778 1.41496 17.402 1.41496 17C1.41496 16.598 1.57096 16.222 1.85296 15.939ZM14.439 6.64604L13.853 6.06004C13.759 5.96604 13.634 5.91504 13.499 5.91504C13.364 5.91504 13.239 5.96704 13.145 6.06004L11.706 7.49904L12.999 8.79204L14.438 7.35304C14.633 7.15804 14.633 6.84104 14.438 6.64604H14.439ZM2.56096 17.354L3.14696 17.94C3.24096 18.034 3.36596 18.085 3.49996 18.085C3.63396 18.085 3.75996 18.033 3.85296 17.94L12.292 9.50104L10.999 8.20804L2.55996 16.647C2.36496 16.842 2.36496 17.159 2.55996 17.354H2.56096Z"
        fill="url(#paint2_linear_14701_101019)"
      />
      <path
        d="M16.5 5C16.776 5 17 4.776 17 4.5C17 3.673 17.673 3 18.5 3C18.776 3 19 2.776 19 2.5C19 2.224 18.776 2 18.5 2C17.673 2 17 1.327 17 0.5C17 0.224 16.776 0 16.5 0C16.224 0 16 0.224 16 0.5C16 1.327 15.327 2 14.5 2C14.224 2 14 2.224 14 2.5C14 2.776 14.224 3 14.5 3C15.327 3 16 3.673 16 4.5C16 4.776 16.224 5 16.5 5ZM17.002 2.5C16.812 2.643 16.643 2.812 16.5 3.002C16.357 2.812 16.188 2.643 15.998 2.5C16.188 2.357 16.357 2.188 16.5 1.998C16.643 2.188 16.812 2.357 17.002 2.5Z"
        fill="url(#paint3_linear_14701_101019)"
      />
      <path
        d="M16.5 15C16.776 15 17 14.776 17 14.5C17 13.673 17.673 13 18.5 13C18.776 13 19 12.776 19 12.5C19 12.224 18.776 12 18.5 12C17.673 12 17 11.327 17 10.5C17 10.224 16.776 10 16.5 10C16.224 10 16 10.224 16 10.5C16 11.327 15.327 12 14.5 12C14.224 12 14 12.224 14 12.5C14 12.776 14.224 13 14.5 13C15.327 13 16 13.673 16 14.5C16 14.776 16.224 15 16.5 15ZM17.002 12.5C16.812 12.643 16.643 12.812 16.5 13.002C16.357 12.812 16.188 12.643 15.998 12.5C16.188 12.357 16.357 12.188 16.5 11.998C16.643 12.188 16.812 12.357 17.002 12.5Z"
        fill="url(#paint4_linear_14701_101019)"
      />
    </g>
    <defs>
      <linearGradient
        id="paint0_linear_14701_101019"
        x1="12"
        y1="3.5"
        x2="7.19131"
        y2="0.253876"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3463FC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_14701_101019"
        x1="20"
        y1="8.5"
        x2="17.9391"
        y2="7.1088"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3463FC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <linearGradient
        id="paint2_linear_14701_101019"
        x1="15.585"
        y1="12"
        x2="5.85079"
        y2="5.42895"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3463FC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <linearGradient
        id="paint3_linear_14701_101019"
        x1="19"
        y1="2.5"
        x2="15.5652"
        y2="0.18134"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3463FC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <linearGradient
        id="paint4_linear_14701_101019"
        x1="19"
        y1="12.5"
        x2="15.5652"
        y2="10.1813"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3463FC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <clipPath id="clip0_14701_101019">
        <rect
          width="20"
          height="20"
          fill="white"
          transform="matrix(-1 0 0 1 20 0)"
        />
      </clipPath>
    </defs>
  </svg>
);
export const OptimizeNormalIcon = () => (
  <svg
    style={{ float: 'left' }}
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <g clip-path="url(#clip0_14701_101757)">
      <path
        d="M8.5 7C8.776 7 9 6.776 9 6.5C9 5.122 10.122 4 11.5 4C11.776 4 12 3.776 12 3.5C12 3.224 11.776 3 11.5 3C10.122 3 9 1.878 9 0.5C9 0.224 8.776 0 8.5 0C8.224 0 8 0.224 8 0.5C8 1.878 6.878 3 5.5 3C5.224 3 5 3.224 5 3.5C5 3.776 5.224 4 5.5 4C6.878 4 8 5.122 8 6.5C8 6.776 8.224 7 8.5 7ZM9.699 3.5C9.209 3.796 8.796 4.208 8.5 4.699C8.204 4.209 7.792 3.796 7.301 3.5C7.791 3.204 8.204 2.792 8.5 2.301C8.796 2.791 9.208 3.204 9.699 3.5Z"
        fill="url(#paint0_linear_14701_101757)"
      />
      <path
        d="M8.5 7C8.776 7 9 6.776 9 6.5C9 5.122 10.122 4 11.5 4C11.776 4 12 3.776 12 3.5C12 3.224 11.776 3 11.5 3C10.122 3 9 1.878 9 0.5C9 0.224 8.776 0 8.5 0C8.224 0 8 0.224 8 0.5C8 1.878 6.878 3 5.5 3C5.224 3 5 3.224 5 3.5C5 3.776 5.224 4 5.5 4C6.878 4 8 5.122 8 6.5C8 6.776 8.224 7 8.5 7ZM9.699 3.5C9.209 3.796 8.796 4.208 8.5 4.699C8.204 4.209 7.792 3.796 7.301 3.5C7.791 3.204 8.204 2.792 8.5 2.301C8.796 2.791 9.208 3.204 9.699 3.5Z"
        fill="#999999"
      />
      <path
        d="M18.5 10C18.776 10 19 9.776 19 9.5C19 9.224 19.224 9 19.5 9C19.776 9 20 8.776 20 8.5C20 8.224 19.776 8 19.5 8C19.224 8 19 7.776 19 7.5C19 7.224 18.776 7 18.5 7C18.224 7 18 7.224 18 7.5C18 7.776 17.776 8 17.5 8C17.224 8 17 8.224 17 8.5C17 8.776 17.224 9 17.5 9C17.776 9 18 9.224 18 9.5C18 9.776 18.224 10 18.5 10Z"
        fill="url(#paint1_linear_14701_101757)"
      />
      <path
        d="M18.5 10C18.776 10 19 9.776 19 9.5C19 9.224 19.224 9 19.5 9C19.776 9 20 8.776 20 8.5C20 8.224 19.776 8 19.5 8C19.224 8 19 7.776 19 7.5C19 7.224 18.776 7 18.5 7C18.224 7 18 7.224 18 7.5C18 7.776 17.776 8 17.5 8C17.224 8 17 8.224 17 8.5C17 8.776 17.224 9 17.5 9C17.776 9 18 9.224 18 9.5C18 9.776 18.224 10 18.5 10Z"
        fill="#999999"
      />
      <path
        d="M1.85296 15.939L12.439 5.35304C12.722 5.07004 13.098 4.91504 13.5 4.91504C13.902 4.91504 14.278 5.07104 14.561 5.35304L15.147 5.93904C15.43 6.22204 15.585 6.59804 15.585 7.00004C15.585 7.40204 15.429 7.77804 15.147 8.06104L4.56096 18.647C4.27796 18.93 3.90196 19.085 3.49996 19.085C3.09796 19.085 2.72196 18.929 2.43896 18.647L1.85296 18.061C1.56996 17.778 1.41496 17.402 1.41496 17C1.41496 16.598 1.57096 16.222 1.85296 15.939ZM14.439 6.64604L13.853 6.06004C13.759 5.96604 13.634 5.91504 13.499 5.91504C13.364 5.91504 13.239 5.96704 13.145 6.06004L11.706 7.49904L12.999 8.79204L14.438 7.35304C14.633 7.15804 14.633 6.84104 14.438 6.64604H14.439ZM2.56096 17.354L3.14696 17.94C3.24096 18.034 3.36596 18.085 3.49996 18.085C3.63396 18.085 3.75996 18.033 3.85296 17.94L12.292 9.50104L10.999 8.20804L2.55996 16.647C2.36496 16.842 2.36496 17.159 2.55996 17.354H2.56096Z"
        fill="url(#paint2_linear_14701_101757)"
      />
      <path
        d="M1.85296 15.939L12.439 5.35304C12.722 5.07004 13.098 4.91504 13.5 4.91504C13.902 4.91504 14.278 5.07104 14.561 5.35304L15.147 5.93904C15.43 6.22204 15.585 6.59804 15.585 7.00004C15.585 7.40204 15.429 7.77804 15.147 8.06104L4.56096 18.647C4.27796 18.93 3.90196 19.085 3.49996 19.085C3.09796 19.085 2.72196 18.929 2.43896 18.647L1.85296 18.061C1.56996 17.778 1.41496 17.402 1.41496 17C1.41496 16.598 1.57096 16.222 1.85296 15.939ZM14.439 6.64604L13.853 6.06004C13.759 5.96604 13.634 5.91504 13.499 5.91504C13.364 5.91504 13.239 5.96704 13.145 6.06004L11.706 7.49904L12.999 8.79204L14.438 7.35304C14.633 7.15804 14.633 6.84104 14.438 6.64604H14.439ZM2.56096 17.354L3.14696 17.94C3.24096 18.034 3.36596 18.085 3.49996 18.085C3.63396 18.085 3.75996 18.033 3.85296 17.94L12.292 9.50104L10.999 8.20804L2.55996 16.647C2.36496 16.842 2.36496 17.159 2.55996 17.354H2.56096Z"
        fill="#999999"
      />
      <path
        d="M16.5 5C16.776 5 17 4.776 17 4.5C17 3.673 17.673 3 18.5 3C18.776 3 19 2.776 19 2.5C19 2.224 18.776 2 18.5 2C17.673 2 17 1.327 17 0.5C17 0.224 16.776 0 16.5 0C16.224 0 16 0.224 16 0.5C16 1.327 15.327 2 14.5 2C14.224 2 14 2.224 14 2.5C14 2.776 14.224 3 14.5 3C15.327 3 16 3.673 16 4.5C16 4.776 16.224 5 16.5 5ZM17.002 2.5C16.812 2.643 16.643 2.812 16.5 3.002C16.357 2.812 16.188 2.643 15.998 2.5C16.188 2.357 16.357 2.188 16.5 1.998C16.643 2.188 16.812 2.357 17.002 2.5Z"
        fill="url(#paint3_linear_14701_101757)"
      />
      <path
        d="M16.5 5C16.776 5 17 4.776 17 4.5C17 3.673 17.673 3 18.5 3C18.776 3 19 2.776 19 2.5C19 2.224 18.776 2 18.5 2C17.673 2 17 1.327 17 0.5C17 0.224 16.776 0 16.5 0C16.224 0 16 0.224 16 0.5C16 1.327 15.327 2 14.5 2C14.224 2 14 2.224 14 2.5C14 2.776 14.224 3 14.5 3C15.327 3 16 3.673 16 4.5C16 4.776 16.224 5 16.5 5ZM17.002 2.5C16.812 2.643 16.643 2.812 16.5 3.002C16.357 2.812 16.188 2.643 15.998 2.5C16.188 2.357 16.357 2.188 16.5 1.998C16.643 2.188 16.812 2.357 17.002 2.5Z"
        fill="#999999"
      />
      <path
        d="M16.5 15C16.776 15 17 14.776 17 14.5C17 13.673 17.673 13 18.5 13C18.776 13 19 12.776 19 12.5C19 12.224 18.776 12 18.5 12C17.673 12 17 11.327 17 10.5C17 10.224 16.776 10 16.5 10C16.224 10 16 10.224 16 10.5C16 11.327 15.327 12 14.5 12C14.224 12 14 12.224 14 12.5C14 12.776 14.224 13 14.5 13C15.327 13 16 13.673 16 14.5C16 14.776 16.224 15 16.5 15ZM17.002 12.5C16.812 12.643 16.643 12.812 16.5 13.002C16.357 12.812 16.188 12.643 15.998 12.5C16.188 12.357 16.357 12.188 16.5 11.998C16.643 12.188 16.812 12.357 17.002 12.5Z"
        fill="url(#paint4_linear_14701_101757)"
      />
      <path
        d="M16.5 15C16.776 15 17 14.776 17 14.5C17 13.673 17.673 13 18.5 13C18.776 13 19 12.776 19 12.5C19 12.224 18.776 12 18.5 12C17.673 12 17 11.327 17 10.5C17 10.224 16.776 10 16.5 10C16.224 10 16 10.224 16 10.5C16 11.327 15.327 12 14.5 12C14.224 12 14 12.224 14 12.5C14 12.776 14.224 13 14.5 13C15.327 13 16 13.673 16 14.5C16 14.776 16.224 15 16.5 15ZM17.002 12.5C16.812 12.643 16.643 12.812 16.5 13.002C16.357 12.812 16.188 12.643 15.998 12.5C16.188 12.357 16.357 12.188 16.5 11.998C16.643 12.188 16.812 12.357 17.002 12.5Z"
        fill="#999999"
      />
    </g>
    <defs>
      <linearGradient
        id="paint0_linear_14701_101757"
        x1="12"
        y1="3.5"
        x2="7.19131"
        y2="0.253876"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3463FC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_14701_101757"
        x1="20"
        y1="8.5"
        x2="17.9391"
        y2="7.1088"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3463FC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <linearGradient
        id="paint2_linear_14701_101757"
        x1="15.585"
        y1="12"
        x2="5.85079"
        y2="5.42895"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3463FC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <linearGradient
        id="paint3_linear_14701_101757"
        x1="19"
        y1="2.5"
        x2="15.5652"
        y2="0.18134"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3463FC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <linearGradient
        id="paint4_linear_14701_101757"
        x1="19"
        y1="12.5"
        x2="15.5652"
        y2="10.1813"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3463FC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <clipPath id="clip0_14701_101757">
        <rect
          width="20"
          height="20"
          fill="white"
          transform="matrix(-1 0 0 1 20 0)"
        />
      </clipPath>
    </defs>
  </svg>
);
// 扩写
export const ElaborateIcon = () => (
  <svg
    style={{ float: 'left' }}
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M5.83366 13.7498H14.167V11.6665L17.0837 14.5832L14.167 17.4998V15.4165H5.83366V17.4998L2.91699 14.5832L5.83366 11.6665V13.7498ZM10.8337 4.58317V12.0832H9.16699V4.58317H4.16699V2.9165H15.8337V4.58317H10.8337Z"
      fill="url(#paint0_linear_14701_101027)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_14701_101027"
        x1="2.91699"
        y1="10.2082"
        x2="12.8236"
        y2="3.71179"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3463FC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
    </defs>
  </svg>
);
export const ElaborateNormalIcon = () => (
  <svg
    style={{ float: 'left' }}
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M5.83366 13.7498H14.167V11.6665L17.0837 14.5832L14.167 17.4998V15.4165H5.83366V17.4998L2.91699 14.5832L5.83366 11.6665V13.7498ZM10.8337 4.58317V12.0832H9.16699V4.58317H4.16699V2.9165H15.8337V4.58317H10.8337Z"
      fill="url(#paint0_linear_14701_101765)"
    />
    <path
      d="M5.83366 13.7498H14.167V11.6665L17.0837 14.5832L14.167 17.4998V15.4165H5.83366V17.4998L2.91699 14.5832L5.83366 11.6665V13.7498ZM10.8337 4.58317V12.0832H9.16699V4.58317H4.16699V2.9165H15.8337V4.58317H10.8337Z"
      fill="#999999"
    />
    <defs>
      <linearGradient
        id="paint0_linear_14701_101765"
        x1="2.91699"
        y1="10.2082"
        x2="12.8236"
        y2="3.71179"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3463FC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
    </defs>
  </svg>
);
// 缩写
export const AbbreviationIcon = () => (
  <svg
    style={{ float: 'left' }}
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M11.0417 4.58317V12.0832H9.375V4.58317H4.375V2.9165H16.0417V4.58317H11.0417Z"
      fill="url(#paint0_linear_14701_101032)"
    />
    <path
      d="M13.7497 17.4998V15.4165H16.6663V13.7498H13.7497V11.6665L10.833 14.5832L13.7497 17.4998Z"
      fill="url(#paint1_linear_14701_101032)"
    />
    <path
      d="M6.66634 17.4998V15.4165H3.74968V13.7498H6.66634V11.6665L9.58301 14.5832L6.66634 17.4998Z"
      fill="url(#paint2_linear_14701_101032)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_14701_101032"
        x1="4.375"
        y1="7.49984"
        x2="11.0871"
        y2="1.73308"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3463FC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_14701_101032"
        x1="10.833"
        y1="14.5832"
        x2="14.8403"
        y2="11.8781"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3463FC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <linearGradient
        id="paint2_linear_14701_101032"
        x1="9.58301"
        y1="14.5832"
        x2="5.57576"
        y2="11.8781"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3463FC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
    </defs>
  </svg>
);
export const AbbreviationNormalIcon = () => (
  <svg
    style={{ float: 'left' }}
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M11.0417 4.58317V12.0832H9.375V4.58317H4.375V2.9165H16.0417V4.58317H11.0417Z"
      fill="url(#paint0_linear_14701_101770)"
    />
    <path
      d="M11.0417 4.58317V12.0832H9.375V4.58317H4.375V2.9165H16.0417V4.58317H11.0417Z"
      fill="#999999"
    />
    <path
      d="M13.7497 17.4998V15.4165H16.6663V13.7498H13.7497V11.6665L10.833 14.5832L13.7497 17.4998Z"
      fill="url(#paint1_linear_14701_101770)"
    />
    <path
      d="M13.7497 17.4998V15.4165H16.6663V13.7498H13.7497V11.6665L10.833 14.5832L13.7497 17.4998Z"
      fill="#999999"
    />
    <path
      d="M6.66634 17.4998V15.4165H3.74968V13.7498H6.66634V11.6665L9.58301 14.5832L6.66634 17.4998Z"
      fill="url(#paint2_linear_14701_101770)"
    />
    <path
      d="M6.66634 17.4998V15.4165H3.74968V13.7498H6.66634V11.6665L9.58301 14.5832L6.66634 17.4998Z"
      fill="#999999"
    />
    <defs>
      <linearGradient
        id="paint0_linear_14701_101770"
        x1="4.375"
        y1="7.49984"
        x2="11.0871"
        y2="1.73308"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3463FC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_14701_101770"
        x1="10.833"
        y1="14.5832"
        x2="14.8403"
        y2="11.8781"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3463FC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <linearGradient
        id="paint2_linear_14701_101770"
        x1="9.58301"
        y1="14.5832"
        x2="5.57576"
        y2="11.8781"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3463FC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
    </defs>
  </svg>
);
// 更多操作
export const MoreIcon = () => (
  <svg
    style={{ float: 'left' }}
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M11.4587 14.1667C11.4587 14.5534 11.305 14.9244 11.0315 15.1979C10.758 15.4714 10.3871 15.625 10.0003 15.625C9.61355 15.625 9.24262 15.4714 8.96913 15.1979C8.69564 14.9244 8.54199 14.5534 8.54199 14.1667C8.54199 13.7799 8.69564 13.409 8.96913 13.1355C9.24262 12.862 9.61355 12.7083 10.0003 12.7083C10.3871 12.7083 10.758 12.862 11.0315 13.1355C11.305 13.409 11.4587 13.7799 11.4587 14.1667ZM11.4587 10C11.4587 10.3868 11.305 10.7577 11.0315 11.0312C10.758 11.3047 10.3871 11.4583 10.0003 11.4583C9.61355 11.4583 9.24262 11.3047 8.96913 11.0312C8.69564 10.7577 8.54199 10.3868 8.54199 10C8.54199 9.61323 8.69564 9.24229 8.96913 8.9688C9.24262 8.69531 9.61355 8.54167 10.0003 8.54167C10.3871 8.54167 10.758 8.69531 11.0315 8.9688C11.305 9.24229 11.4587 9.61323 11.4587 10ZM11.4587 5.83333C11.4587 6.22011 11.305 6.59104 11.0315 6.86453C10.758 7.13802 10.3871 7.29167 10.0003 7.29167C9.61355 7.29167 9.24262 7.13802 8.96913 6.86453C8.69564 6.59104 8.54199 6.22011 8.54199 5.83333C8.54199 5.44656 8.69564 5.07563 8.96913 4.80214C9.24262 4.52865 9.61355 4.375 10.0003 4.375C10.3871 4.375 10.758 4.52865 11.0315 4.80214C11.305 5.07563 11.4587 5.44656 11.4587 5.83333Z"
      fill="#333333"
    />
  </svg>
);
// 邮件模板
export const EmailTemplateIcon = () => (
  <svg
    style={{ float: 'left' }}
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M7.16078 15.2963C7.39186 15.6983 7.12203 16.25 6.65834 16.25H0.833334C0.373097 16.25 0 15.8769 0 15.4167V3.33333C0 2.8731 0.373096 2.5 0.833333 2.5H16.6667C17.1269 2.5 17.5 2.8731 17.5 3.33333V5.44841C17.5 5.89591 16.9531 6.17333 16.5507 5.97753C16.5311 5.96798 16.5112 5.95836 16.491 5.94864C16.344 5.87779 16.25 5.72946 16.25 5.56625V3.75H1.25V5.5675L6.81375 9.14375C6.64177 9.54016 6.50783 9.95201 6.41375 10.3737L1.25 7.05375V15H6.66433C6.87036 15 7.0581 15.1177 7.16078 15.2963Z"
      fill="url(#paint0_linear_14701_101784)"
    />
    <path
      d="M7.16078 15.2963C7.39186 15.6983 7.12203 16.25 6.65834 16.25H0.833334C0.373097 16.25 0 15.8769 0 15.4167V3.33333C0 2.8731 0.373096 2.5 0.833333 2.5H16.6667C17.1269 2.5 17.5 2.8731 17.5 3.33333V5.44841C17.5 5.89591 16.9531 6.17333 16.5507 5.97753C16.5311 5.96798 16.5112 5.95836 16.491 5.94864C16.344 5.87779 16.25 5.72946 16.25 5.56625V3.75H1.25V5.5675L6.81375 9.14375C6.64177 9.54016 6.50783 9.95201 6.41375 10.3737L1.25 7.05375V15H6.66433C6.87036 15 7.0581 15.1177 7.16078 15.2963Z"
      fill="#3463FC"
    />
    <path
      d="M15 10V11.25H11.25V10H15ZM11.25 12.5H13.75V13.75H11.25V12.5Z"
      fill="url(#paint1_linear_14701_101784)"
    />
    <path
      d="M15 10V11.25H11.25V10H15ZM11.25 12.5H13.75V13.75H11.25V12.5Z"
      fill="#3463FC"
    />
    <path
      d="M18.75 11.875C18.75 12.6137 18.6045 13.3451 18.3218 14.0276C18.0391 14.7101 17.6248 15.3301 17.1025 15.8525C16.5801 16.3748 15.9601 16.7891 15.2776 17.0718C14.5951 17.3545 13.8637 17.5 13.125 17.5C12.3863 17.5 11.6549 17.3545 10.9724 17.0718C10.2899 16.7891 9.66985 16.3748 9.14752 15.8525C8.62519 15.3301 8.21086 14.7101 7.92818 14.0276C7.6455 13.3451 7.5 12.6137 7.5 11.875C7.5 10.3832 8.09263 8.95242 9.14752 7.89752C10.2024 6.84263 11.6332 6.25 13.125 6.25C14.6168 6.25 16.0476 6.84263 17.1025 7.89752C18.1574 8.95242 18.75 10.3832 18.75 11.875ZM8.75 11.875C8.75 13.0353 9.21094 14.1481 10.0314 14.9686C10.8519 15.7891 11.9647 16.25 13.125 16.25C14.2853 16.25 15.3981 15.7891 16.2186 14.9686C17.0391 14.1481 17.5 13.0353 17.5 11.875C17.5 10.7147 17.0391 9.60188 16.2186 8.78141C15.3981 7.96094 14.2853 7.5 13.125 7.5C11.9647 7.5 10.8519 7.96094 10.0314 8.78141C9.21094 9.60188 8.75 10.7147 8.75 11.875Z"
      fill="url(#paint2_linear_14701_101784)"
    />
    <path
      d="M18.75 11.875C18.75 12.6137 18.6045 13.3451 18.3218 14.0276C18.0391 14.7101 17.6248 15.3301 17.1025 15.8525C16.5801 16.3748 15.9601 16.7891 15.2776 17.0718C14.5951 17.3545 13.8637 17.5 13.125 17.5C12.3863 17.5 11.6549 17.3545 10.9724 17.0718C10.2899 16.7891 9.66985 16.3748 9.14752 15.8525C8.62519 15.3301 8.21086 14.7101 7.92818 14.0276C7.6455 13.3451 7.5 12.6137 7.5 11.875C7.5 10.3832 8.09263 8.95242 9.14752 7.89752C10.2024 6.84263 11.6332 6.25 13.125 6.25C14.6168 6.25 16.0476 6.84263 17.1025 7.89752C18.1574 8.95242 18.75 10.3832 18.75 11.875ZM8.75 11.875C8.75 13.0353 9.21094 14.1481 10.0314 14.9686C10.8519 15.7891 11.9647 16.25 13.125 16.25C14.2853 16.25 15.3981 15.7891 16.2186 14.9686C17.0391 14.1481 17.5 13.0353 17.5 11.875C17.5 10.7147 17.0391 9.60188 16.2186 8.78141C15.3981 7.96094 14.2853 7.5 13.125 7.5C11.9647 7.5 10.8519 7.96094 10.0314 8.78141C9.21094 9.60188 8.75 10.7147 8.75 11.875Z"
      fill="#3463FC"
    />
    <defs>
      <linearGradient
        id="paint0_linear_14701_101784"
        x1="8.75"
        y1="2.5"
        x2="8.75"
        y2="16.25"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#D169FF" />
        <stop offset="1" stop-color="#AD30E5" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_14701_101784"
        x1="13.125"
        y1="10"
        x2="13.125"
        y2="13.75"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#D169FF" />
        <stop offset="1" stop-color="#AD30E5" />
      </linearGradient>
      <linearGradient
        id="paint2_linear_14701_101784"
        x1="13.125"
        y1="6.25"
        x2="13.125"
        y2="17.5"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#D169FF" />
        <stop offset="1" stop-color="#AD30E5" />
      </linearGradient>
    </defs>
  </svg>
);
// 语法纠错-Grammar correction
export const GrammarCorrectionIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <path
      d="M7.71906 1C9.00796 1.00017 10.2696 1.37105 11.3536 2.06841C12.4375 2.76577 13.298 3.76018 13.8325 4.93306C14.3669 6.10593 14.5527 7.40778 14.3678 8.68334C14.1828 9.9589 13.6349 11.1543 12.7893 12.1271L14.4186 13.8142C14.473 13.8667 14.5163 13.9296 14.5459 13.9991C14.5754 14.0687 14.5907 14.1434 14.5907 14.219C14.5907 14.2946 14.5755 14.3694 14.546 14.439C14.5165 14.5085 14.4733 14.5714 14.4189 14.6239C14.3646 14.6764 14.3002 14.7175 14.2297 14.7445C14.1591 14.7716 14.0838 14.7842 14.0083 14.7816C13.9328 14.7789 13.8586 14.7611 13.7901 14.7291C13.7216 14.6972 13.6603 14.6517 13.6097 14.5956L11.9831 12.9111C11.1507 13.5949 10.166 14.0685 9.11225 14.292C8.05845 14.5155 6.96636 14.4824 5.92805 14.1954C4.88975 13.9084 3.93567 13.3759 3.14625 12.643C2.35683 11.91 1.75523 10.9979 1.39216 9.98372C1.02908 8.96951 0.915189 7.88287 1.06007 6.81541C1.20496 5.74796 1.60438 4.73099 2.22465 3.85025C2.84493 2.96951 3.66787 2.25082 4.6241 1.75477C5.58034 1.25872 6.64182 0.999852 7.71906 1ZM7.71906 2.12533C6.2355 2.12533 4.8127 2.71468 3.76366 3.76371C2.71463 4.81275 2.12528 6.23555 2.12528 7.71911C2.12528 9.20267 2.71463 10.6255 3.76366 11.6745C4.8127 12.7235 6.2355 13.3129 7.71906 13.3129C9.20262 13.3129 10.6254 12.7235 11.6745 11.6745C12.7235 10.6255 13.3128 9.20267 13.3128 7.71911C13.3128 6.23555 12.7235 4.81275 11.6745 3.76371C10.6254 2.71468 9.20262 2.12533 7.71906 2.12533ZM10.6168 4.82133C10.8337 5.03822 10.8364 5.39022 10.6231 5.60978L8.51373 7.71822L10.6168 9.82133L10.6231 9.82756C10.7244 9.93454 10.7797 10.077 10.7771 10.2243C10.7745 10.3716 10.7142 10.512 10.6092 10.6154C10.5042 10.7187 10.3629 10.7768 10.2156 10.7771C10.0682 10.7774 9.92667 10.7198 9.82128 10.6169L7.71817 8.51378L5.61684 10.6169C5.51217 10.7233 5.3695 10.7838 5.22022 10.7851C5.07094 10.7863 4.92728 10.7282 4.82084 10.6236C4.7144 10.5189 4.6539 10.3762 4.65265 10.2269C4.6514 10.0777 4.7095 9.934 4.81417 9.82756L4.82128 9.82133L6.92262 7.71822L4.81417 5.60978C4.72383 5.51619 4.6687 5.3942 4.65817 5.26455C4.64763 5.1349 4.68234 5.00561 4.7564 4.89867L4.82128 4.82133C4.9268 4.71587 5.06988 4.65662 5.21906 4.65662C5.36825 4.65662 5.51133 4.71587 5.61684 4.82133L7.71817 6.92267L9.82128 4.82133C9.9268 4.71587 10.0699 4.65662 10.2191 4.65662C10.3682 4.65662 10.5113 4.71587 10.6168 4.82133Z"
      fill="#333333"
    />
    <path
      d="M7.71906 1C9.00796 1.00017 10.2696 1.37105 11.3536 2.06841C12.4375 2.76577 13.298 3.76018 13.8325 4.93306C14.3669 6.10593 14.5527 7.40778 14.3678 8.68334C14.1828 9.9589 13.6349 11.1543 12.7893 12.1271L14.4186 13.8142C14.473 13.8667 14.5163 13.9296 14.5459 13.9991C14.5754 14.0687 14.5907 14.1434 14.5907 14.219C14.5907 14.2946 14.5755 14.3694 14.546 14.439C14.5165 14.5085 14.4733 14.5714 14.4189 14.6239C14.3646 14.6764 14.3002 14.7175 14.2297 14.7445C14.1591 14.7716 14.0838 14.7842 14.0083 14.7816C13.9328 14.7789 13.8586 14.7611 13.7901 14.7291C13.7216 14.6972 13.6603 14.6517 13.6097 14.5956L11.9831 12.9111C11.1507 13.5949 10.166 14.0685 9.11225 14.292C8.05845 14.5155 6.96636 14.4824 5.92805 14.1954C4.88975 13.9084 3.93567 13.3759 3.14625 12.643C2.35683 11.91 1.75523 10.9979 1.39216 9.98372C1.02908 8.96951 0.915189 7.88287 1.06007 6.81541C1.20496 5.74796 1.60438 4.73099 2.22465 3.85025C2.84493 2.96951 3.66787 2.25082 4.6241 1.75477C5.58034 1.25872 6.64182 0.999852 7.71906 1ZM7.71906 2.12533C6.2355 2.12533 4.8127 2.71468 3.76366 3.76371C2.71463 4.81275 2.12528 6.23555 2.12528 7.71911C2.12528 9.20267 2.71463 10.6255 3.76366 11.6745C4.8127 12.7235 6.2355 13.3129 7.71906 13.3129C9.20262 13.3129 10.6254 12.7235 11.6745 11.6745C12.7235 10.6255 13.3128 9.20267 13.3128 7.71911C13.3128 6.23555 12.7235 4.81275 11.6745 3.76371C10.6254 2.71468 9.20262 2.12533 7.71906 2.12533ZM10.6168 4.82133C10.8337 5.03822 10.8364 5.39022 10.6231 5.60978L8.51373 7.71822L10.6168 9.82133L10.6231 9.82756C10.7244 9.93454 10.7797 10.077 10.7771 10.2243C10.7745 10.3716 10.7142 10.512 10.6092 10.6154C10.5042 10.7187 10.3629 10.7768 10.2156 10.7771C10.0682 10.7774 9.92667 10.7198 9.82128 10.6169L7.71817 8.51378L5.61684 10.6169C5.51217 10.7233 5.3695 10.7838 5.22022 10.7851C5.07094 10.7863 4.92728 10.7282 4.82084 10.6236C4.7144 10.5189 4.6539 10.3762 4.65265 10.2269C4.6514 10.0777 4.7095 9.934 4.81417 9.82756L4.82128 9.82133L6.92262 7.71822L4.81417 5.60978C4.72383 5.51619 4.6687 5.3942 4.65817 5.26455C4.64763 5.1349 4.68234 5.00561 4.7564 4.89867L4.82128 4.82133C4.9268 4.71587 5.06988 4.65662 5.21906 4.65662C5.36825 4.65662 5.51133 4.71587 5.61684 4.82133L7.71817 6.92267L9.82128 4.82133C9.9268 4.71587 10.0699 4.65662 10.2191 4.65662C10.3682 4.65662 10.5113 4.71587 10.6168 4.82133Z"
      fill="url(#paint0_linear_14706_103811)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_14706_103811"
        x1="0.999024"
        y1="7.89095"
        x2="10.4167"
        y2="1.62125"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3463FC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
    </defs>
  </svg>
);
export const GrammarCorrectionNormalIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <path
      d="M7.71906 1C9.00796 1.00017 10.2696 1.37105 11.3536 2.06841C12.4375 2.76577 13.298 3.76018 13.8325 4.93306C14.3669 6.10593 14.5527 7.40778 14.3678 8.68334C14.1828 9.9589 13.6349 11.1543 12.7893 12.1271L14.4186 13.8142C14.473 13.8667 14.5163 13.9296 14.5459 13.9991C14.5754 14.0687 14.5907 14.1434 14.5907 14.219C14.5907 14.2946 14.5755 14.3694 14.546 14.439C14.5165 14.5085 14.4733 14.5714 14.4189 14.6239C14.3646 14.6764 14.3002 14.7175 14.2297 14.7445C14.1591 14.7716 14.0838 14.7842 14.0083 14.7816C13.9328 14.7789 13.8586 14.7611 13.7901 14.7291C13.7216 14.6972 13.6603 14.6517 13.6097 14.5956L11.9831 12.9111C11.1507 13.5949 10.166 14.0685 9.11225 14.292C8.05845 14.5155 6.96636 14.4824 5.92805 14.1954C4.88975 13.9084 3.93567 13.3759 3.14625 12.643C2.35683 11.91 1.75523 10.9979 1.39216 9.98372C1.02908 8.96951 0.915189 7.88287 1.06007 6.81541C1.20496 5.74796 1.60438 4.73099 2.22465 3.85025C2.84493 2.96951 3.66787 2.25082 4.6241 1.75477C5.58034 1.25872 6.64182 0.999852 7.71906 1ZM7.71906 2.12533C6.2355 2.12533 4.8127 2.71468 3.76366 3.76371C2.71463 4.81275 2.12528 6.23555 2.12528 7.71911C2.12528 9.20267 2.71463 10.6255 3.76366 11.6745C4.8127 12.7235 6.2355 13.3129 7.71906 13.3129C9.20262 13.3129 10.6254 12.7235 11.6745 11.6745C12.7235 10.6255 13.3128 9.20267 13.3128 7.71911C13.3128 6.23555 12.7235 4.81275 11.6745 3.76371C10.6254 2.71468 9.20262 2.12533 7.71906 2.12533ZM10.6168 4.82133C10.8337 5.03822 10.8364 5.39022 10.6231 5.60978L8.51373 7.71822L10.6168 9.82133L10.6231 9.82756C10.7244 9.93454 10.7797 10.077 10.7771 10.2243C10.7745 10.3716 10.7142 10.512 10.6092 10.6154C10.5042 10.7187 10.3629 10.7768 10.2156 10.7771C10.0682 10.7774 9.92667 10.7198 9.82128 10.6169L7.71817 8.51378L5.61684 10.6169C5.51217 10.7233 5.3695 10.7838 5.22022 10.7851C5.07094 10.7863 4.92728 10.7282 4.82084 10.6236C4.7144 10.5189 4.6539 10.3762 4.65265 10.2269C4.6514 10.0777 4.7095 9.934 4.81417 9.82756L4.82128 9.82133L6.92262 7.71822L4.81417 5.60978C4.72383 5.51619 4.6687 5.3942 4.65817 5.26455C4.64763 5.1349 4.68234 5.00561 4.7564 4.89867L4.82128 4.82133C4.9268 4.71587 5.06988 4.65662 5.21906 4.65662C5.36825 4.65662 5.51133 4.71587 5.61684 4.82133L7.71817 6.92267L9.82128 4.82133C9.9268 4.71587 10.0699 4.65662 10.2191 4.65662C10.3682 4.65662 10.5113 4.71587 10.6168 4.82133Z"
      fill="#999999"
    />
  </svg>
);
// 温柔-gentle
export const GentleIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <path
      d="M13.6528 2.3801C12.1416 0.880581 10.1339 0.0541992 7.99612 0.0541992C5.85839 0.0541992 3.85063 0.880581 2.34142 2.3801C0.832202 3.87961 0 5.87767 0 7.99988C0 10.1221 0.832202 12.1182 2.34142 13.6197C3.85063 15.1211 5.86033 15.9456 7.99612 15.9456C10.1339 15.9456 12.1435 15.1192 13.6528 13.6177C16.7701 10.5198 16.7701 5.48 13.6528 2.3801ZM12.9156 12.8845C11.6023 14.19 9.85451 14.9097 7.99612 14.9097C6.13773 14.9097 4.39185 14.19 3.07856 12.8864C1.76528 11.5809 1.04171 9.84663 1.04171 7.99988C1.04171 6.15313 1.76722 4.41889 3.07856 3.11336C4.39379 1.80784 6.13967 1.09009 7.99612 1.09009C9.85257 1.09009 11.6004 1.80784 12.9137 3.11336C15.6256 5.80784 15.6256 10.1919 12.9156 12.8845Z"
      fill="url(#paint0_linear_14706_103959)"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M13.4765 13.4404C16.4955 10.4402 16.4956 5.55956 13.4765 2.55737C12.0123 1.10458 10.068 0.304199 7.99612 0.304199C5.92413 0.304199 3.9798 1.10467 2.51762 2.55744C1.05553 4.01014 0.25 5.94441 0.25 7.99988C0.25 10.0553 1.05547 11.9877 2.51774 13.4424C3.97974 14.8969 5.9259 15.6956 7.99612 15.6956C10.0682 15.6956 12.0145 14.895 13.4765 13.4404ZM2.90231 13.0637C1.54203 11.7115 0.791707 9.9135 0.791707 7.99988C0.791707 6.08611 1.54408 4.28827 2.90218 2.9362C4.26439 1.58403 6.0739 0.840087 7.99612 0.840087C9.91823 0.840087 11.7295 1.5837 13.0899 2.93607C15.9002 5.72825 15.9002 10.2715 13.0918 13.0618C11.7315 14.414 9.92029 15.1597 7.99612 15.1597C6.07187 15.1597 4.26252 14.4139 2.90231 13.0637ZM12.9156 12.8845C15.6256 10.1919 15.6256 5.80784 12.9137 3.11336C11.6004 1.80784 9.85257 1.09009 7.99612 1.09009C6.13967 1.09009 4.39379 1.80784 3.07856 3.11336C1.76722 4.41889 1.04171 6.15313 1.04171 7.99988C1.04171 9.84663 1.76528 11.5809 3.07856 12.8864C4.39185 14.19 6.13773 14.9097 7.99612 14.9097C9.85451 14.9097 11.6023 14.19 12.9156 12.8845ZM7.99612 0.0541992C10.1339 0.0541992 12.1416 0.880581 13.6528 2.3801C16.7701 5.48 16.7701 10.5198 13.6528 13.6177C12.1435 15.1192 10.1339 15.9456 7.99612 15.9456C5.86033 15.9456 3.85063 15.1211 2.34142 13.6197C0.832202 12.1182 0 10.1221 0 7.99988C0 5.87767 0.832202 3.87961 2.34142 2.3801C3.85063 0.880581 5.85839 0.0541992 7.99612 0.0541992Z"
      fill="url(#paint1_linear_14706_103959)"
    />
    <path
      d="M5.14662 5.03784C4.60734 5.03784 4.16699 5.47431 4.16699 6.01165C4.16699 6.549 4.6054 6.98547 5.14662 6.98547C5.68785 6.98547 6.12626 6.549 6.12626 6.01165C6.12626 5.47237 5.68785 5.03784 5.14662 5.03784Z"
      fill="url(#paint2_linear_14706_103959)"
    />
    <path
      d="M10.8439 5.03784C10.3046 5.03784 9.86426 5.47431 9.86426 6.01165C9.86426 6.549 10.3027 6.98353 10.8439 6.98353C11.3851 6.98353 11.8235 6.54706 11.8235 6.00971C11.8235 5.47237 11.3851 5.03784 10.8439 5.03784Z"
      fill="url(#paint3_linear_14706_103959)"
    />
    <path
      d="M11.7673 9.48218C11.5869 9.48218 11.4162 9.59275 11.3483 9.77316C10.8284 11.166 9.48019 12.101 7.99425 12.101C6.50832 12.101 5.16011 11.166 4.64023 9.77316C4.55487 9.54231 4.29687 9.42398 4.06603 9.51128C3.83518 9.59663 3.71685 9.85463 3.80415 10.0855C4.12034 10.9313 4.67709 11.6548 5.41617 12.1766C6.17272 12.7121 7.06506 12.9953 7.99425 12.9953C8.92345 12.9953 9.81578 12.7101 10.5723 12.1766C11.3114 11.6529 11.8701 10.9313 12.1844 10.0855C12.2697 9.85463 12.1533 9.59857 11.9225 9.51128C11.872 9.49188 11.8197 9.48218 11.7673 9.48218Z"
      fill="url(#paint4_linear_14706_103959)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_14706_103959"
        x1="1.04765e-07"
        y1="7.99988"
        x2="10.942"
        y2="0.567256"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3463FC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_14706_103959"
        x1="1.04765e-07"
        y1="7.99988"
        x2="10.942"
        y2="0.567256"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3463FC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <linearGradient
        id="paint2_linear_14706_103959"
        x1="1.04765e-07"
        y1="7.99988"
        x2="10.942"
        y2="0.567256"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3463FC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <linearGradient
        id="paint3_linear_14706_103959"
        x1="1.04765e-07"
        y1="7.99988"
        x2="10.942"
        y2="0.567256"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3463FC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <linearGradient
        id="paint4_linear_14706_103959"
        x1="1.04765e-07"
        y1="7.99988"
        x2="10.942"
        y2="0.567256"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3463FC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
    </defs>
  </svg>
);
export const GentleNormalIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_15173_17924)">
      <path
        d="M13.5647 13.5291L13.5646 13.5291C12.079 15.0071 10.101 15.8206 7.99612 15.8206C5.89311 15.8206 3.91519 15.009 2.42958 13.5311C0.943836 12.053 0.125 10.0887 0.125 7.99988C0.125 5.91104 0.943867 3.94487 2.42952 2.46877C3.91521 0.992624 5.89126 0.179199 7.99612 0.179199C10.1009 0.179199 12.077 0.992582 13.5646 2.46873C16.6329 5.51978 16.6328 10.48 13.5647 13.5291ZM2.99044 12.975L2.9905 12.9751C4.32725 14.302 6.1048 15.0347 7.99612 15.0347C9.8874 15.0347 11.6669 14.302 13.0037 12.9731C15.7629 10.2317 15.7629 5.76805 13.0018 3.02472C11.665 1.69577 9.8854 0.965087 7.99612 0.965087C6.10679 0.965087 4.32922 1.6958 2.9905 3.02465L2.99037 3.02478C1.65565 4.35358 0.916707 6.11962 0.916707 7.99988C0.916707 9.88006 1.65366 11.6462 2.99044 12.975Z"
        fill="#999999"
        stroke="#999999"
        stroke-width="0.25"
      />
      <path
        d="M5.14662 5.03784C4.60734 5.03784 4.16699 5.47431 4.16699 6.01165C4.16699 6.549 4.6054 6.98547 5.14662 6.98547C5.68785 6.98547 6.12626 6.549 6.12626 6.01165C6.12626 5.47237 5.68785 5.03784 5.14662 5.03784Z"
        fill="#999999"
      />
      <path
        d="M10.8439 5.03784C10.3046 5.03784 9.86426 5.47431 9.86426 6.01165C9.86426 6.549 10.3027 6.98353 10.8439 6.98353C11.3851 6.98353 11.8235 6.54706 11.8235 6.00971C11.8235 5.47237 11.3851 5.03784 10.8439 5.03784Z"
        fill="#999999"
      />
      <path
        d="M11.7673 9.48218C11.5869 9.48218 11.4162 9.59275 11.3483 9.77316C10.8284 11.166 9.48019 12.101 7.99425 12.101C6.50832 12.101 5.16011 11.166 4.64023 9.77316C4.55487 9.54231 4.29687 9.42398 4.06603 9.51128C3.83518 9.59663 3.71685 9.85463 3.80415 10.0855C4.12034 10.9313 4.67709 11.6548 5.41617 12.1766C6.17272 12.7121 7.06506 12.9953 7.99425 12.9953C8.92345 12.9953 9.81579 12.7101 10.5723 12.1766C11.3114 11.6529 11.8701 10.9313 12.1844 10.0855C12.2697 9.85463 12.1533 9.59857 11.9225 9.51128C11.872 9.49188 11.8197 9.48218 11.7673 9.48218Z"
        fill="#999999"
      />
    </g>
    <defs>
      <clipPath id="clip0_15173_17924">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
// 中立-neutrality
export const NeutralityIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <g clip-path="url(#clip0_14706_103822)">
      <path
        d="M8 0C3.55573 0 0 3.55573 0 8C0 12.4443 3.55573 16 8 16C12.4443 16 16 12.4443 16 8C16 3.55573 12.4443 0 8 0ZM8 14.6667C4.35573 14.6667 1.33333 11.6443 1.33333 8C1.33333 4.35573 4.35573 1.33333 8 1.33333C11.6443 1.33333 14.6667 4.35573 14.6667 8C14.6667 11.6443 11.6443 14.6667 8 14.6667Z"
        fill="#333333"
      />
      <path
        d="M8 0C3.55573 0 0 3.55573 0 8C0 12.4443 3.55573 16 8 16C12.4443 16 16 12.4443 16 8C16 3.55573 12.4443 0 8 0ZM8 14.6667C4.35573 14.6667 1.33333 11.6443 1.33333 8C1.33333 4.35573 4.35573 1.33333 8 1.33333C11.6443 1.33333 14.6667 4.35573 14.6667 8C14.6667 11.6443 11.6443 14.6667 8 14.6667Z"
        fill="url(#paint0_linear_14706_103822)"
      />
      <path
        d="M4.44434 5.86666C4.44434 6.31093 4.80007 6.75573 5.24434 6.84426C5.68914 7.0224 6.22247 6.75573 6.40007 6.31093C6.57767 5.86666 6.40007 5.33333 6.04434 5.06666C5.68914 4.8 5.06674 4.8 4.711 5.15573C4.5334 5.33333 4.44434 5.6 4.44434 5.86666ZM9.511 5.86666C9.511 6.31093 9.86674 6.75573 10.311 6.84426C10.7558 6.93333 11.2891 6.66666 11.4667 6.2224C11.6443 5.7776 11.4667 5.24426 11.111 4.9776C10.7558 4.71093 10.1334 4.8 9.86674 5.06666C9.68914 5.33333 9.511 5.6 9.511 5.86666ZM5.34567 9.7776H10.7147C10.8562 9.7776 10.9918 9.83379 11.0919 9.9338C11.1919 10.0338 11.2481 10.1695 11.2481 10.3109C11.2481 10.4524 11.1919 10.588 11.0919 10.6881C10.9918 10.7881 10.8562 10.8443 10.7147 10.8443H5.3462C5.20475 10.8443 5.0691 10.7881 4.96908 10.6881C4.86906 10.588 4.81287 10.4524 4.81287 10.3109C4.81287 10.1695 4.86906 10.0338 4.96908 9.9338C5.0691 9.83379 5.20475 9.7776 5.3462 9.7776H5.34567Z"
        fill="#333333"
      />
      <path
        d="M4.44434 5.86666C4.44434 6.31093 4.80007 6.75573 5.24434 6.84426C5.68914 7.0224 6.22247 6.75573 6.40007 6.31093C6.57767 5.86666 6.40007 5.33333 6.04434 5.06666C5.68914 4.8 5.06674 4.8 4.711 5.15573C4.5334 5.33333 4.44434 5.6 4.44434 5.86666ZM9.511 5.86666C9.511 6.31093 9.86674 6.75573 10.311 6.84426C10.7558 6.93333 11.2891 6.66666 11.4667 6.2224C11.6443 5.7776 11.4667 5.24426 11.111 4.9776C10.7558 4.71093 10.1334 4.8 9.86674 5.06666C9.68914 5.33333 9.511 5.6 9.511 5.86666ZM5.34567 9.7776H10.7147C10.8562 9.7776 10.9918 9.83379 11.0919 9.9338C11.1919 10.0338 11.2481 10.1695 11.2481 10.3109C11.2481 10.4524 11.1919 10.588 11.0919 10.6881C10.9918 10.7881 10.8562 10.8443 10.7147 10.8443H5.3462C5.20475 10.8443 5.0691 10.7881 4.96908 10.6881C4.86906 10.588 4.81287 10.4524 4.81287 10.3109C4.81287 10.1695 4.86906 10.0338 4.96908 9.9338C5.0691 9.83379 5.20475 9.7776 5.3462 9.7776H5.34567Z"
        fill="url(#paint1_linear_14706_103822)"
      />
    </g>
    <defs>
      <linearGradient
        id="paint0_linear_14706_103822"
        x1="1.04826e-07"
        y1="8"
        x2="10.9913"
        y2="0.580287"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3463FC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_14706_103822"
        x1="4.44434"
        y1="7.83046"
        x2="8.79301"
        y2="4.37646"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3463FC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <clipPath id="clip0_14706_103822">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
export const NeutralityNormalIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <g clip-path="url(#clip0_14701_100570)">
      <path
        d="M8 0C3.55573 0 0 3.55573 0 8C0 12.4443 3.55573 16 8 16C12.4443 16 16 12.4443 16 8C16 3.55573 12.4443 0 8 0ZM8 14.6667C4.35573 14.6667 1.33333 11.6443 1.33333 8C1.33333 4.35573 4.35573 1.33333 8 1.33333C11.6443 1.33333 14.6667 4.35573 14.6667 8C14.6667 11.6443 11.6443 14.6667 8 14.6667Z"
        fill="#999999"
      />
      <path
        d="M4.44434 5.86666C4.44434 6.31093 4.80007 6.75573 5.24434 6.84426C5.68914 7.0224 6.22247 6.75573 6.40007 6.31093C6.57767 5.86666 6.40007 5.33333 6.04434 5.06666C5.68914 4.8 5.06674 4.8 4.711 5.15573C4.5334 5.33333 4.44434 5.6 4.44434 5.86666ZM9.511 5.86666C9.511 6.31093 9.86674 6.75573 10.311 6.84426C10.7558 6.93333 11.2891 6.66666 11.4667 6.2224C11.6443 5.7776 11.4667 5.24426 11.111 4.9776C10.7558 4.71093 10.1334 4.8 9.86674 5.06666C9.68914 5.33333 9.511 5.6 9.511 5.86666ZM5.34567 9.7776H10.7147C10.8562 9.7776 10.9918 9.83379 11.0919 9.9338C11.1919 10.0338 11.2481 10.1695 11.2481 10.3109C11.2481 10.4524 11.1919 10.588 11.0919 10.6881C10.9918 10.7881 10.8562 10.8443 10.7147 10.8443H5.3462C5.20475 10.8443 5.0691 10.7881 4.96908 10.6881C4.86906 10.588 4.81287 10.4524 4.81287 10.3109C4.81287 10.1695 4.86906 10.0338 4.96908 9.9338C5.0691 9.83379 5.20475 9.7776 5.3462 9.7776H5.34567Z"
        fill="#999999"
      />
    </g>
    <defs>
      <clipPath id="clip0_14701_100570">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
// 正式-formal
export const FormalIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <g clip-path="url(#clip0_14706_103827)">
      <path
        d="M14.1803 13.2086V13.2088C14.1803 13.4117 14.0759 13.6256 13.909 13.7915C13.7421 13.9576 13.528 14.0603 13.3286 14.0603H2.80744C2.60452 14.0603 2.39056 13.9559 2.2246 13.7891C2.05854 13.6222 1.95578 13.4082 1.95578 13.2088V7.97774H6.19208V8.82932C6.19208 9.38731 6.6455 9.83175 7.19451 9.83175H8.94936C9.50735 9.83175 9.95179 9.37833 9.95179 8.82932V7.97774H14.1878L14.1803 13.2086ZM14.1797 7.04112H9.94398V6.19711C9.94398 5.63905 9.49064 5.19468 8.94163 5.19468H7.18677C6.63081 5.19468 6.17676 5.63835 6.17676 6.19711V7.04869H1.94054V5.31968C1.94054 5.11677 2.04493 4.90284 2.21174 4.73689C2.37864 4.57086 2.59272 4.46811 2.79212 4.46811H13.321C13.5239 4.46811 13.7378 4.5725 13.9038 4.73933C14.0698 4.90624 14.1725 5.12032 14.1725 5.31968L14.1725 5.32021L14.1797 7.04112ZM13.321 3.43991H10.8137V2.4662C10.8137 1.535 9.95557 0.807617 8.93397 0.807617H7.17911C6.16547 0.807617 5.29925 1.53458 5.29925 2.4662V3.43983H2.79978C1.76975 3.43983 0.919922 4.28138 0.919922 5.31968V13.2087C0.919922 14.2388 1.7614 15.0885 2.79978 15.0885H13.321C14.351 15.0885 15.2008 14.247 15.2008 13.2087V5.31968C15.2008 4.28173 14.359 3.43991 13.321 3.43991ZM6.33511 2.31361C6.33511 2.19545 6.42022 2.0647 6.58873 1.95613C6.75361 1.8499 6.97427 1.78245 7.18677 1.78245H8.94163C9.1534 1.78245 9.37398 1.85143 9.53921 1.95875C9.70864 2.06879 9.79321 2.19969 9.79321 2.31361V3.43225H6.33511V2.31361ZM9.01501 8.90234L7.11339 8.89557V6.12374H9.01501V8.90234Z"
        fill="#333333"
      />
      <path
        d="M14.1803 13.2086V13.2088C14.1803 13.4117 14.0759 13.6256 13.909 13.7915C13.7421 13.9576 13.528 14.0603 13.3286 14.0603H2.80744C2.60452 14.0603 2.39056 13.9559 2.2246 13.7891C2.05854 13.6222 1.95578 13.4082 1.95578 13.2088V7.97774H6.19208V8.82932C6.19208 9.38731 6.6455 9.83175 7.19451 9.83175H8.94936C9.50735 9.83175 9.95179 9.37833 9.95179 8.82932V7.97774H14.1878L14.1803 13.2086ZM14.1797 7.04112H9.94398V6.19711C9.94398 5.63905 9.49064 5.19468 8.94163 5.19468H7.18677C6.63081 5.19468 6.17676 5.63835 6.17676 6.19711V7.04869H1.94054V5.31968C1.94054 5.11677 2.04493 4.90284 2.21174 4.73689C2.37864 4.57086 2.59272 4.46811 2.79212 4.46811H13.321C13.5239 4.46811 13.7378 4.5725 13.9038 4.73933C14.0698 4.90624 14.1725 5.12032 14.1725 5.31968L14.1725 5.32021L14.1797 7.04112ZM13.321 3.43991H10.8137V2.4662C10.8137 1.535 9.95557 0.807617 8.93397 0.807617H7.17911C6.16547 0.807617 5.29925 1.53458 5.29925 2.4662V3.43983H2.79978C1.76975 3.43983 0.919922 4.28138 0.919922 5.31968V13.2087C0.919922 14.2388 1.7614 15.0885 2.79978 15.0885H13.321C14.351 15.0885 15.2008 14.247 15.2008 13.2087V5.31968C15.2008 4.28173 14.359 3.43991 13.321 3.43991ZM6.33511 2.31361C6.33511 2.19545 6.42022 2.0647 6.58873 1.95613C6.75361 1.8499 6.97427 1.78245 7.18677 1.78245H8.94163C9.1534 1.78245 9.37398 1.85143 9.53921 1.95875C9.70864 2.06879 9.79321 2.19969 9.79321 2.31361V3.43225H6.33511V2.31361ZM9.01501 8.90234L7.11339 8.89557V6.12374H9.01501V8.90234Z"
        fill="url(#paint0_linear_14706_103827)"
      />
      <path
        d="M14.1803 13.2086V13.2088C14.1803 13.4117 14.0759 13.6256 13.909 13.7915C13.7421 13.9576 13.528 14.0603 13.3286 14.0603H2.80744C2.60452 14.0603 2.39056 13.9559 2.2246 13.7891C2.05854 13.6222 1.95578 13.4082 1.95578 13.2088V7.97774H6.19208V8.82932C6.19208 9.38731 6.6455 9.83175 7.19451 9.83175H8.94936C9.50735 9.83175 9.95179 9.37833 9.95179 8.82932V7.97774H14.1878L14.1803 13.2086ZM14.1797 7.04112H9.94398V6.19711C9.94398 5.63905 9.49064 5.19468 8.94163 5.19468H7.18677C6.63081 5.19468 6.17676 5.63835 6.17676 6.19711V7.04869H1.94054V5.31968C1.94054 5.11677 2.04493 4.90284 2.21174 4.73689C2.37864 4.57086 2.59272 4.46811 2.79212 4.46811H13.321C13.5239 4.46811 13.7378 4.5725 13.9038 4.73933C14.0698 4.90624 14.1725 5.12032 14.1725 5.31968L14.1725 5.32021L14.1797 7.04112ZM13.321 3.43991H10.8137V2.4662C10.8137 1.535 9.95557 0.807617 8.93397 0.807617H7.17911C6.16547 0.807617 5.29925 1.53458 5.29925 2.4662V3.43983H2.79978C1.76975 3.43983 0.919922 4.28138 0.919922 5.31968V13.2087C0.919922 14.2388 1.7614 15.0885 2.79978 15.0885H13.321C14.351 15.0885 15.2008 14.247 15.2008 13.2087V5.31968C15.2008 4.28173 14.359 3.43991 13.321 3.43991ZM6.33511 2.31361C6.33511 2.19545 6.42022 2.0647 6.58873 1.95613C6.75361 1.8499 6.97427 1.78245 7.18677 1.78245H8.94163C9.1534 1.78245 9.37398 1.85143 9.53921 1.95875C9.70864 2.06879 9.79321 2.19969 9.79321 2.31361V3.43225H6.33511V2.31361ZM9.01501 8.90234L7.11339 8.89557V6.12374H9.01501V8.90234Z"
        stroke="#333333"
        stroke-width="0.25"
      />
    </g>
    <defs>
      <linearGradient
        id="paint0_linear_14706_103827"
        x1="1.04492"
        y1="7.94806"
        x2="10.6835"
        y2="1.44149"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3463FC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <clipPath id="clip0_14706_103827">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
export const FormalNormalIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <g clip-path="url(#clip0_14701_100567)">
      <path
        d="M14.1803 13.2086V13.2088C14.1803 13.4117 14.0759 13.6256 13.909 13.7915C13.7421 13.9576 13.528 14.0603 13.3286 14.0603H2.80744C2.60452 14.0603 2.39056 13.9559 2.2246 13.7891C2.05854 13.6222 1.95578 13.4082 1.95578 13.2088V7.97774H6.19208V8.82932C6.19208 9.38731 6.6455 9.83175 7.19451 9.83175H8.94936C9.50735 9.83175 9.95179 9.37833 9.95179 8.82932V7.97774H14.1878L14.1803 13.2086ZM14.1797 7.04112H9.94398V6.19711C9.94398 5.63905 9.49064 5.19468 8.94163 5.19468H7.18677C6.63081 5.19468 6.17676 5.63835 6.17676 6.19711V7.04869H1.94054V5.31968C1.94054 5.11677 2.04493 4.90284 2.21174 4.73689C2.37864 4.57086 2.59272 4.46811 2.79212 4.46811H13.321C13.5239 4.46811 13.7378 4.5725 13.9038 4.73933C14.0698 4.90624 14.1725 5.12032 14.1725 5.31968L14.1725 5.32021L14.1797 7.04112ZM13.321 3.43991H10.8137V2.4662C10.8137 1.535 9.95557 0.807617 8.93397 0.807617H7.17911C6.16547 0.807617 5.29925 1.53458 5.29925 2.4662V3.43983H2.79978C1.76975 3.43983 0.919922 4.28138 0.919922 5.31968V13.2087C0.919922 14.2388 1.7614 15.0885 2.79978 15.0885H13.321C14.351 15.0885 15.2008 14.247 15.2008 13.2087V5.31968C15.2008 4.28173 14.359 3.43991 13.321 3.43991ZM6.33511 2.31361C6.33511 2.19545 6.42022 2.0647 6.58873 1.95613C6.75361 1.8499 6.97427 1.78245 7.18677 1.78245H8.94163C9.1534 1.78245 9.37398 1.85143 9.53921 1.95875C9.70864 2.06879 9.79321 2.19969 9.79321 2.31361V3.43225H6.33511V2.31361ZM9.01501 8.90234L7.11339 8.89557V6.12374H9.01501V8.90234Z"
        fill="#999999"
        stroke="#999999"
        stroke-width="0.25"
      />
    </g>
    <defs>
      <clipPath id="clip0_14701_100567">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
// 添加附件
export const AttachmentIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <rect opacity="0.01" width="16" height="16" fill="#999999" />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M12.4578 3.10391C10.9859 1.63203 8.58906 1.63203 7.11875 3.10391L3.04062 7.17891C3.01406 7.20547 3 7.24141 3 7.27891C3 7.31641 3.01406 7.35234 3.04062 7.37891L3.61719 7.95547C3.67188 8.01016 3.76094 8.01016 3.81563 7.95547L7.89375 3.88047C8.4 3.37422 9.07344 3.09609 9.78906 3.09609C10.5047 3.09609 11.1781 3.37422 11.6828 3.88047C12.1891 4.38672 12.4672 5.06016 12.4672 5.77422C12.4672 6.48984 12.1891 7.16172 11.6828 7.66797L7.52656 11.8227L6.85313 12.4961C6.22344 13.1258 5.2 13.1258 4.57031 12.4961C4.26562 12.1914 4.09844 11.7867 4.09844 11.3555C4.09844 10.9242 4.26562 10.5195 4.57031 10.2148L8.69375 6.09297C8.79844 5.98984 8.93594 5.93203 9.08281 5.93203H9.08437C9.23125 5.93203 9.36719 5.98984 9.47031 6.09297C9.575 6.19766 9.63125 6.33516 9.63125 6.48203C9.63125 6.62734 9.57344 6.76484 9.47031 6.86797L6.1 10.2352C6.07344 10.2617 6.05938 10.2977 6.05938 10.3352C6.05938 10.3727 6.07344 10.4086 6.1 10.4352L6.67656 11.0117C6.73125 11.0664 6.82031 11.0664 6.875 11.0117L10.2438 7.64297C10.5547 7.33203 10.725 6.91953 10.725 6.48047C10.725 6.04141 10.5531 5.62734 10.2438 5.31797C9.60156 4.67578 8.55781 4.67734 7.91563 5.31797L7.51562 5.71953L3.79375 9.43984C3.28125 9.95234 3 10.632 3 11.357C3 12.0805 3.28281 12.7602 3.79375 13.2711C4.32344 13.7992 5.01719 14.0633 5.71094 14.0633C6.40469 14.0633 7.09844 13.7992 7.62656 13.2711L12.4578 8.44297C13.1687 7.73047 13.5625 6.78203 13.5625 5.77422C13.5641 4.76484 13.1703 3.81641 12.4578 3.10391Z"
      fill="#999999"
    />
  </svg>
);
// 发送草稿
export const SendDraftIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 16 16"
    fill="none"
  >
    <g clip-path="url(#clip0_14701_103458)">
      <path
        d="M13.6544 2.3456C15.2176 3.9104 16 5.7952 16 8C16 10.2064 15.2176 12.0912 13.6544 13.6544C12.0896 15.2176 10.2048 16 8 16C5.7952 16 3.9104 15.2176 2.3456 13.6544C0.7824 12.0912 0 10.2064 0 8C0 5.7952 0.7824 3.9104 2.3456 2.3456C3.9104 0.7824 5.7952 0 8 0C10.2048 0 12.0896 0.7824 13.6544 2.3456ZM12.808 8.2304L7.9232 3.3072L3 8.2304L3.8848 9.0768L7.3072 5.6544V13.1152H8.5376V5.6544L11.96 9.0768L12.808 8.2304Z"
        fill="#3463FC"
      />
    </g>
    <defs>
      <clipPath id="clip0_14701_103458">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
// 文档图标
export const DocumentFileIcon = () => (
  <svg
    style={{ float: 'left', marginRight: '10px' }}
    xmlns="http://www.w3.org/2000/svg"
    width="31"
    height="30"
    viewBox="0 0 31 30"
    fill="none"
  >
    <path
      d="M19.8125 1.25C19.9611 1.2503 20.1076 1.28563 20.24 1.35313L20.2813 1.37562C20.3242 1.40083 20.3654 1.42902 20.4044 1.46L20.42 1.47312L20.4631 1.5125L26.7381 7.7875L26.7575 7.80813L26.7625 7.81375L26.725 7.77437L26.7769 7.83063L26.7975 7.85563C26.817 7.88015 26.8354 7.9056 26.8525 7.93188L26.8744 7.96875L26.8969 8.00938C26.9631 8.13812 27 8.28313 27 8.4375V27.5C27 27.8315 26.8683 28.1495 26.6339 28.3839C26.3995 28.6183 26.0815 28.75 25.75 28.75H5.75C5.41848 28.75 5.10054 28.6183 4.86612 28.3839C4.6317 28.1495 4.5 27.8315 4.5 27.5V2.5C4.5 2.16848 4.6317 1.85054 4.86612 1.61612C5.10054 1.3817 5.41848 1.25 5.75 1.25H19.8125ZM17.625 3.125H6.375V26.875H25.125V10.625H18.875C18.5435 10.625 18.2255 10.4933 17.9911 10.2589C17.7567 10.0245 17.625 9.70652 17.625 9.375V3.125ZM16.6875 19.375C16.9361 19.375 17.1746 19.4738 17.3504 19.6496C17.5262 19.8254 17.625 20.0639 17.625 20.3125C17.625 20.5611 17.5262 20.7996 17.3504 20.9754C17.1746 21.1512 16.9361 21.25 16.6875 21.25H11.6875C11.4389 21.25 11.2004 21.1512 11.0246 20.9754C10.8488 20.7996 10.75 20.5611 10.75 20.3125C10.75 20.0639 10.8488 19.8254 11.0246 19.6496C11.2004 19.4738 11.4389 19.375 11.6875 19.375H16.6875ZM19.9169 13.75C20.3769 13.75 20.75 14.17 20.75 14.6875C20.75 15.205 20.3769 15.625 19.9169 15.625H11.5831C11.1231 15.625 10.75 15.205 10.75 14.6875C10.75 14.17 11.1231 13.75 11.5831 13.75H19.9169ZM19.5 3.20063V8.75H25.0494L19.5 3.20063Z"
      fill="#3463FC"
    />
  </svg>
);
// 下载附件
export const DownloadFileIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="17"
    height="16"
    viewBox="0 0 17 16"
    fill="none"
  >
    <path
      d="M14.9453 8.08984C14.7245 8.08984 14.5453 8.26904 14.5453 8.48984V10.5314C14.5453 11.7186 13.5797 12.6842 12.3925 12.6842H4.2653C3.0781 12.6842 2.1125 11.7186 2.1125 10.5314V8.48984C2.1125 8.26904 1.9333 8.08984 1.7125 8.08984C1.4917 8.08984 1.3125 8.26904 1.3125 8.48984V10.5314C1.3125 12.1594 2.6373 13.4842 4.2653 13.4842H12.3933C14.0213 13.4842 15.3461 12.1594 15.3461 10.5314V8.48984C15.3459 8.38368 15.3036 8.28194 15.2284 8.20695C15.1533 8.13196 15.0515 8.08984 14.9453 8.08984Z"
      fill="white"
    />
    <path
      d="M5.38846 7.02583C5.59486 7.02583 5.61166 7.02583 7.20926 8.57223C7.46286 8.81703 7.71406 9.06263 7.93726 9.28023V2.92983C7.93726 2.70903 8.11646 2.52983 8.33726 2.52983C8.55806 2.52983 8.73726 2.70903 8.73726 2.92983V9.39463L11.0045 7.14183C11.0829 7.06423 11.1845 7.02583 11.2861 7.02583C11.3893 7.02583 11.4917 7.06503 11.5701 7.14423C11.6071 7.18145 11.6365 7.22561 11.6565 7.27419C11.6765 7.32278 11.6867 7.37483 11.6866 7.42737C11.6864 7.4799 11.6759 7.53189 11.6557 7.58036C11.6354 7.62883 11.6058 7.67283 11.5685 7.70983L8.68286 10.577C8.60819 10.6513 8.50727 10.6931 8.40199 10.6934C8.29671 10.6937 8.19555 10.6524 8.12046 10.5786C6.86766 9.34663 5.44046 7.96823 5.19886 7.77783C5.07406 7.70983 4.98926 7.57783 4.98926 7.42583C4.98905 7.37331 4.99923 7.32127 5.0192 7.2727C5.03918 7.22412 5.06856 7.17998 5.10566 7.1428C5.14277 7.10563 5.18685 7.07616 5.23538 7.05608C5.28392 7.03601 5.33594 7.02573 5.38846 7.02583Z"
      fill="white"
    />
  </svg>
);
export const ChatGrammer = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
  >
    <path
      d="M6.875 8.5C6.875 8.73206 6.78281 8.95462 6.61872 9.11872C6.45462 9.28281 6.23206 9.375 6 9.375C5.76794 9.375 5.54538 9.28281 5.38128 9.11872C5.21719 8.95462 5.125 8.73206 5.125 8.5C5.125 8.26794 5.21719 8.04538 5.38128 7.88128C5.54538 7.71719 5.76794 7.625 6 7.625C6.23206 7.625 6.45462 7.71719 6.61872 7.88128C6.78281 8.04538 6.875 8.26794 6.875 8.5ZM6.875 6C6.875 6.23206 6.78281 6.45462 6.61872 6.61872C6.45462 6.78281 6.23206 6.875 6 6.875C5.76794 6.875 5.54538 6.78281 5.38128 6.61872C5.21719 6.45462 5.125 6.23206 5.125 6C5.125 5.76794 5.21719 5.54538 5.38128 5.38128C5.54538 5.21719 5.76794 5.125 6 5.125C6.23206 5.125 6.45462 5.21719 6.61872 5.38128C6.78281 5.54538 6.875 5.76794 6.875 6ZM6.875 3.5C6.875 3.73206 6.78281 3.95462 6.61872 4.11872C6.45462 4.28281 6.23206 4.375 6 4.375C5.76794 4.375 5.54538 4.28281 5.38128 4.11872C5.21719 3.95462 5.125 3.73206 5.125 3.5C5.125 3.26794 5.21719 3.04538 5.38128 2.88128C5.54538 2.71719 5.76794 2.625 6 2.625C6.23206 2.625 6.45462 2.71719 6.61872 2.88128C6.78281 3.04538 6.875 3.26794 6.875 3.5Z"
      fill="url(#paint0_linear_15163_17156)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_15163_17156"
        x1="6"
        y1="9.375"
        x2="3.74084"
        y2="8.50735"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#187CFC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
    </defs>
  </svg>
);

export const ChatGrammerSelected = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
  >
    <path
      d="M6.875 8.5C6.875 8.73206 6.78281 8.95462 6.61872 9.11872C6.45462 9.28281 6.23206 9.375 6 9.375C5.76794 9.375 5.54538 9.28281 5.38128 9.11872C5.21719 8.95462 5.125 8.73206 5.125 8.5C5.125 8.26794 5.21719 8.04538 5.38128 7.88128C5.54538 7.71719 5.76794 7.625 6 7.625C6.23206 7.625 6.45462 7.71719 6.61872 7.88128C6.78281 8.04538 6.875 8.26794 6.875 8.5ZM6.875 6C6.875 6.23206 6.78281 6.45462 6.61872 6.61872C6.45462 6.78281 6.23206 6.875 6 6.875C5.76794 6.875 5.54538 6.78281 5.38128 6.61872C5.21719 6.45462 5.125 6.23206 5.125 6C5.125 5.76794 5.21719 5.54538 5.38128 5.38128C5.54538 5.21719 5.76794 5.125 6 5.125C6.23206 5.125 6.45462 5.21719 6.61872 5.38128C6.78281 5.54538 6.875 5.76794 6.875 6ZM6.875 3.5C6.875 3.73206 6.78281 3.95462 6.61872 4.11872C6.45462 4.28281 6.23206 4.375 6 4.375C5.76794 4.375 5.54538 4.28281 5.38128 4.11872C5.21719 3.95462 5.125 3.73206 5.125 3.5C5.125 3.26794 5.21719 3.04538 5.38128 2.88128C5.54538 2.71719 5.76794 2.625 6 2.625C6.23206 2.625 6.45462 2.71719 6.61872 2.88128C6.78281 3.04538 6.875 3.26794 6.875 3.5Z"
      fill="white"
    />
  </svg>
);
export const CustomerIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <path
      d="M14.9109 7.99328L11.378 13.9093C11.2586 14.1146 10.8578 14.4201 10.6512 14.3014C10.4453 14.1826 10.5108 13.6847 10.6296 13.4788L14.0054 7.47311C14.1247 7.26653 14.4674 7.24177 14.674 7.36054C14.8806 7.47931 15.0296 7.78731 14.9109 7.99328ZM10.0457 9.17462C9.34431 9.99783 8.46973 10.7153 7.01131 10.7153C5.51948 10.7153 4.61212 9.97555 3.93239 9.19501C3.45552 9.23831 3.07392 9.42511 2.77084 9.76282C2.04719 10.5718 1.86845 12.1045 1.86845 12.8467H9.72779C9.68697 13.13 9.64119 13.412 9.64119 13.7076H1.08231L1.03903 13.2499C1.02974 13.1399 0.807087 10.5372 2.09666 9.12389C2.63538 8.53261 3.34668 8.23324 4.21072 8.23324H4.44513L4.59543 8.41324C5.2529 9.19627 5.94005 9.73806 7.05584 9.73806C8.14256 9.73806 8.73322 9.23399 9.42534 8.3984L9.57562 8.21593H9.81127C10.697 8.21593 11.4101 8.48994 11.9587 8.99834C11.7064 9.1969 11.4682 9.40655 11.2518 9.64159C10.9382 9.3682 10.5436 9.20986 10.0457 9.17462ZM7.15109 8.67982C5.38526 8.67982 4.14641 5.94788 4.14641 4.46903C4.14641 2.92091 5.49351 1.66162 7.15109 1.66162C8.80683 1.66162 10.1539 2.9209 10.1539 4.46903C10.1539 5.94788 8.91569 8.67982 7.15109 8.67982ZM7.12696 2.56218C5.97223 2.56218 5.03209 3.41202 5.03209 4.45482C5.03209 5.63122 6.10892 7.81825 7.12696 7.81825C8.14502 7.81825 9.22124 5.63122 9.22124 4.45482C9.22124 3.41202 8.28173 2.56218 7.12696 2.56218Z"
      fill="#3463FC"
    />
  </svg>
);

export const CallPhone = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <path
      d="M14.1882 10.8043L13.9909 10.5506L13.9836 10.5429C13.9192 10.4753 13.7363 10.2893 13.4373 10.0325C13.0798 9.72486 12.4418 9.27114 12.3915 9.23754L11.499 8.69853C11.3352 8.6164 11.1614 8.59918 11.0444 8.59918C10.9173 8.59918 10.7921 8.61959 10.6827 8.65814C10.2966 8.7929 9.93638 9.40135 9.83611 9.5851C9.7898 9.66995 9.73901 9.7716 9.69543 9.86098L9.68138 9.88912C9.62694 9.99756 9.56611 10.1191 9.50169 10.2376C9.43815 10.3519 9.30207 10.4681 9.16412 10.4681C9.12149 10.4681 9.08156 10.4576 9.04301 10.4359C8.44497 10.0924 7.73673 9.64774 7.01439 8.9517C6.1995 8.16585 5.71581 7.54471 5.44812 6.93854C5.4318 6.89702 5.4336 6.70895 5.53977 6.65654C5.68633 6.58349 5.80566 6.53563 5.91091 6.49297L5.93542 6.48231C6.08697 6.42083 6.23079 6.36274 6.39458 6.26294C6.46535 6.21733 6.50302 6.19647 6.53524 6.1783L6.55157 6.16857C6.61055 6.13657 6.6659 6.10594 6.8356 5.99162C6.84287 5.98639 6.87372 5.96098 6.90638 5.93308L7.01527 5.8346C7.29976 5.56034 7.39324 5.15673 7.25303 4.80738L7.20404 4.66605C7.20312 4.6624 7.02254 4.24226 6.73306 3.74295C6.5924 3.50134 6.37914 3.18008 6.19039 2.9251C5.68041 2.23747 5.20854 2.01764 5.20627 2.01676C5.20128 2.01402 5.07877 1.94892 5.00346 1.91467C4.69583 1.78308 4.34826 1.78783 4.03793 1.92396C3.57423 2.09819 3.28973 2.33593 2.9871 2.58867L2.92084 2.64537C2.02246 3.39539 1.53585 4.37385 1.65063 5.2003C1.82284 6.4501 3.26658 9.00118 5.14047 10.807C7.01346 12.6115 9.65095 13.9985 10.9391 14.1623C11.0367 14.175 11.1369 14.1814 11.2372 14.1814C12.1678 14.1814 12.8938 13.6138 13.4215 13.1192C13.6846 12.8724 14.0321 12.4654 14.2172 12.0625C14.3806 11.7086 14.4587 11.1931 14.1882 10.8043ZM4.58057 7.31987C4.9018 8.04651 5.44991 8.76068 6.35647 9.63366C7.14865 10.3982 7.92046 10.8841 8.57156 11.2585C8.75849 11.3651 8.95857 11.4191 9.16458 11.4191C9.69454 11.4191 10.1319 11.0561 10.3316 10.6963C10.4064 10.5597 10.4727 10.4268 10.5452 10.2807L10.5752 10.219C10.6088 10.1532 10.6392 10.0919 10.6669 10.0397C10.817 9.76886 10.96 9.60507 11.0221 9.5488C11.0394 9.54791 11.0639 9.54837 11.0779 9.54975L11.8706 10.0289L11.8837 10.0379C12.0761 10.1754 12.5521 10.5207 12.8189 10.7507C13.0516 10.9504 13.2018 11.0988 13.2508 11.1469C13.2595 11.1559 13.2649 11.1618 13.2704 11.1654L13.4155 11.3515C13.4259 11.3905 13.4155 11.5366 13.3565 11.6659C13.2281 11.9445 12.9627 12.2503 12.774 12.4273C12.1687 12.9935 11.7127 13.2344 11.2481 13.2344C11.1868 13.2344 11.1233 13.2303 11.0593 13.2217C10.0747 13.097 7.59694 11.8578 5.79745 10.1241C4.00389 8.39544 2.72031 6.01565 2.58963 5.07077C2.52067 4.57256 2.88909 3.90652 3.52794 3.37291L3.59645 3.31642C3.87413 3.08298 4.0579 2.92894 4.3941 2.80189C4.45217 2.77739 4.49889 2.76492 4.53792 2.76492C4.5656 2.76492 4.5951 2.77081 4.62051 2.78149C4.65408 2.79737 4.71035 2.82661 4.74482 2.84522L4.77477 2.86043C4.77795 2.86203 5.0806 3.02035 5.42814 3.4895C5.60146 3.72183 5.79112 4.00811 5.9127 4.21775C6.0461 4.44916 6.17905 4.70505 6.30744 4.97864C6.31245 4.98887 6.31653 4.99771 6.32016 5.00475L6.3619 5.1302C6.36508 5.13768 6.36598 5.14178 6.36689 5.14178C6.36507 5.14403 6.36008 5.14924 6.35146 5.15765L6.32378 5.18509C6.312 5.19622 6.29929 5.20824 6.29203 5.21617C6.17179 5.29558 6.13049 5.31825 6.08785 5.343C6.0461 5.36569 5.99529 5.3936 5.89456 5.45732C5.80108 5.5145 5.70716 5.55262 5.57378 5.60639C5.44174 5.65946 5.29747 5.71892 5.11959 5.8067C4.48076 6.12249 4.39544 6.90223 4.58057 7.31987Z"
      fill="#3463FC"
    />
  </svg>
);
export const Abbrev = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
  >
    <path
      d="M8.25024 10.5V9.25H10.0002V8.25H8.25024V7L6.50024 8.75L8.25024 10.5Z"
      fill="url(#paint0_linear_15173_17542)"
    />
    <path
      d="M4.00024 10.5V9.25H2.25024V8.25H4.00024V7L5.75024 8.75L4.00024 10.5Z"
      fill="url(#paint1_linear_15173_17542)"
    />
    <path
      d="M6.62524 2.75V7.25H5.62524V2.75H2.62524V1.75H9.62524V2.75H6.62524Z"
      fill="url(#paint2_linear_15173_17542)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_15173_17542"
        x1="6.50024"
        y1="8.75"
        x2="8.90459"
        y2="7.12694"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3463FC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_15173_17542"
        x1="5.75024"
        y1="8.75"
        x2="3.3459"
        y2="7.12694"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3463FC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <linearGradient
        id="paint2_linear_15173_17542"
        x1="2.62524"
        y1="4.5"
        x2="6.65251"
        y2="1.03994"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3463FC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
    </defs>
  </svg>
);

export const AbbrevChecked = () => (
  <svg
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8.25024 10.5V9.25H10.0002V8.25H8.25024V7L6.50024 8.75L8.25024 10.5Z"
      fill="white"
    />
    <path
      d="M4.00024 10.5V9.25H2.25024V8.25H4.00024V7L5.75024 8.75L4.00024 10.5Z"
      fill="white"
    />
    <path
      d="M6.62524 2.75V7.25H5.62524V2.75H2.62524V1.75H9.62524V2.75H6.62524Z"
      fill="white"
    />
  </svg>
);

export const ExpandIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
  >
    <path
      d="M3.5 8.25H8.5V7L10.25 8.75L8.5 10.5V9.25H3.5V10.5L1.75 8.75L3.5 7V8.25ZM6.5 2.75V7.25H5.5V2.75H2.5V1.75H9.5V2.75H6.5Z"
      fill="url(#paint0_linear_15163_17346)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_15163_17346"
        x1="1.75"
        y1="6.125"
        x2="7.69394"
        y2="2.22717"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#187CFC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
    </defs>
  </svg>
);

export const ExpandIconChecked = () => (
  <svg
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3.5 8.25H8.5V7L10.25 8.75L8.5 10.5V9.25H3.5V10.5L1.75 8.75L3.5 7V8.25ZM6.5 2.75V7.25H5.5V2.75H2.5V1.75H9.5V2.75H6.5Z"
      fill="white"
    />
  </svg>
);

export const SimplifyIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
  >
    <g clip-path="url(#clip0_15163_17338)">
      <path
        d="M5.10001 4.2C5.26561 4.2 5.40001 4.0656 5.40001 3.9C5.40001 3.0732 6.07321 2.4 6.90001 2.4C7.06561 2.4 7.20001 2.2656 7.20001 2.1C7.20001 1.9344 7.06561 1.8 6.90001 1.8C6.07321 1.8 5.40001 1.1268 5.40001 0.3C5.40001 0.1344 5.26561 0 5.10001 0C4.93441 0 4.80001 0.1344 4.80001 0.3C4.80001 1.1268 4.12681 1.8 3.30001 1.8C3.13441 1.8 3.00001 1.9344 3.00001 2.1C3.00001 2.2656 3.13441 2.4 3.30001 2.4C4.12681 2.4 4.80001 3.0732 4.80001 3.9C4.80001 4.0656 4.93441 4.2 5.10001 4.2ZM5.81941 2.1C5.52541 2.2776 5.27761 2.5248 5.10001 2.8194C4.92241 2.5254 4.67521 2.2776 4.38061 2.1C4.67461 1.9224 4.92241 1.6752 5.10001 1.3806C5.27761 1.6746 5.52481 1.9224 5.81941 2.1Z"
        fill="#3463FC"
      />
      <path
        d="M5.10001 4.2C5.26561 4.2 5.40001 4.0656 5.40001 3.9C5.40001 3.0732 6.07321 2.4 6.90001 2.4C7.06561 2.4 7.20001 2.2656 7.20001 2.1C7.20001 1.9344 7.06561 1.8 6.90001 1.8C6.07321 1.8 5.40001 1.1268 5.40001 0.3C5.40001 0.1344 5.26561 0 5.10001 0C4.93441 0 4.80001 0.1344 4.80001 0.3C4.80001 1.1268 4.12681 1.8 3.30001 1.8C3.13441 1.8 3.00001 1.9344 3.00001 2.1C3.00001 2.2656 3.13441 2.4 3.30001 2.4C4.12681 2.4 4.80001 3.0732 4.80001 3.9C4.80001 4.0656 4.93441 4.2 5.10001 4.2ZM5.81941 2.1C5.52541 2.2776 5.27761 2.5248 5.10001 2.8194C4.92241 2.5254 4.67521 2.2776 4.38061 2.1C4.67461 1.9224 4.92241 1.6752 5.10001 1.3806C5.27761 1.6746 5.52481 1.9224 5.81941 2.1Z"
        fill="url(#paint0_linear_15163_17338)"
      />
      <path
        d="M11.1 6.0002C11.2656 6.0002 11.4 5.8658 11.4 5.7002C11.4 5.5346 11.5344 5.4002 11.7 5.4002C11.8656 5.4002 12 5.2658 12 5.1002C12 4.9346 11.8656 4.8002 11.7 4.8002C11.5344 4.8002 11.4 4.6658 11.4 4.5002C11.4 4.3346 11.2656 4.2002 11.1 4.2002C10.9344 4.2002 10.8 4.3346 10.8 4.5002C10.8 4.6658 10.6656 4.8002 10.5 4.8002C10.3344 4.8002 10.2 4.9346 10.2 5.1002C10.2 5.2658 10.3344 5.4002 10.5 5.4002C10.6656 5.4002 10.8 5.5346 10.8 5.7002C10.8 5.8658 10.9344 6.0002 11.1 6.0002Z"
        fill="#3463FC"
      />
      <path
        d="M11.1 6.0002C11.2656 6.0002 11.4 5.8658 11.4 5.7002C11.4 5.5346 11.5344 5.4002 11.7 5.4002C11.8656 5.4002 12 5.2658 12 5.1002C12 4.9346 11.8656 4.8002 11.7 4.8002C11.5344 4.8002 11.4 4.6658 11.4 4.5002C11.4 4.3346 11.2656 4.2002 11.1 4.2002C10.9344 4.2002 10.8 4.3346 10.8 4.5002C10.8 4.6658 10.6656 4.8002 10.5 4.8002C10.3344 4.8002 10.2 4.9346 10.2 5.1002C10.2 5.2658 10.3344 5.4002 10.5 5.4002C10.6656 5.4002 10.8 5.5346 10.8 5.7002C10.8 5.8658 10.9344 6.0002 11.1 6.0002Z"
        fill="url(#paint1_linear_15163_17338)"
      />
      <path
        d="M1.11181 9.56362L7.46341 3.21202C7.63321 3.04222 7.85881 2.94922 8.10001 2.94922C8.34121 2.94922 8.56681 3.04282 8.73661 3.21202L9.08821 3.56362C9.25801 3.73342 9.35101 3.95902 9.35101 4.20022C9.35101 4.44142 9.25741 4.66702 9.08821 4.83682L2.73661 11.1884C2.56681 11.3582 2.34121 11.4512 2.10001 11.4512C1.85881 11.4512 1.63321 11.3576 1.46341 11.1884L1.11181 10.8368C0.942013 10.667 0.849012 10.4414 0.849012 10.2002C0.849012 9.95902 0.942613 9.73342 1.11181 9.56362ZM8.66341 3.98782L8.31181 3.63622C8.25541 3.57982 8.18041 3.54922 8.09941 3.54922C8.01841 3.54922 7.94341 3.58042 7.88701 3.63622L7.02361 4.49962L7.79941 5.27542L8.66281 4.41202C8.77981 4.29502 8.77981 4.10482 8.66281 3.98782H8.66341ZM1.53661 10.4126L1.88821 10.7642C1.94461 10.8206 2.01961 10.8512 2.10001 10.8512C2.18041 10.8512 2.25601 10.82 2.31181 10.7642L7.37521 5.70082L6.59941 4.92502L1.53601 9.98842C1.41901 10.1054 1.41901 10.2956 1.53601 10.4126H1.53661Z"
        fill="#3463FC"
      />
      <path
        d="M1.11181 9.56362L7.46341 3.21202C7.63321 3.04222 7.85881 2.94922 8.10001 2.94922C8.34121 2.94922 8.56681 3.04282 8.73661 3.21202L9.08821 3.56362C9.25801 3.73342 9.35101 3.95902 9.35101 4.20022C9.35101 4.44142 9.25741 4.66702 9.08821 4.83682L2.73661 11.1884C2.56681 11.3582 2.34121 11.4512 2.10001 11.4512C1.85881 11.4512 1.63321 11.3576 1.46341 11.1884L1.11181 10.8368C0.942013 10.667 0.849012 10.4414 0.849012 10.2002C0.849012 9.95902 0.942613 9.73342 1.11181 9.56362ZM8.66341 3.98782L8.31181 3.63622C8.25541 3.57982 8.18041 3.54922 8.09941 3.54922C8.01841 3.54922 7.94341 3.58042 7.88701 3.63622L7.02361 4.49962L7.79941 5.27542L8.66281 4.41202C8.77981 4.29502 8.77981 4.10482 8.66281 3.98782H8.66341ZM1.53661 10.4126L1.88821 10.7642C1.94461 10.8206 2.01961 10.8512 2.10001 10.8512C2.18041 10.8512 2.25601 10.82 2.31181 10.7642L7.37521 5.70082L6.59941 4.92502L1.53601 9.98842C1.41901 10.1054 1.41901 10.2956 1.53601 10.4126H1.53661Z"
        fill="url(#paint2_linear_15163_17338)"
      />
      <path
        d="M9.90002 3C10.0656 3 10.2 2.8656 10.2 2.7C10.2 2.2038 10.6038 1.8 11.1 1.8C11.2656 1.8 11.4 1.6656 11.4 1.5C11.4 1.3344 11.2656 1.2 11.1 1.2C10.6038 1.2 10.2 0.7962 10.2 0.3C10.2 0.1344 10.0656 0 9.90002 0C9.73442 0 9.60002 0.1344 9.60002 0.3C9.60002 0.7962 9.19622 1.2 8.70002 1.2C8.53442 1.2 8.40002 1.3344 8.40002 1.5C8.40002 1.6656 8.53442 1.8 8.70002 1.8C9.19622 1.8 9.60002 2.2038 9.60002 2.7C9.60002 2.8656 9.73442 3 9.90002 3ZM10.2012 1.5C10.0872 1.5858 9.98582 1.6872 9.90002 1.8012C9.81422 1.6872 9.71282 1.5858 9.59882 1.5C9.71282 1.4142 9.81422 1.3128 9.90002 1.1988C9.98582 1.3128 10.0872 1.4142 10.2012 1.5Z"
        fill="#3463FC"
      />
      <path
        d="M9.90002 3C10.0656 3 10.2 2.8656 10.2 2.7C10.2 2.2038 10.6038 1.8 11.1 1.8C11.2656 1.8 11.4 1.6656 11.4 1.5C11.4 1.3344 11.2656 1.2 11.1 1.2C10.6038 1.2 10.2 0.7962 10.2 0.3C10.2 0.1344 10.0656 0 9.90002 0C9.73442 0 9.60002 0.1344 9.60002 0.3C9.60002 0.7962 9.19622 1.2 8.70002 1.2C8.53442 1.2 8.40002 1.3344 8.40002 1.5C8.40002 1.6656 8.53442 1.8 8.70002 1.8C9.19622 1.8 9.60002 2.2038 9.60002 2.7C9.60002 2.8656 9.73442 3 9.90002 3ZM10.2012 1.5C10.0872 1.5858 9.98582 1.6872 9.90002 1.8012C9.81422 1.6872 9.71282 1.5858 9.59882 1.5C9.71282 1.4142 9.81422 1.3128 9.90002 1.1988C9.98582 1.3128 10.0872 1.4142 10.2012 1.5Z"
        fill="url(#paint3_linear_15163_17338)"
      />
      <path
        d="M9.90002 9C10.0656 9 10.2 8.8656 10.2 8.7C10.2 8.2038 10.6038 7.8 11.1 7.8C11.2656 7.8 11.4 7.6656 11.4 7.5C11.4 7.3344 11.2656 7.2 11.1 7.2C10.6038 7.2 10.2 6.7962 10.2 6.3C10.2 6.1344 10.0656 6 9.90002 6C9.73442 6 9.60002 6.1344 9.60002 6.3C9.60002 6.7962 9.19622 7.2 8.70002 7.2C8.53442 7.2 8.40002 7.3344 8.40002 7.5C8.40002 7.6656 8.53442 7.8 8.70002 7.8C9.19622 7.8 9.60002 8.2038 9.60002 8.7C9.60002 8.8656 9.73442 9 9.90002 9ZM10.2012 7.5C10.0872 7.5858 9.98582 7.6872 9.90002 7.8012C9.81422 7.6872 9.71282 7.5858 9.59882 7.5C9.71282 7.4142 9.81422 7.3128 9.90002 7.1988C9.98582 7.3128 10.0872 7.4142 10.2012 7.5Z"
        fill="#3463FC"
      />
      <path
        d="M9.90002 9C10.0656 9 10.2 8.8656 10.2 8.7C10.2 8.2038 10.6038 7.8 11.1 7.8C11.2656 7.8 11.4 7.6656 11.4 7.5C11.4 7.3344 11.2656 7.2 11.1 7.2C10.6038 7.2 10.2 6.7962 10.2 6.3C10.2 6.1344 10.0656 6 9.90002 6C9.73442 6 9.60002 6.1344 9.60002 6.3C9.60002 6.7962 9.19622 7.2 8.70002 7.2C8.53442 7.2 8.40002 7.3344 8.40002 7.5C8.40002 7.6656 8.53442 7.8 8.70002 7.8C9.19622 7.8 9.60002 8.2038 9.60002 8.7C9.60002 8.8656 9.73442 9 9.90002 9ZM10.2012 7.5C10.0872 7.5858 9.98582 7.6872 9.90002 7.8012C9.81422 7.6872 9.71282 7.5858 9.59882 7.5C9.71282 7.4142 9.81422 7.3128 9.90002 7.1988C9.98582 7.3128 10.0872 7.4142 10.2012 7.5Z"
        fill="url(#paint4_linear_15163_17338)"
      />
    </g>
    <defs>
      <linearGradient
        id="paint0_linear_15163_17338"
        x1="7.20001"
        y1="2.1"
        x2="4.3148"
        y2="0.152325"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#187CFC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_15163_17338"
        x1="12"
        y1="5.1002"
        x2="10.7635"
        y2="4.26548"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#187CFC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <linearGradient
        id="paint2_linear_15163_17338"
        x1="9.35101"
        y1="7.20022"
        x2="3.51051"
        y2="3.25757"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#187CFC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <linearGradient
        id="paint3_linear_15163_17338"
        x1="11.4"
        y1="1.5"
        x2="9.33916"
        y2="0.108804"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#187CFC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <linearGradient
        id="paint4_linear_15163_17338"
        x1="11.4"
        y1="7.5"
        x2="9.33916"
        y2="6.1088"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#187CFC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <clipPath id="clip0_15163_17338">
        <rect
          width="12"
          height="12"
          fill="white"
          transform="matrix(-1 0 0 1 12 0)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const SimplifyIconEd = () => (
  <svg
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_16548_88367)">
      <path
        d="M5.1 4.2C5.2656 4.2 5.4 4.0656 5.4 3.9C5.4 3.0732 6.0732 2.4 6.9 2.4C7.0656 2.4 7.2 2.2656 7.2 2.1C7.2 1.9344 7.0656 1.8 6.9 1.8C6.0732 1.8 5.4 1.1268 5.4 0.3C5.4 0.1344 5.2656 0 5.1 0C4.9344 0 4.8 0.1344 4.8 0.3C4.8 1.1268 4.1268 1.8 3.3 1.8C3.1344 1.8 3 1.9344 3 2.1C3 2.2656 3.1344 2.4 3.3 2.4C4.1268 2.4 4.8 3.0732 4.8 3.9C4.8 4.0656 4.9344 4.2 5.1 4.2ZM5.8194 2.1C5.5254 2.2776 5.2776 2.5248 5.1 2.8194C4.9224 2.5254 4.6752 2.2776 4.3806 2.1C4.6746 1.9224 4.9224 1.6752 5.1 1.3806C5.2776 1.6746 5.5248 1.9224 5.8194 2.1Z"
        fill="white"
      />
      <path
        d="M11.1 6C11.2656 6 11.4 5.8656 11.4 5.7C11.4 5.5344 11.5344 5.4 11.7 5.4C11.8656 5.4 12 5.2656 12 5.1C12 4.9344 11.8656 4.8 11.7 4.8C11.5344 4.8 11.4 4.6656 11.4 4.5C11.4 4.3344 11.2656 4.2 11.1 4.2C10.9344 4.2 10.8 4.3344 10.8 4.5C10.8 4.6656 10.6656 4.8 10.5 4.8C10.3344 4.8 10.2 4.9344 10.2 5.1C10.2 5.2656 10.3344 5.4 10.5 5.4C10.6656 5.4 10.8 5.5344 10.8 5.7C10.8 5.8656 10.9344 6 11.1 6Z"
        fill="white"
      />
      <path
        d="M1.1118 9.5634L7.4634 3.2118C7.6332 3.042 7.8588 2.949 8.1 2.949C8.3412 2.949 8.5668 3.0426 8.7366 3.2118L9.0882 3.5634C9.258 3.7332 9.351 3.9588 9.351 4.2C9.351 4.4412 9.2574 4.6668 9.0882 4.8366L2.7366 11.1882C2.5668 11.358 2.3412 11.451 2.1 11.451C1.8588 11.451 1.6332 11.3574 1.4634 11.1882L1.1118 10.8366C0.942 10.6668 0.848999 10.4412 0.848999 10.2C0.848999 9.9588 0.9426 9.7332 1.1118 9.5634ZM8.6634 3.9876L8.3118 3.636C8.2554 3.5796 8.1804 3.549 8.0994 3.549C8.0184 3.549 7.9434 3.5802 7.887 3.636L7.0236 4.4994L7.7994 5.2752L8.6628 4.4118C8.7798 4.2948 8.7804 4.1046 8.6634 3.9876ZM1.5366 10.4124L1.8882 10.764C1.9446 10.8204 2.0196 10.851 2.1 10.851C2.1804 10.851 2.256 10.8198 2.3118 10.764L7.3752 5.7006L6.5994 4.9248L1.536 9.9882C1.419 10.1052 1.4196 10.2954 1.5366 10.4124Z"
        fill="white"
      />
      <path
        d="M9.9 3C10.0656 3 10.2 2.8656 10.2 2.7C10.2 2.2038 10.6038 1.8 11.1 1.8C11.2656 1.8 11.4 1.6656 11.4 1.5C11.4 1.3344 11.2656 1.2 11.1 1.2C10.6038 1.2 10.2 0.7962 10.2 0.3C10.2 0.1344 10.0656 0 9.9 0C9.7344 0 9.6 0.1344 9.6 0.3C9.6 0.7962 9.1962 1.2 8.7 1.2C8.5344 1.2 8.4 1.3344 8.4 1.5C8.4 1.6656 8.5344 1.8 8.7 1.8C9.1962 1.8 9.6 2.2038 9.6 2.7C9.6 2.8656 9.7344 3 9.9 3ZM10.2012 1.5C10.0872 1.5858 9.9858 1.6872 9.9 1.8012C9.8142 1.6872 9.7128 1.5858 9.5988 1.5C9.7128 1.4142 9.8142 1.3128 9.9 1.1988C9.9858 1.3128 10.0872 1.4142 10.2012 1.5Z"
        fill="white"
      />
      <path
        d="M9.9 9C10.0656 9 10.2 8.8656 10.2 8.7C10.2 8.2038 10.6038 7.8 11.1 7.8C11.2656 7.8 11.4 7.6656 11.4 7.5C11.4 7.3344 11.2656 7.2 11.1 7.2C10.6038 7.2 10.2 6.7962 10.2 6.3C10.2 6.1344 10.0656 6 9.9 6C9.7344 6 9.6 6.1344 9.6 6.3C9.6 6.7962 9.1962 7.2 8.7 7.2C8.5344 7.2 8.4 7.3344 8.4 7.5C8.4 7.6656 8.5344 7.8 8.7 7.8C9.1962 7.8 9.6 8.2038 9.6 8.7C9.6 8.8656 9.7344 9 9.9 9ZM10.2012 7.5C10.0872 7.5858 9.9858 7.6872 9.9 7.8012C9.8142 7.6872 9.7128 7.5858 9.5988 7.5C9.7128 7.4142 9.8142 7.3128 9.9 7.1988C9.9858 7.3128 10.0872 7.4142 10.2012 7.5Z"
        fill="white"
      />
    </g>
    <defs>
      <clipPath id="clip0_16548_88367">
        <rect
          width="12"
          height="12"
          fill="white"
          transform="matrix(-1 0 0 1 12 0)"
        />
      </clipPath>
    </defs>
  </svg>
);
export const EvaluationIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 34 34"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8.39835 34C8.90815 34 9.38024 33.8679 9.83377 33.6223L16.7453 29.8254C16.8257 29.7791 16.9169 29.7546 17.0097 29.7546C17.1025 29.7546 17.1937 29.7791 17.2741 29.8254L24.1859 33.6223C24.639 33.8679 25.1303 34 25.6211 34C26.5086 34 27.3397 33.6033 27.9251 32.9046C28.5295 32.2056 28.7747 31.261 28.6239 30.3167L27.3018 22.27C27.264 22.0435 27.3397 21.7977 27.4907 21.6279L33.0993 15.9612C33.93 15.1112 34.2133 13.8646 33.8356 12.7123C33.4581 11.5977 32.5329 10.8044 31.3997 10.6156L23.6759 9.44458C23.4873 9.40686 23.3171 9.27458 23.2227 9.08562L19.7481 1.77562C19.2379 0.68 18.1806 0 16.9906 0C15.801 0 14.7624 0.68 14.2336 1.77562L10.7778 9.08562C10.6834 9.27458 10.5132 9.40686 10.3244 9.44458L2.60096 10.6156C1.46801 10.7856 0.523827 11.5977 0.164875 12.7123C-0.213036 13.8644 0.0704956 15.1112 0.901348 15.9612L6.49083 21.6654C6.64187 21.8165 6.71769 22.0623 6.67998 22.3079L5.35789 30.3544C5.20704 31.28 5.45227 32.2246 6.05665 32.9421C6.66103 33.6033 7.51083 34 8.39835 34Z"
      fill="#F9E216"
    />
  </svg>
);
export const EvaluationIconNo = () => (
  <svg
    width="25"
    height="24"
    viewBox="0 0 35 34"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_1236_3329)">
      <path
        d="M8.79813 34C9.30793 34 9.78002 33.8679 10.2336 33.6223L17.1451 29.8254C17.2255 29.7791 17.3167 29.7546 17.4095 29.7546C17.5023 29.7546 17.5935 29.7791 17.6739 29.8254L24.5857 33.6223C25.0388 33.8679 25.53 34 26.0209 34C26.9084 34 27.7395 33.6033 28.3249 32.9046C28.9293 32.2056 29.1745 31.261 29.0236 30.3167L27.7015 22.27C27.6638 22.0435 27.7395 21.7977 27.8905 21.6279L33.4991 15.9612C34.3298 15.1112 34.6131 13.8646 34.2354 12.7123C33.8579 11.5977 32.9327 10.8044 31.7995 10.6156L24.0757 9.44458C23.8871 9.40686 23.7169 9.27458 23.6225 9.08562L20.1479 1.77562C19.6377 0.68 18.5804 0 17.3904 0C16.2007 0 15.1622 0.68 14.6334 1.77562L11.1775 9.08562C11.0832 9.27458 10.913 9.40686 10.7242 9.44458L3.00074 10.6156C1.86779 10.7856 0.923607 11.5977 0.564655 12.7123C0.186745 13.8644 0.470276 15.1112 1.30113 15.9612L6.89061 21.6654C7.04165 21.8165 7.11747 22.0623 7.07976 22.3079L5.75767 30.3544C5.60682 31.28 5.85205 32.2246 6.45643 32.9421C7.06081 33.6033 7.91061 34 8.79813 34Z"
        fill="#D9D9D9"
      />
    </g>
    <defs>
      <clipPath id="clip0_1236_3329">
        <rect
          width="34"
          height="34"
          fill="white"
          transform="matrix(-1 0 0 1 34.4 0)"
        />
      </clipPath>
    </defs>
  </svg>
);
export const ReferencesIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 12 12"
    fill="none"
  >
    <path
      d="M6.97908 1.4125C7.17867 1.41255 7.37103 1.48721 7.51839 1.62181C7.66576 1.75641 7.75749 1.94123 7.77558 2.14L7.77858 2.2125V3.5555L7.83158 3.559C10.4651 3.74 11.8451 5.2825 11.8901 8.9085L11.8916 9.0945C11.8916 9.6195 11.3991 9.739 11.0691 9.545C11.0359 9.52553 11.0038 9.50417 10.9731 9.481L10.8966 9.421L10.5266 9.111L10.4521 9.051L10.3151 8.9445L10.1811 8.8475L10.0346 8.7485C9.35658 8.3015 8.63308 8.0415 7.82658 8.0305L7.77858 8.031V9.649C7.77855 9.83099 7.71647 10.0075 7.60258 10.1495L7.55058 10.208C7.41159 10.3501 7.22465 10.4355 7.02619 10.4473C6.82773 10.4591 6.63198 10.3966 6.47708 10.272L6.41958 10.221L2.91458 6.794C2.39058 6.348 2.36808 5.599 2.82658 5.1475L2.88908 5.0905L6.41908 1.6405C6.56873 1.49417 6.76977 1.41232 6.97908 1.4125ZM6.77908 4.537L6.77858 2.687L3.56258 5.829C3.49408 5.887 3.48558 5.9485 3.55408 6.023L6.77858 9.1735V7.109L7.22458 7.0605C8.48808 6.923 9.59108 7.2585 10.5851 7.9135L10.6731 7.9725L10.8336 8.0845L10.8551 8.1L10.8436 7.9625C10.6106 5.4445 9.51858 4.574 7.40408 4.5415L7.27908 4.5405L7.27858 4.5365H6.77858L6.77908 4.537Z"
      fill="#3463FC"
    />
    <path
      d="M3.2446 2.15C3.33369 2.05952 3.45388 2.00633 3.58077 2.00122C3.70765 1.99612 3.83172 2.03949 3.9278 2.12252C4.02388 2.20556 4.08476 2.32204 4.0981 2.44832C4.11143 2.5746 4.07621 2.70123 3.9996 2.8025L3.9591 2.85L1.0631 5.802C0.994595 5.86 0.986095 5.9215 1.0381 5.9805L1.1011 6.041L3.9636 9.039C4.0509 9.13107 4.09994 9.25288 4.10077 9.37976C4.1016 9.50663 4.05417 9.62908 3.96809 9.72229C3.882 9.81549 3.7637 9.87249 3.63716 9.88172C3.51062 9.89095 3.3853 9.85173 3.2866 9.772L3.2406 9.7295L0.386595 6.7405L0.349595 6.7075C-0.110405 6.2565 -0.111905 5.5525 0.320095 5.1275L0.382095 5.071L3.2441 2.15H3.2446Z"
      fill="#3463FC"
    />
  </svg>
);
export const CopyIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 12 12"
    fill="none"
  >
    <path
      d="M8.4375 2.25H2.0625C1.81395 2.2503 1.57567 2.34917 1.39992 2.52492C1.22417 2.70067 1.1253 2.93895 1.125 3.1875V10.3125C1.125 10.8296 1.54575 11.25 2.0625 11.25H8.4375C8.95463 11.25 9.375 10.8296 9.375 10.3125V3.1875C9.375 2.67075 8.95463 2.25 8.4375 2.25ZM8.625 10.3125C8.625 10.4156 8.54063 10.5 8.4375 10.5H2.0625C2.01277 10.5 1.96508 10.4802 1.92992 10.4451C1.89475 10.4099 1.875 10.3622 1.875 10.3125V3.1875C1.875 3.13777 1.89475 3.09008 1.92992 3.05492C1.96508 3.01975 2.01277 3 2.0625 3H8.4375C8.48723 3 8.53492 3.01975 8.57008 3.05492C8.60525 3.09008 8.625 3.13777 8.625 3.1875V10.3125Z"
      fill="#3463FC"
    />
    <path
      d="M9.9375 0.75H3.5625C3.46304 0.75 3.36766 0.789509 3.29734 0.859835C3.22701 0.930161 3.1875 1.02554 3.1875 1.125C3.1875 1.22446 3.22701 1.31984 3.29734 1.39017C3.36766 1.46049 3.46304 1.5 3.5625 1.5H9.9375C9.98723 1.5 10.0349 1.51975 10.0701 1.55492C10.1052 1.59008 10.125 1.63777 10.125 1.6875V8.8125C10.125 8.91196 10.1645 9.00734 10.2348 9.07767C10.3052 9.14799 10.4005 9.1875 10.5 9.1875C10.5995 9.1875 10.6948 9.14799 10.7652 9.07767C10.8355 9.00734 10.875 8.91196 10.875 8.8125V1.6875C10.875 1.17075 10.4546 0.75 9.9375 0.75Z"
      fill="#3463FC"
    />
    <path
      d="M7.125 4.21875H3.375C3.27554 4.21875 3.18016 4.25826 3.10984 4.32859C3.03951 4.39891 3 4.49429 3 4.59375C3 4.69321 3.03951 4.78859 3.10984 4.85891C3.18016 4.92924 3.27554 4.96875 3.375 4.96875H7.125C7.22446 4.96875 7.31984 4.92924 7.39017 4.85891C7.46049 4.78859 7.5 4.69321 7.5 4.59375C7.5 4.49429 7.46049 4.39891 7.39017 4.32859C7.31984 4.25826 7.22446 4.21875 7.125 4.21875ZM7.125 6.09375H3.375C3.27554 6.09375 3.18016 6.13326 3.10984 6.20358C3.03951 6.27391 3 6.36929 3 6.46875C3 6.56821 3.03951 6.66359 3.10984 6.73392C3.18016 6.80424 3.27554 6.84375 3.375 6.84375H7.125C7.22446 6.84375 7.31984 6.80424 7.39017 6.73392C7.46049 6.66359 7.5 6.56821 7.5 6.46875C7.5 6.36929 7.46049 6.27391 7.39017 6.20358C7.31984 6.13326 7.22446 6.09375 7.125 6.09375ZM5.625 7.953H3.375C3.27554 7.953 3.18016 7.99251 3.10984 8.06283C3.03951 8.13316 3 8.22854 3 8.328C3 8.42746 3.03951 8.52284 3.10984 8.59317C3.18016 8.66349 3.27554 8.703 3.375 8.703H5.625C5.72446 8.703 5.81984 8.66349 5.89016 8.59317C5.96049 8.52284 6 8.42746 6 8.328C6 8.22854 5.96049 8.13316 5.89016 8.06283C5.81984 7.99251 5.72446 7.953 5.625 7.953Z"
      fill="#3463FC"
    />
  </svg>
);
export const WithdrawIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 12 12"
    fill="none"
  >
    <path
      d="M2.78087 4.42051L4.10687 5.66301C4.30122 5.85946 4.50122 6.17786 4.30687 6.37426C4.11237 6.57066 3.84717 6.57066 3.65277 6.37426L1.62147 4.31716C1.46722 4.16176 1.45597 3.91296 1.61022 3.75756L3.65282 1.66836C3.84722 1.47196 4.11247 1.47196 4.30687 1.66836C4.50127 1.86476 4.30127 2.18321 4.10687 2.37956L2.79812 3.67051H7.26157C9.05702 3.67051 10.5 5.22771 10.5 7.01901C10.5 8.81026 9.05697 10.5211 7.26157 10.5211H2.50142C2.22817 10.5211 1.99892 10.3943 1.99892 10.1211C1.99892 9.84791 2.22817 9.77106 2.50142 9.77106H7.22192C8.42617 9.77106 9.74982 8.22041 9.74982 7.01896C9.74982 5.81751 8.42617 4.42061 7.22192 4.42061H2.78087V4.42051Z"
      fill="#3463FC"
    />
  </svg>
);
export const ReferencesCloseIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
  >
    <g clip-path="url(#clip0_3029_8901)">
      <path
        d="M6 0C9.31365 0 12 2.68635 12 6C12 9.31365 9.31365 12 6 12C2.68635 12 0 9.31365 0 6C0 2.68635 2.68635 0 6 0ZM5.9826 5.2849L3.998 3.3003C3.96086 3.26315 3.91676 3.23369 3.86823 3.21359C3.8197 3.19348 3.76768 3.18314 3.71515 3.18314C3.66262 3.18314 3.6106 3.19348 3.56207 3.21359C3.51354 3.23369 3.46944 3.26315 3.4323 3.3003L3.3003 3.4323C3.26315 3.46944 3.23369 3.51354 3.21359 3.56207C3.19348 3.6106 3.18314 3.66262 3.18314 3.71515C3.18314 3.76768 3.19348 3.8197 3.21359 3.86823C3.23369 3.91676 3.26315 3.96086 3.3003 3.998L5.2849 5.9826L3.3003 7.9672C3.26315 8.00434 3.23369 8.04844 3.21359 8.09697C3.19348 8.1455 3.18314 8.19752 3.18314 8.25005C3.18314 8.30258 3.19348 8.3546 3.21359 8.40313C3.23369 8.45166 3.26315 8.49576 3.3003 8.5329L3.4323 8.6649C3.46944 8.70205 3.51354 8.73151 3.56207 8.75161C3.6106 8.77172 3.66262 8.78206 3.71515 8.78206C3.76768 8.78206 3.8197 8.77172 3.86823 8.75161C3.91676 8.73151 3.96086 8.70205 3.998 8.6649L5.9826 6.68025L7.9672 8.6649C8.00434 8.70205 8.04844 8.73151 8.09697 8.75161C8.1455 8.77172 8.19752 8.78206 8.25005 8.78206C8.30258 8.78206 8.3546 8.77172 8.40313 8.75161C8.45166 8.73151 8.49576 8.70205 8.5329 8.6649L8.6649 8.5329C8.70205 8.49576 8.73151 8.45166 8.75161 8.40313C8.77172 8.3546 8.78206 8.30258 8.78206 8.25005C8.78206 8.19752 8.77172 8.1455 8.75161 8.09697C8.73151 8.04844 8.70205 8.00434 8.6649 7.9672L6.68025 5.9826L8.6649 3.998C8.70205 3.96086 8.73151 3.91676 8.75161 3.86823C8.77172 3.8197 8.78206 3.76768 8.78206 3.71515C8.78206 3.66262 8.77172 3.6106 8.75161 3.56207C8.73151 3.51354 8.70205 3.46944 8.6649 3.4323L8.5329 3.3003C8.49576 3.26315 8.45166 3.23369 8.40313 3.21359C8.3546 3.19348 8.30258 3.18314 8.25005 3.18314C8.19752 3.18314 8.1455 3.19348 8.09697 3.21359C8.04844 3.23369 8.00434 3.26315 7.9672 3.3003L5.9826 5.2849Z"
        fill="#999999"
      />
    </g>
    <defs>
      <clipPath id="clip0_3029_8901">
        <rect width="12" height="12" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
export const WhatsappCheckIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M9.517 1H2.233C1.90699 1.00157 1.59474 1.13157 1.36393 1.36181C1.13312 1.59206 1.00236 1.904 1 2.23V9.51775C1.00177 9.84363 1.13185 10.1557 1.36208 10.3863C1.5923 10.617 1.90412 10.7476 2.23 10.75H9.517C10.198 10.75 10.75 10.198 10.75 9.51775V2.23225C10.75 1.552 10.198 1 9.517 1ZM2.23525 1.75H9.51775C9.784 1.75 10 1.966 10 2.23225V3.93127C9.98856 3.92725 9.97678 3.92394 9.96469 3.92139H4.36004C4.35189 3.92074 4.34366 3.92041 4.33534 3.92041C4.32703 3.92041 4.31879 3.92074 4.31065 3.92139H1.75V2.233C1.75118 2.10488 1.80276 1.98237 1.89357 1.89198C1.98438 1.80159 2.10712 1.75059 2.23525 1.75ZM4.03984 4.51314H1.75V9.5155C1.75078 9.6435 1.80187 9.76605 1.89224 9.8567C1.98261 9.94735 2.10501 9.99882 2.233 10H4.04122C4.0401 9.98903 4.03964 9.97798 4.03984 9.96691V4.51314ZM4.63013 10H9.517C9.64497 10 9.76771 9.94922 9.85827 9.8588C9.94883 9.76838 9.9998 9.64572 10 9.51775V4.50325C9.98856 4.50727 9.97678 4.51059 9.96469 4.51314H4.63159V9.96691C4.63176 9.97798 4.63127 9.98903 4.63013 10Z"
      fill="white"
    />
  </svg>
);
export const WhatsappIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
  >
    <path
      d="M9.5166 1C10.1976 1 10.75 1.55217 10.75 2.23242V9.51758C10.75 10.1978 10.1976 10.75 9.5166 10.75H2.23047C1.90474 10.7476 1.5925 10.6172 1.3623 10.3867C1.13208 10.1561 1.00177 9.84346 1 9.51758V2.23047C1.00236 1.90446 1.13345 1.59157 1.36426 1.36133C1.59502 1.13134 1.90758 1.00157 2.2334 1H9.5166ZM9.96484 4.5127H4.63184V9.9668C4.63201 9.97791 4.63006 9.98899 4.62891 10H9.5166C9.64457 10 9.76784 9.94882 9.8584 9.8584C9.94879 9.76801 9.9998 9.64542 10 9.51758V4.50195C9.98851 4.50602 9.97699 4.51012 9.96484 4.5127ZM1.75 4.5127V9.51562C1.75081 9.64358 1.80224 9.76583 1.89258 9.85645C1.98294 9.94707 2.10542 9.99882 2.2334 10H4.04297C4.04184 9.98897 4.03984 9.97793 4.04004 9.9668V4.5127H1.75ZM2.23535 1.75C2.10731 1.75059 1.98435 1.80133 1.89355 1.8916C1.80274 1.98199 1.75118 2.10528 1.75 2.2334V3.92188H4.32324C4.32714 3.92172 4.33102 3.9209 4.33496 3.9209C4.33877 3.9209 4.3429 3.92174 4.34668 3.92188H9.96484C9.97688 3.92443 9.9886 3.92763 10 3.93164V2.23242C10 1.96617 9.78383 1.75 9.51758 1.75H2.23535Z"
      fill="#3463FC"
    />
  </svg>
);
export const ResyncIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_7461_6274)">
      <path d="M12.025 2.492L8.03302 0V4.989L12.025 2.492Z" fill="#3463FC" />
      <path
        d="M8.015 1.98901C4.301 2.12701 0.979004 5.17801 0.979004 8.92401C0.979004 12.756 4.334 16 8.168 16C11.912 16 14.873 13.03 15.008 9.31701H13.979C13.845 12.463 11.346 15 8.168 15C4.902 15 1.963 12.188 1.963 8.92401C1.963 5.74501 4.869 3.15301 8.015 3.02001V1.98901Z"
        fill="#3463FC"
      />
    </g>
    <defs>
      <clipPath id="clip0_7461_6274">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
