import React, { Component } from 'react';
import ReactMarkdown from 'react-markdown';
import { connect as umiConnect } from 'umi';
import {
  TransFerIcon,
  CheckedIcon,
  BeautifyIcon,
  BeautifyIconChecked,
  CollectIcon,
  CollectIconChecked,
  Abbrev,
  AbbrevChecked,
  ExpandIcon,
  ExpandIconChecked,
  SimplifyIconEd,
  SimplifyIcon,
  AiIcon,
  LinkIcon,
  SmileIcon,
  SendIcon,
  Unread,
  Read,
  HistoryLineLeft,
  HistoryLineRight,
  CircleIcon,
  NodataBg,
  ChatGrammerSelected,
  ChatGrammer,
  FormalIcon,
  NeutralityIcon,
  GentleIcon,
  GrammarCorrectionIcon,
  CustomerIcon,
  CallPhone,
  EvaluationIcon,
  EvaluationIconNo,
  WithdrawIcon,
  CopyIcon,
  ReferencesIcon,
  ReferencesCloseIcon,
  HotspotIssuesIcon,
  WhatsappIcon,
  WhatsappCheckIcon,
  SendFail,
  ResyncIcon,
} from './icon';
import NewUpIcon from '@/assets/new-up-icon.png';
import NewDownIcon from '@/assets/new-down-icon.png';
import AiIntentionIcon from '@/assets/ai-intention-icon.png';
import MoodGoodIcon from '../../../assets/mood-good-2.svg';
import MoodNormalIcon from '../../../assets/mood-normal-2.svg';
import MoodBadIcon from '../../../assets/mood-bad-2.svg';
import MoodGoodActiveIcon from '../../../assets/mood-good-active.png';
import MoodNormalActiveIcon from '../../../assets/mood-normal-active.png';
import MoodBadActiveIcon from '../../../assets/mood-bad-active.png';
import { ReactComponent as FileFrame } from '@/assets/file_frame.svg';

import WorkerLayoutBottomIcon from '@/assets/workerLayout_bottom.svg';
import WorkerLayoutTopIcon from '@/assets/workerLayout_top.svg';
import AgentIcon from '@/assets/agent.svg';
import CustomIcon from '@/assets/custom.svg';
import imChatNoData12 from '../../agentImChat/im-chat-no-data12.svg';
import emojiData from '../../../pages/agentImChat/emoji';
import Phone from '../../../assets/Phone2.png';
import Customer from '../../../assets/customer.png';
import workTableSettingAi from '@/assets/workTable_setting_ai.svg';
import worktableLayoutHiddenEye from '@/assets/worktable_layout_hiddenEye.svg';
import { parsePhoneNumberFromString } from 'libphonenumber-js';
import { notification } from '../../../utils/utils.js';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import AdvancedEditable from './AdvancedEditable/index.js';
import {
  IMWebClient,
  getDownloadPreSignedUrl,
} from '../../../pages/agentImChat/sdk/im.web.sdk.js';
import {
  AgentPhoto,
  CustomerPhoto,
  IncomingCallCloseIcon,
} from '../../../pages/agentImChat/icon.js';
import { FormattedMessage, getIntl } from 'umi';
import { ReactComponent as Search } from '@/assets/Search.svg';
import UserNav from './userNav/index';
import styles from './index.less';
import AudioPlayer from '../chatLayout/AudioPlayer/index.js';
import EmailDetailContent from './emailDetailContent';
import {
  Input,
  Select,
  Image,
  Tooltip,
  message,
  Progress,
  Checkbox,
  Spin,
  Popover,
  Button,
  Row,
  Col,
  Popconfirm,
  Form,
  Modal,
  Card,
  Tabs,
  Carousel,
} from 'antd';
const { TextArea } = Input;
import QuickReply from '../chatLayout/quickReply/index.js';
import {
  handleWorkDetail,
  uploadParams,
  convertMarkdownImageToHtmlImage,
  convertHtmlImageToMarkdownImage,
} from '../tools.js';
import ShhowReplyModle from './showQuickReply/index.js';
import {
  PlusOutlined,
  ReloadOutlined,
  ExportOutlined,
  PhoneOutlined,
  CopyOutlined,
  LoadingOutlined,
  RightOutlined,
  LeftOutlined,
} from '@ant-design/icons';
import EditorIcon from '../../../assets/external-editor.png';
import {
  addAigcTicketSmartFill,
  aigcTicketSmartFill,
} from '../../../service/intelligentFormFilling';
import { getTicketSmartFillLanguage } from '../../../service/worktable';
const { Meta } = Card;
class ChatLayout extends Component {
  quickReplyChild = React.createRef();

  constructor(props) {
    super(props);
    this.imContainerRef = React.createRef();
    this.imContainerRefs = React.createRef();
    this.inputRef = React.createRef();
    this.smartFillRef = React.createRef();
    this.smartFillRef1 = React.createRef();
    this.popoverRefAgent = React.createRef();
    this.popoverRefCustomer = React.createRef();
    this.carouselRef = React.createRef();
    this.state = {
      carouseNum: 1,
      inputLoading: false,
      templateModal: false,
      whatsAppLoading: false,
      whatsAppType: null,
      whatsAppInput: '',
      whatsAppMessageList: [],
      // languageCode:"zh_HK",
      // templateContentList:[{type: "HEADER", format: "IMAGE", text: null, buttons: null},…]
      // templateName:"connectnow"
      // templateStatus:"APPROVED"
      // templateType:"MARKETING"
      // templateUuid:"1503392ddf354a239778f1af5aacf836"
      // whatsappTemplateId:null,
      whatsAppMessageItem: null,
      whatsAppParameters: {},
      whatsAppMessageItemContent: '',
      loadingModal: false,
      abbrev: false, // 缩写
      whatsApp: false, // whatsApp
      expand: false, //阔写
      simplify: false, // 简化
      grammerState: false, // 语法纠错
      openStates: {},
      // 图片预览相关状态
      imagePreviewVisible: false,
      imagePreviewId: '',
      open: false, // 打开电话弹框
      workTypeDetail: {}, // 工单类型详情
      replyMessageModleData: [], // 快捷回复数据
      replyMessageBaseData: [], // 快捷回复基础数据
      replyModle: false, // 输入时弹出快捷回复
      loading: false,
      activeTab: 1, // 默认选中'回复'1，个人备注2
      showQuickReply: false,
      socialMediaTimeStatus: false, // 超时24小时关闭聊天
      // ========================
      translationCodeList: [], // 翻译的语言列表
      emojiModal: false, // emoji窗口开关
      inputValue: '', // 输入框值
      inputValueAType: '', //复制内容的类型
      inputNoteValue: '', //备注输入值，用于电话
      uploadCancelToken: '', // 控制上传附件取消
      scrollHeight: '',
      clientHeight: '',
      translateStatus: false, // 是否开启翻译的状态，默认false
      translateCode: '', // 翻译的语言did
      currentSessionData: {},
      isTranslationExpanded: false, // 翻译窗口是否打开
      customerMood: 2,
      contentSummary: '',
      waitExecuteList: [],
      selectWaitExecuteList: [],
      addTodoStatus: false,
      waitExecuteEvent: '',
      workRecordIdSummary: '',
      customerIdSummary: '',
      worktable_setting_ai: '',
      isAdminJoinChat: true,
      loadingIntelligentFormFilling: false,
      loadingSave: false,
      ticketSmartFill: {},
      ticketSmartFillInitData: {},
      intelligentFormFillingTicketId: '',
      selectLanguageModal: false,
      loadingBtn: false,
      isPopoverVisibleReference: false, //引用功能
      isPopoverVisibleCopy: false, //复制功能
      isPopoverVisibleWithdraw: false, //撤回功能
      popoverPosition: {
        top: 0,
        left: 0,
      },
      tagRef: '',
      isReferencesMessage: false,
      referencesMessageContent: {},
      userProfilePicture: '', //坐席个性化头像
      nickName: '', //坐席个性化头像名称
      // 智能填单语言列表
      filteredLanguages: [],
      historyMessage: [],
      showUndo: false,
      newAIGCcontent: '',
    };
  }
  // showQuickReply
  componentDidUpdate(prevProps, prevState) {
    //进线意图是否展示
    let flag = localStorage.getItem('worktable_setting_ai') === 'true';
    if (this.state.worktable_setting_ai !== flag) {
      this.setState({
        worktable_setting_ai: flag,
      });
    }
    // //实时翻译是否开启
    // let translationFlag =
    //   localStorage.getItem('worktable_setting_translation') === 'true';
    // let translationCode = localStorage.getItem(
    //   'worktable_setting_translation_code',
    // );

    //快速回复列表
    if (this.state.showQuickReply !== prevState.showQuickReply) {
      this.listQuickReply('');
    }

    //电话逻辑
    if (
      this.props.currentSessionData.channelId === '7' &&
      this.props.currentSessionData.messageList &&
      this.props.currentSessionData !== prevProps.currentSessionData
    ) {
      console.log('列表为空===111', this.props.currentSessionData);
      this.scrollToBottom();
      this.setState({
        currentSessionData: this.props.currentSessionData,
        replyMessageModleData: [],
        inputValue: '',
        showUndo: false,
        inputValueAType: '',
      });
      if (
        this.props.currentSessionData.ticketId &&
        this.props.currentSessionData.ticketId !== undefined
      ) {
        this.setState({
          workTypeDetail: {},
        });

        //获取所有工单信息
        this.queryWorkOrderDetail();
      }
    }
    //当前对话发生改变
    if (
      this.state.currentSessionData.messageList &&
      prevState.currentSessionData.messageList &&
      this.state.currentSessionData.messageList.length !==
        prevState.currentSessionData.messageList.length
    ) {
      this.scrollToBottom();
    }

    //聊天逻辑
    if (
      this.props.currentSessionData &&
      this.props.currentSessionData.ticketId !==
        prevProps.currentSessionData.ticketId
    ) {
      console.log('chatLayoutUpdate5', this.props.currentSessionData);
      this.setState({
        currentSessionData: {
          ...this.props.currentSessionData,
        },
        replyMessageModleData: [],
        inputValue: '',
        showUndo: false,
        inputValueAType: '',
        // translateStatus: this.props.currentSessionData.translateStatus,
        isAdminJoinChat: this.props.currentSessionData.isAdminJoinChat,
        popoverPosition: {
          top: 0,
          left: 0,
        },
        tagRef: '',
        isReferencesMessage: false,
        referencesMessageContent: {},
      });
      if (
        this.props.currentSessionData.ticketId &&
        this.props.currentSessionData.ticketId !== undefined
      ) {
        // this.setTranslationEnable(translationFlag, translationCode);
        this.setState({
          workTypeDetail: {},
          // loading: true,
        });

        if (this.props.workTableTabValue !== 3) {
          //这里处理数据展示，纯前端
          this.getChatLayout();
          //获取聊天记录，这里不需要获取聊天记录，改为从queryProcessingWorkOrderList接口一次性获取，此接口只需要拿socialMediaTimeStatus，不需要loading
          this.loadTicketInfo();
          //查询坐席头像
          this.queryUserProfilePicture(
            this.props.currentSessionData.channelConfigId,
          );
        }
        //获取所有工单信息
        this.queryWorkOrderDetail();
        this.setState({
          isTranslationExpanded: false,
        });
      }
    } else if (
      this.props.globalList &&
      this.props.globalList !== prevProps.globalList
    ) {
      const findOne = this.props.globalList.find(
        item => item?.ticketId === this.props.currentSessionData?.ticketId,
      );

      console.log(
        'chatLayoutUpdate4',
        findOne,
        prevProps.globalList,
        this.props.globalList,
      );
      if (findOne) {
        // let publicStatus = 1;
        // let newMessageList = findOne.messageList?.filter((item, index) => {
        //   //记录是否全部数据status都为1；
        //   if (item.status === 0) {
        //     publicStatus = 0;
        //   }
        //   return item;
        // });

        console.log(
          'chatLayoutUpdate4.3',
          // publicStatus,
          // newMessageList,
          findOne,
        );

        //去重
        let findOneMessageList = findOne.messageList?.filter(
          (item, index, self) =>
            index === self.findIndex(t => t.id === item.id),
        );
        console.log(
          'chatLayoutUpdate3',
          this.state.currentSessionData.messageList,
          findOneMessageList,
        );

        if (this.state.currentSessionData?.messageList) {
          // 对比并整合两个消息列表中的数据
          // 如果findOneMessageList中的项与currentSessionData.messageList中的项ID相同，则整合它们的字段
          // 保留findOneMessageList中的数据，但添加currentSessionData.messageList中有而findOneMessageList中没有的字段
          //目前用于智能总结，智能填单，记录他们的修改状态
          findOneMessageList = findOneMessageList.map(findOneItem => {
            // 查找currentSessionData.messageList中是否有相同ID的项
            const matchingItem = this.state.currentSessionData.messageList.find(
              currentItem => currentItem.id === findOneItem.id,
            );

            // 如果找到匹配项，则整合字段
            if (matchingItem) {
              // 创建一个新对象，首先包含findOneItem的所有字段
              // 然后添加matchingItem中有但findOneItem中没有的字段
              return {
                ...matchingItem, // 先放入原有数据的所有字段
                ...findOneItem, // 然后用新数据覆盖相同的字段
              };
            }

            // 如果没有找到匹配项，则返回原始的findOneItem
            return findOneItem;
          });

          // // 添加currentSessionData.messageList中有但findOneMessageList中没有的项
          // //暂时不需要这一步
          // const findOneMessageIds = findOneMessageList.map(item => item.id);
          // const additionalMessages = this.state.currentSessionData.messageList.filter(
          //   item => !findOneMessageIds.includes(item.id) && item.id // 确保有id且不在findOneMessageList中
          // );
          findOneMessageList = [
            ...this.state.currentSessionData.messageList,
            ...findOneMessageList,
          ];
        }

        //去重
        let newFindOneMessageList = findOneMessageList
          ?.reverse()
          .filter((item, index, self) => {
            if (index === self.findIndex(t => t.id === item.id)) {
              return item;
            } else if (!item.id) {
              return item;
            }
          })
          ?.reverse();

        // //赋值已读
        // if (publicStatus === 1) {
        //   newFindOneMessageList.forEach((it, index) => {
        //     newFindOneMessageList[index].status = publicStatus;
        //   });
        // }
        //转换type和role
        let resultsList = handleWorkDetail(newFindOneMessageList);
        console.log(
          'chatLayoutUpdate2',
          findOneMessageList,
          newFindOneMessageList,
          findOne,
          {
            ...findOne,
            messageList: [...resultsList],
          },
        );

        //整合
        this.setState({
          currentSessionData: {
            ...findOne,
            messageList: [...resultsList],
          },
        });
      }
    }
    //直接复制右侧ai生成的回答到输入框
    if (
      this.props.workTableCopyAnswer &&
      this.props.workTableCopyAnswer !== prevProps.workTableCopyAnswer
    ) {
      let blobUrl = this.props.workTableCopyAnswer?.content;
      //置空复制的内容
      this.props.dispatch({
        type: 'worktable/copyAnswerToWorkTable',
        payload: null,
      });
      // this.setState({
      //   // inputValue: blobUrl,
      //   inputValueAType: this.props.workTableCopyAnswer?.atype,
      // });
      this.inputRef.current?.focus();
      if ([2, 3].includes(+this.props.workTableCopyAnswer?.atype)) {
        this.createPasteEvent(blobUrl, this.props.workTableCopyAnswer?.atype);
      } else {
        this.inputRef.current?.setContent(blobUrl);
      }
    }
    //监听inputValue变化,用于发送后清空setContent
    if (
      this.state.inputValue !== prevState.inputValue &&
      this.state.inputValue === ''
    ) {
      this.inputRef.current?.setContent(this.state.inputValue);
    }
  }
  // 处理设置里开启实时翻译
  setTranslationEnable = (value, code) => {
    console.log('setTranslationEnable', value, code);
    this.setState({
      currentSessionData: {
        ...this.state.currentSessionData,
        translateStatus: value,
        translateCode: code,
      },
      // translateStatus: value,
    });
    this.props.updateSessionBySessionId(
      this.state.currentSessionData.ticketId,
      'translateStatus',
      value,
    );
    this.props.updateSessionBySessionId(
      this.state.currentSessionData.ticketId,
      'translateCode',
      code,
    );
    this.props.updateCurrentSession('translateStatus', value);
    this.props.updateCurrentSession('translateCode', code);
  };
  //消息点击事件
  // 图片预览方法 - 触发Image组件自带预览
  handleImagePreview = id => {
    this.setState({
      imagePreviewVisible: true,
      imagePreviewId: id,
    });
  };

  // 关闭图片预览
  handleImagePreviewClose = () => {
    this.setState({
      imagePreviewVisible: false,
      imagePreviewId: '',
    });
  };

  handleFunctionClick = (event, role, content) => {
    // 检查是否直接点击了图片
    const clickedElement = event.target;

    if (clickedElement.className === 'ant-image-mask-info') {
      // 如果点击的是预览图标，直接触发预览
      event.stopPropagation();
      this.handleImagePreview(content.id);
      console.log('图片预览图标被点击');
      return;
    }
    // 阻止事件冒泡
    event.stopPropagation();
    // 获取鼠标点击位置
    // 获取鼠标点击位置
    const { clientX, clientY } = event;

    // 获取messageContainer元素
    const messageContainer = document.querySelector(
      `.${styles.messageContainer}`,
    );

    // 如果找到了messageContainer元素，计算相对位置
    let relativeX = clientX;
    let relativeY = clientY;

    if (messageContainer) {
      const rect = messageContainer.getBoundingClientRect();
      relativeX = clientX - rect.left;
      relativeY = clientY - rect.top;
      console.log('相对于messageContainer的位置:', relativeX, relativeY);
    }
    // 'customer-noCopy','agent-noCopy'用于附件，图片，视频类消息没有复制功能
    console.log('popoverPosition', role, content);
    this.setState({
      popoverPosition: { top: relativeY, left: relativeX, content: content },
      isPopoverVisibleReference:
        ['agent', 'customer', 'customer-noCopy', 'agent-noCopy'].includes(
          role,
        ) &&
        ['8', '9'].includes(String(this.state.currentSessionData.channelId))
          ? true
          : false,
      isPopoverVisibleWithdraw:
        ['agent', 'agent-noCopy'].includes(role) &&
        ['8', '9'].includes(String(this.state.currentSessionData.channelId)) &&
        content.time &&
        new Date().getTime() - new Date(content.time).getTime() < 2 * 60 * 1000
          ? true
          : false,

      isPopoverVisibleCopy:
        role !== 'customer-noCopy' && role !== 'agent-noCopy' ? true : false,
      tagRef: event.target,
    });
  };
  handleClickOutside = event => {
    // 如果点击发生在浮动div外部，则隐藏它
    if (this.state.tagRef !== event.target) {
      this.setState({
        isPopoverVisibleReference: false, //引用功能
        isPopoverVisibleCopy: false, //复制功能
        isPopoverVisibleWithdraw: false, //撤回功能
      });
    }
  };
  createPasteEvent = async (url, atype) => {
    // 处理跨域问题s3地址转二进制
    this.props.dispatch({
      type: 'worktable/s3ToBinary',
      payload: {
        fileUrl: url,
      },
      callback: response => {
        if (!response || !response.includes('blob:')) {
          message.error(
            getIntl().formatMessage({
              id: 'work.order.detail.copy.error',
              defaultMessage: '复制失败',
            }),
          );
          return;
        } else {
          message.success(
            getIntl().formatMessage({
              id: 'work.order.detail.copy.success',
              defaultValue: '复制成功',
            }),
          );
          if (atype === 2) {
            this.inputRef.current?.handlePaste(
              `<div style="width:100%;display:block;"><img src="${response}" style="width:auto;max-height:100px;"></div><br>`,
            );
          } else {
            this.inputRef.current?.handlePaste(
              `<div style="width:100%;display:block;"><video src="${response}" style="width:auto;max-height:100px;" controls></video></div><br>`,
            );
          }
        }
      },
    });
    // 发送POST请求获取文件

    // await fetch(`https://${process.env.DOMAIN_NAME_OVER}/api/crm/call/recordFile/workFile`)
    //   .then(response => response.blob())
    // .then(blob => {
    //   const blobUrl = URL.createObjectURL(blob);
    //   console.log('直接获取的Blob URL:', blobUrl);
    // })
    // .catch(error => {
    //   console.error('获取资源时出错:', error);
    // });
  };

  // 结束工单
  handleFinish = () => {
    IMWebClient.disconnect({
      tenantId: this.props.user.companyId,
      sessionId: this.state.currentSessionData.ticketId,
      role:
        this.props.user.roleList?.[0]?.roleId === '1005'
          ? 'agent_admin'
          : 'agent',
    });
    IMWebClient.closeACW({
      tenantId: this.props.user.companyId,
      sessionId: this.state.currentSessionData.ticketId,
    });
    console.log('=====关闭disconnect');
    setTimeout(() => {
      this.props.queryProcessingWorkOrDerListSchedule({
        workOrderId: this.state.currentSessionData.ticketId,
        workRecordTypeCode: '',
        priorityLevelId: '',
      });
    }, 5000);
    this.setState({
      isTranslationExpanded: false,
    });
  };
  // 点击按钮打开智能总结弹框（不是acw状态）
  openAiIntelligenceSumModal = async () => {
    await this.props.dispatch({
      type: 'worktable/saveAcwAiIntelligenceSumModal',
      payload: false,
    });
    this.props.openAiIntelligenceSumModal();
  };
  // 切换回复、个人备注tab
  handleTabClick = () => {
    const { activeTab } = this.state;
    this.setState({
      activeTab: activeTab === 1 ? 2 : 1,
      inputValue: '',
      showUndo: false,
      inputValueAType: '',
    });
  };
  /**
   * 处理点击翻译按钮
   */
  handleTranslationClick = e => {
    if ([2, 3].includes(+this.props.currentSessionData.ticketStatus)) return;
    // e.nativeEvent.stopImmediatePropagation();
    this.setState({ isTranslationExpanded: !this.state.isTranslationExpanded });
  };
  /**
   * 处理切换开启关闭翻译功能
   * @param e
   */
  handleTranslationChange = e => {
    // e.nativeEvent.stopImmediatePropagation();
    const value = !this.state.currentSessionData.translateStatus;

    this.setState({
      currentSessionData: {
        ...this.state.currentSessionData,
        translateStatus: value,
      },
      // translateStatus: value,
    });
    this.props.updateSessionBySessionId(
      this.state.currentSessionData.ticketId,
      'translateStatus',
      value,
    );
    this.props.updateCurrentSession('translateStatus', value);
  };

  /**
   * 处理选择翻译语言的变化事件
   * @param e
   */
  handleTranslationCodeChange = e => {
    // e.nativeEvent.stopImmediatePropagation();
    this.setState({
      currentSessionData: {
        ...this.state.currentSessionData,
        translateCode: e,
      },
    });
    this.props.updateSessionBySessionId(
      this.state.currentSessionData.ticketId,
      'translateCode',
      e,
    );

    this.props.updateCurrentSession('translateCode', e);
    this.setState({
      isTranslationExpanded: true,
    });
  };
  // 打开快捷回复
  openQuickReply = open => {
    if ([2, 3].includes(+this.props.currentSessionData.ticketStatus)) return;
    this.setState({
      showQuickReply: open,
    });
  };

  /**
   * 处理点击上传icon按钮
   */
  handleUploadIconClick = () => {
    // 步骤1：创建input元素
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    let channelType = this.state.currentSessionData.channelType; //  当前会话渠道
    // 根据 channelType 设置 accept 属性来限制文件格式
    if (channelType === '15') {
      // 企业微信客户: 图片(JPG, PNG), 音频(AMR), 视频(MP4), 常见文件类型(PDF, Word, Excel)
      fileInput.accept =
        '.jpg,.jpeg,.png,.amr,.mp4,.pdf,.doc,.docx,.xls,.xlsx,.txt';
    } else if (channelType === '16') {
      // 微信公众号客户: 图片(BMP, PNG, JPEG, JPG, GIF), 视频(MP4), 缩略图(JPG)
      fileInput.accept = '.bmp,.png,.jpeg,.jpg,.gif,.mp4,.jpg';
    } else if (channelType === '14') {
      // Line客户: 图片(PNG, JPEG), 视频(MP4)
      fileInput.accept = '.png,.jpg,.jpeg,.mp4';
    } else if (channelType === '13') {
      // Instagram客户: 图片（.png,.jpeg,.gif）、音频（.wav）、视频（.mp4,.ogg,.avi,.mov,.webm）
      fileInput.accept = '.png,.jpg,.jpeg,.gif,.wav,.mp4,.ogg,.avi,.mov,.webm';
    } else if (channelType === '3') {
      // Facebook客户: 图片（.png,.jpeg,.gif）、音频（.wav）、视频（.mp4,.ogg,.avi,.mov,.webm）
      fileInput.accept = '.png,.jpg,.jpeg,.gif,.wav,.mp4,.ogg,.avi,.mov,.webm';
    }
    // 将input元素添加到DOM中
    document.body.appendChild(fileInput);
    // 步骤2：触发选择文件事件
    fileInput.click();
    // 步骤3：选择文件后删除input元素
    fileInput.addEventListener('change', this.handleFileUpload);
    // 从DOM中删除input元素
    fileInput.remove();
  };

  handleFileUpload = async event => {
    const file = event.target.files[0]; // 获取上传的文件
    const maxSize = 500 * 1024 * 1024;

    let channelType = this.state.currentSessionData.channelType; //  当前会话渠道
    // 根据 channelType 选择不同的检查逻辑
    let isValid = true;
    if (channelType === '15') {
      isValid = await this.handleCheckWeComFile(file); // 企业微信检查
    } else if (channelType === '16') {
      isValid = await this.handleCheckWeChatOfficialAccountFile(file); // 微信公众号检查
    } else if (channelType === '14') {
      isValid = await this.handleCheckLineFile(file); // Line检查
    } else if (channelType === '13') {
      isValid = await this.handleCheckInstagramFile(file); // Instagram检查
    } else if (channelType === '3') {
      isValid = await this.handleCheckInstagramFile(file); // Facebook检查
    }
    // 文件验证不通过，终止上传流程
    if (!isValid) {
      return;
    }
    if (file && file.size > maxSize) {
      notification.error({
        message: '文件大小超过500MB，无法上传。',
      });
      return;
    }
    const formData = new FormData();
    formData.append('file', file);

    let contentType = '';
    if (this.isImage(file.name)) {
      contentType = 'image';
    } else if (this.isVideo(file.name)) {
      contentType = 'video';
    } else if (this.isAudio(file.name)) {
      contentType = 'audio';
    } else {
      contentType = 'link';
    }

    const ticketId = this.state.currentSessionData.ticketId;
    const customerId = this.state.currentSessionData.uid;
    const contentId = uuidv4();
    // 创建一个新的取消令牌
    let source = axios.CancelToken.source();
    this.setState({ uploadCancelToken: source });

    const now = new Date();
    const year = now.getFullYear();
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now
      .getDate()
      .toString()
      .padStart(2, '0');
    const date = `${year}/${month}/${day}`;

    // 获取上传的预签名数据（用于上传使用）
    let uploadPreSignedUrlResult = await this.props.handleIMWebClientGetUploadPreSignedUrl(
      file.name,
      file.type,
      date,
    );
    console.log('上传downloadPreSignedUrlResult===', uploadPreSignedUrlResult);
    // 发送附件类型消息，返回一个消息id用于持续更新上传进度值状态
    console.log('查contentID', contentId);
    let messageId = await this.props.handleAddMessageNew({
      ticketId: ticketId,
      role:
        this.props.user.roleList?.[0]?.roleId === '1005'
          ? 'agent_admin'
          : 'agent', //  agent
      type: 'role',
      username: this.props.user.userName,
      uid: customerId,
      contentType: contentType,
      fileName: file.name,
      content: file.name,
      filePath: uploadPreSignedUrlResult.data,
      uploadProgress: 0,
      uploadPreSignedUrlResult: uploadPreSignedUrlResult.data,
      workRecordFileList: [],
      contentId: contentId,
      id: contentId,
    });
    console.log('上传messageId===', messageId);
    // 判断是否需要上传操作。
    let isCancel = false;
    // 执行上传操作...
    const upload = axios
      .put(uploadPreSignedUrlResult.data, file, {
        onUploadProgress: progressEvent => {
          // 计算进度值
          let progress = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total,
          );
          // 获取这个会话的信息。
          const currentSession = this.props.globalList.find(
            f => f.ticketId === ticketId,
          );

          if (currentSession) {
            let newMessageList = handleWorkDetail(currentSession.messageList);
            console.log(
              '上传currentSession===',
              currentSession,
              newMessageList,
            );
            // 根据messageId匹配消息更新进度值
            const updatedMessageList = newMessageList.map(message =>
              message.id === messageId
                ? { ...message, uploadProgress: progress, uploadStatus: 0 }
                : message,
            );
            console.log('上传updatedMessageList===', updatedMessageList);

            // 更新会话列表数据。
            this.props.updateSessionBySessionId(
              ticketId,
              'messageList',
              updatedMessageList,
            );
            this.setState({
              currentSessionData: {
                ...this.state.currentSessionData,
                messageList: updatedMessageList,
              },
            });
          }
        },
        cancelToken: source.token,
      })
      .catch(async () => {
        // 获取这个会话的信息。
        const currentSession = this.props.globalList.find(
          f => f.ticketId === ticketId,
        );
        if (currentSession) {
          // 删除异常的上传消息，根据消息id匹配
          const updatedMessageList = currentSession.messageList.filter(
            message => message.messageId !== messageId,
          );
          // 更新会话列表数据。
          this.props.updateSessionBySessionId(
            ticketId,
            'messageList',
            updatedMessageList,
          );
          console.log('上传updatedMessageList catch===', updatedMessageList);
          this.setState({
            currentSessionData: {
              ...this.state.currentSessionData,
              messageList: updatedMessageList,
            },
          });
        }
        isCancel = true;
      })
      .finally(async () => {
        // 重置上传所需的token信息。
        source = null;
        // 如果没有取消上传操作
        if (!isCancel) {
          // 获取下载的预签名连接（用于消息展示使用）
          let downloadPreSignedUrlResult = await getDownloadPreSignedUrl(
            file.name,
            file.type,
            this.props.user.companyId,
            date,
          );
          if (downloadPreSignedUrlResult.code !== 200) {
            return;
          }
          console.log(
            '下载downloadPreSignedUrlResult===',
            downloadPreSignedUrlResult,
          );
          const messageContent = downloadPreSignedUrlResult.data;
          const currentSession = this.props.globalList.find(
            f => f.ticketId === ticketId,
          );
          let messageItem = null;
          if (currentSession) {
            // 更新上传消息中的content的值，根据messageId匹配
            const updatedMessageList = currentSession.messageList.map(message =>
              message.id === messageId
                ? { ...message, filePath: messageContent, uploadStatus: 1 }
                : message,
            );
            messageItem = updatedMessageList.filter(f => f.id === messageId);
            console.log('上传fileMessageItem1===', updatedMessageList);

            // 更新会话列表数据。
            this.props.updateSessionBySessionId(
              ticketId,
              'messageList',
              updatedMessageList,
            );
            console.log(
              '上传fileMessageItem2===',
              this.state.currentSessionData,
            );
            this.setState({
              currentSessionData: {
                ...this.state.currentSessionData,
                messageList: updatedMessageList,
              },
            });
          }
          console.log('上传fileMessageItem3===', messageItem);
          if (messageItem) {
            console.log('查contentID', contentId);

            // 发送IM消息
            this.props.handleIMWebClientSend(
              ticketId,
              messageContent,
              {
                contentId: contentId,
                contentType: contentType,
                attachmentName: file.name,
              },
              true,
            );
            // 执行调用保存工单消息接口
            // 构建消息体数据

            let saveImData = {
              ticketId: ticketId,
              role:
                this.props.user.roleList?.[0]?.roleId === '1005'
                  ? 'agent_admin'
                  : 'agent', //  agent
              type: 'role',
              username: this.props.user.userName,
              uid: customerId,
              contentType: contentType,
              fileName: file.name,
              content: '',
              workRecordFileList: [],
              id: contentId,
            };

            // 如果是图片和视频，content直接存上传返回的地址
            if (
              contentType === 'image' ||
              contentType === 'video' ||
              contentType === 'audio'
            ) {
              if (contentType === 'image') {
                saveImData.contentType === '2';
              } else if (contentType === 'video') {
                saveImData.contentType === '3';
              } else if (contentType === 'audio') {
                saveImData.contentType === '5';
              }

              saveImData.content = messageContent;
            }

            // 如果是附件 content存附件名称，workRecordFileList需要存
            if (contentType === 'link') {
              const result = uploadParams(uploadPreSignedUrlResult.data);
              saveImData.content = decodeURIComponent(result.fileName);
              saveImData.contentType === '4';
              const fileObj = {
                fileName: result.fileName,
                fileUrl: messageContent,
                bucketName: result.bucketName,
              };
              saveImData.workRecordFileList.push(fileObj);
            }
            console.log('查contentID', saveImData);
            this.props.handleSaveImData(saveImData);
          }
        }
      });
  };

  // 判断文件后缀是否为图片
  isImage = fileName => {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif'];
    const extension = fileName
      .substring(fileName.lastIndexOf('.'))
      .toLowerCase();
    return imageExtensions.includes(extension);
  };

  // 判断文件后缀是否为视频
  isVideo = fileName => {
    const videoExtensions = ['.mp4', '.mov', '.avi'];
    const extension = fileName
      .substring(fileName.lastIndexOf('.'))
      .toLowerCase();
    return videoExtensions.includes(extension);
  };
  // 判断文件后缀是否为音频
  isAudio = fileName => {
    const audioExtensions = ['.mp3', '.wav', '.avi', '.mkv'];
    const extension = fileName
      .substring(fileName.lastIndexOf('.'))
      .toLowerCase();
    return audioExtensions.includes(extension);
  };

  // 检查企业微信文件
  handleCheckWeComFile(file) {
    const fileType = file.type;
    const fileSize = file.size;

    // 文件大小大于 5 个字节
    if (fileSize <= 5) {
      message.error('The file size must be greater than 5 bytes');
      return false;
    }

    // 企业微信客户限制
    if (fileType.includes('image')) {
      if (fileSize > 10 * 1024 * 1024) {
        message.error('Image size cannot exceed 10MB');
        return false;
      }
    } else if (fileType === 'audio/amr') {
      if (fileSize > 2 * 1024 * 1024) {
        message.error('Audio size cannot exceed 2MB');
        return false;
      }
    } else if (fileType === 'video/mp4') {
      if (fileSize > 10 * 1024 * 1024) {
        message.error('Video size cannot exceed 10MB');
        return false;
      }
    } else {
      // 其他普通文件，最大 20MB
      if (fileSize > 20 * 1024 * 1024) {
        message.error('The file size cannot exceed 20MB');
        return false;
      }
    }

    return true;
  }
  // 检查微信公众号文件
  handleCheckWeChatOfficialAccountFile(file) {
    const fileType = file.type;
    const fileSize = file.size;

    // 文件大小大于 5 个字节
    if (fileSize <= 5) {
      message.error('The file size must be greater than 5 bytes');
      return false;
    }

    // 微信公众号客户限制
    if (fileType.includes('image')) {
      if (fileSize > 10 * 1024 * 1024) {
        message.error('Image size cannot exceed 10MB');
        return false;
      }
    } else if (fileType.includes('video')) {
      if (fileSize > 10 * 1024 * 1024) {
        message.error('Video size cannot exceed 10MB');
        return false;
      }
    } else {
      message.error('Unsupported file format');
      return false;
    }

    return true;
  }
  // 检查Line文件
  handleCheckLineFile(file) {
    const fileType = file.type;
    const fileSize = file.size;

    // 文件大小大于 5 个字节
    if (fileSize <= 5) {
      message.error('The file size must be greater than 5 bytes');
      return false;
    }

    // 微信公众号客户限制
    if (fileType.includes('image')) {
      if (fileSize > 1048576) {
        message.error('Image size cannot exceed 1MB');
        return false;
      }
    } else if (fileType.includes('video')) {
      if (fileSize > 20 * 1024 * 1024) {
        message.error('Video size cannot exceed 20MB');
        return false;
      }
    } else {
      message.error('Unsupported file format');
      return false;
    }

    return true;
  }
  // 检查Instagram文件
  handleCheckInstagramFile(file) {
    const fileType = file.type;
    const fileSize = file.size;

    // 文件大小大于 5 个字节
    if (fileSize <= 5) {
      message.error('The file size must be greater than 5 bytes');
      return false;
    }

    // 微信公众号客户限制
    if (fileType.includes('image')) {
      if (fileSize > 8 * 1024 * 1024) {
        message.error('Image size cannot exceed 8MB');
        return false;
      }
    } else if (fileType.includes('audio')) {
      if (fileSize > 25 * 1024 * 1024) {
        message.error('Audio size cannot exceed 25MB');
        return false;
      }
    } else if (fileType.includes('video')) {
      if (fileSize > 20 * 1024 * 1024) {
        message.error('Video size cannot exceed 25MB');
        return false;
      }
    } else {
      message.error('Unsupported file format');
      return false;
    }

    return true;
  }

  handleCancelUpload = message => {
    if (this.state.uploadCancelToken) {
      uploadCancelToken.cancel('用户取消上传');
    }
  };

  /**
   * 发送IM消息
   * @param value
   */
  handleSendMessage = async value => {
    let { currentSessionData } = this.state;
    if (value === '' || ['<br>', '<br/>', '<br />'].includes(value)) {
      return;
    }
    // 检测是否包含图片标签
    if (
      value &&
      (value.includes('<div style="width:100%;display:block;"><img src="') ||
        value.includes('<div style="width:100%;display:block;"><video src="'))
    ) {
      this.sendInputImg(value);
    } else {
      // 将 <br> 标签替换为换行符 \n
      const formattedValue = value.replace(/<br\s*\/?>/gi, '\n\n');
      const contentId = uuidv4();
      let messageData = {
        ticketId: currentSessionData.ticketId,
        content: formattedValue,
        contactId: currentSessionData.contactId, //联系id
        contentType:
          this.state.inputValueAType == '2'
            ? 'image'
            : this.state.inputValueAType == '3'
            ? 'video'
            : 'text',
        role:
          this.props.user.roleList?.[0]?.roleId === '1005'
            ? 'agent_admin'
            : 'agent', //  agent
        type: 'role',
        username: this.props.user.userName,
        uid: currentSessionData.uid,
        id: contentId,
        status: [
          8,
          9,
          10,
          11,
          17,
          18,
          '8',
          '9',
          '10',
          '11',
          '17',
          '18',
        ].includes(currentSessionData.channelId)
          ? 0
          : 2,
        uploadStatus: 1,
        translationContent: '', // 翻译的内容
        translationStatus:
          currentSessionData.translateStatus &&
          currentSessionData.customerSourceLanguageCode
            ? 1
            : 0, // 0不翻译，1翻译中，2翻译成功，3翻译失败
        referenceContentId: this.state.referencesMessageContent.id,
        referenceReplyPerson: this.state.referencesMessageContent.username,
        referenceContent: ['4', 'link'].includes(
          String(this.state.referencesMessageContent.contentType),
        )
          ? this.state.referencesMessageContent.fileName
          : this.state.referencesMessageContent.content,
        referenceContentType: this.state.referencesMessageContent.contentType,
        referenceContentFile: ['4', 'link'].includes(
          String(this.state.referencesMessageContent.contentType),
        )
          ? this.state.referencesMessageContent.content
          : this.state.referencesMessageContent.filePath,
      };
      // 个人备注调接口
      if (this.state.activeTab == 2) {
        messageData.type = 'remark';
        messageData.role = 'system';
        // 添加消息数据到到消息列表中
        await this.props.handleAddMessageNew(messageData);
        await this.handleAddRemark(formattedValue);
      } else {
        // 添加消息数据到到消息列表中
        const messageId = await this.props.handleAddMessageNew(messageData);
        console.log(messageData.id, messageId, '是否一致');
        // 判断是否开启了实时翻译功能，是否存在存在客服源语言code
        if (
          currentSessionData.translateStatus &&
          currentSessionData.customerSourceLanguageCode &&
          messageData.type !== 'remark'
        ) {
          let translateResponse = await this.props.handleGetTranslateText(
            messageData.ticketId,
            messageId,
            messageData.content,
            currentSessionData.customerSourceLanguageCode,
            'agent',
          );
          if (translateResponse && translateResponse.code === 200) {
            const newMessageData = {
              ...messageData,
              translationContent: translateResponse.data.translatedText,
              translateLanguage: translateResponse.data.sourceLanguageCode,
            };
            this.props.handleSaveImData(newMessageData);
            this.props.handleIMWebClientSend(
              currentSessionData.ticketId,
              translateResponse.data.translatedText,
              {
                contentId: contentId,
                referenceContentId: this.state.referencesMessageContent.id,
                referenceReplyPerson: this.state.referencesMessageContent
                  .username,
                referenceContent: this.state.referencesMessageContent.content,
                referenceContentType: this.state.referencesMessageContent
                  .contentType,
                referenceContentFile: this.state.referencesMessageContent
                  .filePath,
              },
            );
          }
        } else {
          // 发送消息到IM服务端
          this.props.handleIMWebClientSend(
            currentSessionData.ticketId,
            formattedValue,
            {
              contentId: contentId,
              referenceContentId: this.state.referencesMessageContent.id,
              referenceReplyPerson: this.state.referencesMessageContent
                .username,
              referenceContent: this.state.referencesMessageContent.content,
              referenceContentType: this.state.referencesMessageContent
                .contentType,
              referenceContentFile: this.state.referencesMessageContent
                .filePath,
            },
          );
        }
      }
    }
    this.setState({
      inputValue: '',
      showUndo: false,
      inputValueAType: '',
      replyModle: false,
      isReferencesMessage: false,
      referencesMessageContent: {},
      popoverPosition: {
        top: 0,
        left: 0,
      },
    });
  };

  /**
   * 发送备注
   * @param value
   */
  handleSendNote = value => {
    this.setState({
      loading: true,
    });
    let params = {
      workRecordId: this.props.currentSessionData.ticketId,
      operationLogReason: this.state.inputNoteValue,
    };
    this.props.dispatch({
      type: 'worktable/queryWorkRemark',
      payload: params,
      callback: response => {
        this.setState({
          loading: false,
        });
        if (response.code == 200) {
          this.setState({
            inputNoteValue: '',
          });
          let { currentSessionData } = this.props;
          console.log(
            currentSessionData,
            this.props.currentSessionData,
            '列表为空===111222',
          );
          let messageData = {
            ticketId: currentSessionData.ticketId,
            content: value,
            contentType: 'text',
            role: 'system', //  agent
            type: 'remark',
            time: this.getCurrentTime(),
            username: this.props.user.userName,
            uid: currentSessionData.uid,
            translationContent: '', // 翻译的内容
            translationStatus:
              currentSessionData.translateStatus &&
              currentSessionData.customerSourceLanguageCode
                ? 1
                : 0, // 0不翻译，1翻译中，2翻译成功，3翻译失败
          };
          //添加消息数据到到消息列表中
          let newcurrentSessionData = { ...this.props.currentSessionData };
          // if (newcurrentSessionData.messageList?.length > 0) {
          //   newcurrentSessionData.messageList.push(messageData);
          // } else {
          //   newcurrentSessionData.messageList = [];
          //   newcurrentSessionData.messageList.push(messageData);
          // }
          this.setState({
            currentSessionData: newcurrentSessionData,
          });
          // 添加消息数据到到消息列表中
          this.props.handleAddNoteNew(messageData);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  getCurrentTime = () => {
    const now = new Date();
    const hours = String(now.getHours()).padStart(2, '0'); // 获取小时并补零
    const minutes = String(now.getMinutes()).padStart(2, '0'); // 获取分钟并补零
    return `${hours}:${minutes}`;
  };
  // 备注调用后端接口不走im queryWorkSummarize
  handleAddRemark = async value => {
    let params = {
      workRecordId: this.state.currentSessionData.ticketId || '',
      operationLogReason: value,
    };
    this.props.dispatch({
      type: 'worktable/queryWorkRemark',
      payload: params,
      callback: response => {
        if (response.code == 200) {
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 智能总结结果 - 确定添加消息列表
  getItelligentSummaryContent = async params => {
    const { summary, mood, toDoList } = params;
    let {
      currentSessionData,
      workRecordIdSummary,
      customerIdSummary,
    } = this.state;
    let messageData = {
      ticketId: workRecordIdSummary,
      content: '',
      contentType: 'text',
      role: 'system',
      type: 'aisummary',
      username: this.props.user.userName,
      uid: customerIdSummary,
      translationContent: '', // 翻译的内容
      translationStatus: '',
      // 修改智能总结状态
      summarizeStatus: false,
    };
    messageData.content = {
      summary: summary,
      mood: mood,
      toDoList: toDoList,
    };
    messageData.content = JSON.stringify(messageData.content);
    // 添加消息数据到到消息列表中
    await this.props.handleAddMessageNew(messageData);
    this.setState({
      // serviceSummaryModal: false,
      // isShowModal: false,
    });
  };
  checkQuickReply = value => {
    const { replyMessageBaseData } = this.state;
    // console.log(replyMessageBaseData, 'checkQuickReply');
    const newData = replyMessageBaseData.filter(item =>
      item.quickReplyText.includes(value),
    );
    // console.log(newData, 'checkQuickReply');
    if (newData.length > 0) {
      this.setState({
        replyMessageModleData: newData,
        replyModle: true,
      });
    } else {
      this.setState({
        replyMessageModleData: [],
        replyModle: false,
      });
    }
  };
  /**
   * 处理设置输入框值 查询快捷回复数据
   * @param value
   */
  handleSetInputValue = value => {
    this.inputRef.current?.setContent(value);
    this.setState({ inputValueAType: '' });
    // this.setState({ inputValue: value, inputValueAType: '' });
    if (value) {
      this.checkQuickReply(value);
    } else {
      this.setState({
        replyMessageModleData: [],
        replyModle: false,
      });
    }
  };
  /**
   * 处理设置输入框值 电话工单备注
   * @param value
   */
  handleSetInputNoteValue = value => {
    this.setState({ inputNoteValue: value });
  };
  /**
   * 处理输入框回车事件
   * @param event
   */
  handleKeyPress = event => {
    let content = this.state.inputValue.trim();
    if (
      content === '' ||
      content === '<br>' ||
      content === '<br/>' ||
      content === '<br />'
    ) {
      return;
    }
    if (event.key === 'Enter' && !event.shiftKey) {
      // 阻止默认的换行行为
      event.preventDefault();
      // 发送消息
      this.handleSendMessage(content);
    }
  };
  /**
   * 处理电话工单备注输入框回车事件
   * @param event
   */
  handleNoteKeyPress = event => {
    let content = this.state.inputNoteValue.trim();
    if (content === '') {
      return;
    }
    if (event === 'send') {
      // 发送备注
      this.handleSendNote(content);
    } else if (event.key === 'Enter' && !event.shiftKey) {
      // 阻止默认的换行行为
      event?.preventDefault();
      // 发送备注
      this.handleSendNote(content);
    }
  };
  /**
   * 处理点击重试翻译
   * @param item
   */
  handleClickRetryTranslation = async item => {
    this.props.handleClickRetryTranslation(item);
  };

  /**
   * 处理选择表情。
   * @param value 表情
   */
  handleEmojiClick = value => {
    // 使用insertAtCursor方法在当前光标位置插入表情
    this.inputRef.current?.insertAtCursor(value);
    // 设置inputValue的类型为空字符串
    this.setState({
      // inputValue: this.state.inputValue + value,
      inputValueAType: '',
    });
    // this.inputRef.current.focus();
  };
  /**
   * 处理点击表情icon按钮
   */
  handleEmojiIconClick = () => {
    this.setState({ emojiModal: !this.state.emojiModal });
  };

  /**
   * 处理关闭表情窗口
   */
  handleCloseEmojiModalClick = event => {
    event.stopPropagation();
    this.setState({ emojiModal: false });
  };
  /**
   * 处理点击满意度按钮
   */
  handleInviteRatingsClick = () => {
    let { currentSessionData } = this.state;
    // 社媒渠道、结束和acw不能点击
    if (
      currentSessionData.invitationEvaluationStatus ||
      !['8', '9'].includes(this.state.currentSessionData.channelId) ||
      [2, 3].includes(+this.state.currentSessionData.ticketStatus)
    )
      return;
    this.setState({
      currentSessionData: {
        ...currentSessionData,
        invitationEvaluationStatus: true,
      },
    });
    let ticketId = currentSessionData.ticketId;
    // 更新当前会话的邀请状态
    this.props.updateSessionBySessionId(
      ticketId,
      'invitationEvaluationStatus',
      true,
    );
    this.props.updateCurrentSession('invitationEvaluationStatus', true);
    let customerId = this.props.globalList.find(
      session => session.ticketId === ticketId,
    ).uid;
    this.props.handleIMWebClientInviteRatings(ticketId);
    this.props.handleAddMessageNew({
      ticketId: ticketId,
      uid: customerId,
      type: 'system',
      role: 'system',
      content: getIntl().formatMessage({ id: 'im.chat.rating' }),
      contentType: 'text',
    });
  };

  // 关闭聊天
  handleCloseChat = () => {
    IMWebClient.closeACW({
      tenantId: this.props.user.companyId,
      sessionId: this.state.currentSessionData.ticketId,
    });
    this.setState({
      isTranslationExpanded: false,
    });
  };
  // 获取工单详情接口
  fetchTicketDetailData = (ticketId, channelConfigId) => {
    let params = {
      channelTypeId: channelConfigId,
      workRecordId: ticketId, //
    };
    // this.setState({
    //   loading: true,
    // });
    this.props.dispatch({
      type: 'workOrderCenter/newQueryWorkOrderDetail',
      payload: params,
      callback: response => {
        let { code, data, msg } = response;
        if (code === 200) {
          //找到当前对话信息
          // let workItem = {
          //   ...this.props.globalList.find(item => item.ticketId === ticketId),
          // };
          //初始化对话信息时更新messageList
          // if (data.dataList && data.dataList.length > 0) {
          //   workItem.messageList = handleWorkDetail(
          //     data.dataList,
          //     this.props.currentSessionData.messageList,
          //   ); // 更新messageList
          // }
          //注释xuwen之前逻辑,整合未读消息id到已有messageList,用做判重
          // let newData = this.props.currentSessionData.messageList?.filter(
          //   item => {
          //     if (item.status === 0) {
          //       return item.id;
          //     }
          //   },
          // );
          // console.log(newData, 'workItem2');

          // if (workItem.messageList) {
          //   workItem.messageList = [...workItem.messageList, ...newData];
          // } else {
          //   workItem.messageList = [...newData];
          // }
          // console.log(workItem, 'workItem3');

          //  else if (!data.dataList.length) {
          //   workItem.messageList = [
          //     {
          //       id: '',
          //       uid: null,
          //       username: null,
          //       role: '',
          //       type: '',
          //       contentType: '',
          //       content: '',
          //       fileName: '',
          //       status: 1, //已读状态
          //       time: '',
          //       translationStatus: false,
          //       translationContent: '',
          //       uploadProgress: 0,
          //     },
          //   ];
          // } else {
          //   workItem.messageList = [];
          // }
          // console.log({ ...workItem }, '{ ...workItem }');
          this.setState({
            // loading: false,
            socialMediaTimeStatus: data.socialMediaTimeStatus,
            // currentSessionData: { ...workItem },
          });
          //更新父组件globalList，同步离线消息
          // this.props.setGlobalMessageList(workItem);
          // setTimeout(() => {
          //   this.setState({
          //     loading: false,
          //   });
          // }, 1200);
        }
        // else {
        //   this.setState({
        //     loading: false,
        //     currentSessionData: {
        //       ...this.props.currentSessionData,
        //       messageList: [
        //         {
        //           id: '',
        //           uid: null,
        //           username: null,
        //           role: '',
        //           type: '',
        //           contentType: '',
        //           content: '',
        //           fileName: '',
        //           status: 1, //已读状态
        //           time: '',
        //           translationStatus: false,
        //           translationContent: '',
        //           uploadProgress: 0,
        //         },
        //       ],
        //     },
        //   });
        // }
      },
    });
  };

  // 加载工单信息
  loadTicketInfo = () => {
    if (this.props.channelConfigId) {
      this.fetchTicketDetailData(
        this.props.currentSessionData.ticketId,
        this.props.channelConfigId,
      );
    }
  };
  //查询坐席头像
  queryUserProfilePicture = channelId => {
    this.props.dispatch({
      type: 'userManagement/getUserProfilePictureWorkbench',
      payload: {
        userId: this.props.user.userId,
        companyId: this.props.user.companyId,
        channelConfigId: channelId,
      },
      callback: response => {
        if (response.code === 200) {
          this.setState({
            nickName: response.data.nickName,
            userProfilePicture: response.data.profilePicturePath,
          });
        }
      },
    });
  };
  // 组件挂载时
  componentDidMount() {
    // 翻译的语言列表
    const googleLanguage = JSON.parse(localStorage.getItem('languageLocal'));
    this.setState({
      translationCodeList: googleLanguage,
    });
    // 组件挂载时滚动到底部
    this.scrollToBottom();
    // 组件挂载时查询快捷回复
    this.listQuickReply('');
    // 添加全局点击事件监听器
    document.addEventListener('click', this.handleClickOutside);
  }
  componentWillUnmount() {
    // 移除全局点击事件监听器
    document.removeEventListener('click', this.handleClickOutside);
  }
  handleCloseTranslate = () => {
    this.setState({
      isTranslationExpanded: false,
    });
  };

  scrollToBottom = () => {
    console.log('scrollToBottom');

    try {
      const container = this.imContainerRef.current;
      const clientContainer = this.imContainerRefs.current;
      if (container) {
        const scrollHeight = container.scrollHeight;
        const height = clientContainer.clientHeight;
        const maxScrollTop = scrollHeight - height;
        clientContainer.scrollTop = maxScrollTop > 0 ? maxScrollTop + 200 : 0;
      }
    } catch (e) {
      console.log('IMChat scrollToBottom Exception', e);
    }
  };

  // 滚动到置顶
  scrollToTop = () => {
    try {
      const container = this.imContainerRefs.current;
      if (container) {
        container.scrollTop = 0;
      }
    } catch (e) {
      console.log('IMChat scrollToPosition Exception', e);
    }
  };
  //加入聊天
  handleJoinChat = () => {
    this.setState({
      currentSessionData: {
        ...this.state.currentSessionData,
        isAdminJoinChat: true,
      },
    });
    let ticketId = this.state.currentSessionData.ticketId;
    // 更新当前会话的邀请状态
    this.props.updateSessionBySessionId(ticketId, 'isAdminJoinChat', true);
    this.props.updateCurrentSession('isAdminJoinChat', true);
    //坐席管理员加入聊天
    IMWebClient.adminJoinChat({
      tenantId: this.props.user.companyId,
      sessionId: this.state.currentSessionData.ticketId,
    });
  };
  getChatLayout = () => {
    //找到当前对话信息
    let workItem = {
      ...this.props.globalList.find(
        item => item.ticketId === this.props.currentSessionData.ticketId,
      ),
    };

    //初始化对话信息时更新messageList
    if (
      this.props.currentSessionData.messageList &&
      this.props.currentSessionData.messageList.length > 0
    ) {
      workItem.messageList = handleWorkDetail(
        this.props.currentSessionData.messageList,
      ); // 更新messageList
    }
    this.setState(
      {
        currentSessionData: { ...workItem },
      },
      () => {
        this.scrollToBottom();
        console.log(
          '找到当前对话信息queryWorkRecordInfo',
          workItem,
          this.state.currentSessionData,
          this.props.globalList,
          this.props.currentSessionData,
        );
      },
    );
  };
  // 查询工单详情
  queryWorkOrderDetail = () => {
    // this.setState({
    //   loading: true,
    // });
    let ticketId =
      this.props.workTableTabValue === 1
        ? this.props.ticketId
        : this.props.currentSessionData?.ticketId;
    if (
      ticketId == undefined ||
      ticketId == 'undefined' ||
      typeof ticketId == 'undefined'
    )
      return;
    this.props.dispatch({
      type: 'workOrderCenter/workbenchTicketDetails',
      payload: ticketId,
      callback: response => {
        if (response.code == 200) {
          this.setState({
            workTypeDetail: response.data,
          });
          // setTimeout(() => {
          //   this.setState({
          //     loading: false,
          //   });
          // }, 500);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 输入时出现快捷回复
  listQuickReply = value => {
    this.setState({});
    this.props.dispatch({
      type: 'workOrderCenter/allQuickReply',
      payload: {
        quickReplyText: '',
      },
      callback: response => {
        if (response) {
          // console.log(response, 'listQuickReply');
          let { code, data, msg } = response;
          if (code === 200) {
            if (data && data.length > 0) {
              this.setState({
                replyMessageBaseData: data ? data : [],
                // replyMessageModleData: data ? data : [],
                // replyModle: true,
              });
            } else {
              this.setState({
                replyMessageBaseData: [],
                // replyModle: false,
              });
            }
          } else {
            this.setState({
              replyMessageBaseData: [],
              // replyModle: false,
            });
          }
        }
      },
    });
  };

  // 鼠标移入时内容同步到输入框
  handleMouseInterUpdateInput = data => {
    this.setState({
      // inputValue: data.quickReplyText,
      inputValueAType: '',
      replyModle: false,
      showQuickReply: false,
    });
    this.inputRef.current?.focus();
    this.inputRef.current?.setContent(data.quickReplyText);
  };

  // 点击其他区域关闭快捷回复提示
  handleCloseReplyModel = () => {
    this.setState({
      replyModle: false,
      showQuickReply: false,
    });
  };
  // 保存客户资料中号码
  handleSelect = (value, part) => {
    console.log(value, part, '点击电话号码');
    // 存储电话到客户资料
    if (value === 2) {
      // 先检查是否重复
      const data = {
        customerId: this.props.currentSessionData.uid,
        telephone: part,
      };
      this.props.dispatch({
        type: 'worktable/updateCustomerPhone',
        payload: data,
        callback: res => {
          this.setState({
            openStates: {
              parentIndex: false,
            },
          });
          if (res.code === 200) {
            notification.success({
              message: res.msg,
            });
          } else {
            notification.error({
              message: res.msg,
            });
          }
        },
      });
    }
    // 拨打电话
    if (value === 1) {
      if (part) {
        localStorage.setItem('currentPhone', JSON.stringify(part));
        this.props.handleClickCurrentPhone(part);
      }
    }
  };
  // navBar新增电话拨出
  handleNavBarPhone = values => {
    if (values) {
      localStorage.setItem('currentPhone', JSON.stringify(values));
      this.props.handleClickCurrentPhone(values);
    }
  };
  handleOpenChange = value => {
    const numbers = value.replace(/\D/g, ''); // 电话号码
    if (numbers.length > 0) {
      this.setState(prevState => ({
        openStates: {
          ...prevState.openStates,
          parentIndex: true,
        },
      }));
    }
  };
  // 语法纠错打开弹窗
  openGrammer = open => {
    if ([2, 3].includes(+this.props.currentSessionData.ticketStatus)) return;
    this.setState({
      grammerState: open,
    });
  };
  // 选中某个语法
  handleSelectGrammer = value => {
    if (!this.state.inputValue) {
      notification.error({
        message: getIntl().formatMessage({
          id: 'new.worktable.chat.ticket.notion.error',
        }),
      });
      return;
    }
    if (+this.state.activeTab === 1) {
      let params = {
        format: 'string',
        content: this.state.inputValue,
      };
      this.setState({
        inputLoading: true,
      });
      if (+value === 1) {
        this.props.dispatch({
          type: 'worktable/grammarCheckText',
          payload: params,
          callback: response => {
            if (response.code == 200) {
              this.inputRef.current?.focus();
              if (this.state.inputValue !== response.data) {
                //存放aigc记录
                let messageStack = this.state.historyMessage;
                messageStack.push(this.state.inputValue);
                this.setState({
                  historyMessage: messageStack,
                  showUndo: true,
                  newAIGCcontent: response.data,
                });
                this.inputRef.current?.clear();
                this.inputRef.current?.setContent(response.data);
              }
              this.setState({
                // inputValue: response.data,
                inputValueAType: '',
                inputLoading: false,
              });
            } else {
              this.setState({
                inputLoading: false,
              });
              notification.error({
                message: response.msg,
              });
            }
          },
        });
      } else if (+value === 2) {
        this.props.dispatch({
          type: 'worktable/aiGentleText',
          payload: params,
          callback: response => {
            if (response.code == 200) {
              this.inputRef.current?.focus();
              if (this.state.inputValue !== response.data) {
                //存放aigc记录
                let messageStack = this.state.historyMessage;
                messageStack.push(this.state.inputValue);
                this.setState({
                  historyMessage: messageStack,
                  showUndo: true,
                  newAIGCcontent: response.data,
                });
                this.inputRef.current?.clear();
                this.inputRef.current?.setContent(response.data);
              }
              this.setState({
                // inputValue: response.data,
                inputValueAType: '',
                inputLoading: false,
              });
            } else {
              this.setState({
                inputLoading: false,
              });
              notification.error({
                message: response.msg,
              });
            }
          },
        });
      } else if (+value === 3) {
        this.props.dispatch({
          type: 'worktable/aiNeutralText',
          payload: params,
          callback: response => {
            if (response.code == 200) {
              this.inputRef.current?.focus();
              if (this.state.inputValue !== response.data) {
                //存放aigc记录
                let messageStack = this.state.historyMessage;
                messageStack.push(this.state.inputValue);
                this.setState({
                  historyMessage: messageStack,
                  showUndo: true,
                  newAIGCcontent: response.data,
                });
                this.inputRef.current?.clear();
                this.inputRef.current?.setContent(response.data);
              }
              this.setState({
                // inputValue: response.data,
                inputValueAType: '',
                inputLoading: false,
              });
            } else {
              this.setState({
                inputLoading: false,
              });
              notification.error({
                message: response.msg,
              });
            }
          },
        });
      } else if (+value === 4) {
        this.props.dispatch({
          type: 'worktable/formalText',
          payload: params,
          callback: response => {
            if (response.code == 200) {
              this.inputRef.current?.focus();
              if (this.state.inputValue !== response.data) {
                //存放aigc记录
                let messageStack = this.state.historyMessage;
                messageStack.push(this.state.inputValue);
                this.setState({
                  historyMessage: messageStack,
                  showUndo: true,
                  newAIGCcontent: response.data,
                });
                this.inputRef.current?.clear();
                this.inputRef.current?.setContent(response.data);
              }
              this.setState({
                // inputValue: response.data,
                inputValueAType: '',
                inputLoading: false,
              });
            } else {
              this.setState({
                inputLoading: false,
              });
              notification.error({
                message: response.msg,
              });
            }
          },
        });
      }
    }
    this.setState({
      grammerState: false,
    });
  };

  // 简化写法
  handleSimplify = () => {
    if (!this.state.inputValue) {
      notification.error({
        message: getIntl().formatMessage({
          id: 'new.worktable.chat.ticket.notion.error',
        }),
      });
      return;
    }
    const summaryResult = this.handleCountChatMsg();
    this.setState({
      simplify: true,
      inputLoading: true,
    });
    let params = {
      format: 'string',
      content: this.state.inputValue,
      context: summaryResult ? summaryResult : '',
    };
    this.props.dispatch({
      type: 'worktable/aiBeautifyText',
      payload: params,
      callback: response => {
        if (response.code == 200) {
          this.inputRef.current?.focus();
          if (this.state.inputValue !== response.data) {
            //存放aigc记录
            let messageStack = this.state.historyMessage;
            messageStack.push(this.state.inputValue);
            this.setState({
              historyMessage: messageStack,
              showUndo: true,
              newAIGCcontent: response.data,
            });
            this.inputRef.current?.clear();
            this.inputRef.current?.setContent(response.data);
          }
          this.setState({
            simplify: false,
            // inputValue: response.data,
            inputValueAType: '',
            inputLoading: false,
          });
        } else {
          this.setState({
            simplify: false,
            inputLoading: false,
          });
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 扩写法
  handleExpand = () => {
    if (!this.state.inputValue) {
      notification.error({
        message: getIntl().formatMessage({
          id: 'new.worktable.chat.ticket.notion.error',
        }),
      });
      return;
    }
    const summaryResult = this.handleCountChatMsg();
    this.setState({
      expand: true,
      inputLoading: true,
    });
    let params = {
      format: 'string',
      content: this.state.inputValue,
      context: summaryResult ? summaryResult : '',
    };
    this.props.dispatch({
      type: 'worktable/aiExpansionText',
      payload: params,
      callback: response => {
        if (response.code == 200) {
          this.inputRef.current?.focus();
          if (this.state.inputValue !== response.data) {
            //存放aigc记录
            let messageStack = this.state.historyMessage;
            messageStack.push(this.state.inputValue);
            this.setState({
              historyMessage: messageStack,
              showUndo: true,
              newAIGCcontent: response.data,
            });
            this.inputRef.current?.clear();
            this.inputRef.current?.setContent(response.data);
          }
          this.setState({
            expand: false,
            // inputValue: response.data,
            inputValueAType: '',
            inputLoading: false,
          });
        } else {
          this.setState({
            expand: false,
            inputLoading: false,
          });
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 缩写
  handleAbbrev = () => {
    if (!this.state.inputValue) {
      notification.error({
        message: getIntl().formatMessage({
          id: 'new.worktable.chat.ticket.notion.error',
        }),
      });
      return;
    }
    this.setState({
      abbrev: true,
      inputLoading: true,
    });
    let params = {
      format: 'string',
      content: this.state.inputValue,
    };
    this.props.dispatch({
      type: 'worktable/aiConciseText',
      payload: params,
      callback: response => {
        if (response.code == 200) {
          this.inputRef.current?.focus();
          if (this.state.inputValue !== response.data) {
            //存放aigc记录
            let messageStack = this.state.historyMessage;
            messageStack.push(this.state.inputValue);
            this.setState({
              historyMessage: messageStack,
              showUndo: true,
              newAIGCcontent: response.data,
            });
            this.inputRef.current?.clear();
            this.inputRef.current?.setContent(response.data);
          }
          this.setState({
            abbrev: false,
            // inputValue: response.data,
            inputValueAType: '',
            inputLoading: false,
          });
        } else {
          this.setState({
            abbrev: false,
            inputLoading: false,
          });
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  //whatsApp模版
  handleWhatsApp = () => {
    this.setState(
      {
        whatsApp: !this.state.whatsApp,
        whatsAppType: null,
        whatsAppInput: '',
        whatsAppParameters: {},
        whatsAppMessageItem: null,
        whatsAppMessageItemContent: '',
      },
      () => {
        if (this.state.whatsApp) {
          this.queryWhatsAppMessage();
        }
      },
    );
  };
  //whatsApp模版查询
  queryWhatsAppMessage = () => {
    this.setState({
      whatsAppLoading: true,
    });
    this.props.dispatch({
      type: 'worktable/queryWhatsAppMessage',
      payload: {
        templateType: this.state.whatsAppType ?? '',
        channelId: this.state.currentSessionData.channelConfigId,
        content: this.state.whatsAppInput,
      },
      callback: response => {
        if (response.code == 200) {
          this.setState({
            whatsAppMessageList: response.data,
            whatsAppLoading: false,
          });
        } else {
          this.setState({
            whatsAppLoading: false,
          });
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  //whatsApp模版输入
  onChangeInputWhatsApp = e => {
    this.setState({
      whatsAppInput: e.target.value,
    });
  };
  //whatsApp模版回车
  onPressEnterWhatsApp = () => {
    this.queryWhatsAppMessage();
  };
  /**
   * 撤销aigc消息
   */
  handleUndoClick = () => {
    this.inputRef.current?.focus();
    let messageStack = [...this.state.historyMessage];
    let text = messageStack.pop();
    if (this.state.inputValue !== text) {
      this.setState({
        historyMessage: messageStack,
        showUndo: false,
      });
      this.inputRef.current?.clear();
      this.inputRef.current?.setContent(text);
    }
  };
  setKnowledgeAnswer = e => {
    console.log(e, '调用了chatLayout');
  };

  // 新版智能总结
  handleAiIntelligenceSummary = () => {
    const summaryResult = this.handleCountChatMsg();
    if (summaryResult.length > 0) {
      let params = {
        contentQuestion: summaryResult || '',
        workRecordId: this.props.currentSessionData.ticketId,
      };
      this.setState({
        loadingSummaryModal: true,
        workRecordIdSummary: this.props.currentSessionData.ticketId,
        customerIdSummary: this.props.currentSessionData.uid,
      });
      localStorage.setItem(
        'workRecordIdSummary',
        this.props.currentSessionData.ticketId,
      );
      this.props.dispatch({
        type: 'worktable/querySummary',
        payload: params,
        callback: response => {
          if (response.code == 200) {
            let contentList = response.data;
            let mood;
            if (contentList.mood == 1 || contentList.mood == 3) {
              mood = contentList.mood;
            } else {
              mood = 2;
            }
            this.setState(
              {
                contentSummary: contentList.summary,
                customerMood: mood,
                waitExecuteList: contentList.toDoList,
                // loadingSummaryModal: false,
              },
              () => {
                this.handleResolved();
              },
            );
          } else if ([60001, 60002, 60003].includes(response.code)) {
            this.setState({
              loadingSummaryModal: false,
            });
            notification.error({
              message: response.msg,
            });
          } else {
            this.setState({
              loadingSummaryModal: false,
            });
            notification.error({
              message: response.msg,
            });
          }
        },
      });
    } else {
      notification.warning({
        message: getIntl().formatMessage({
          id: 'work.order.detail.effective.content.tips',
          defaultValue: '暂无有效内容用来总结！',
        }),
      });
    }
  };
  // 拼接聊天内容
  handleCountChatMsg = () => {
    const { currentSessionData } = this.state;
    let extractedTextsNew1 = '';
    if (currentSessionData && currentSessionData.messageList) {
      for (let i = 0; i < currentSessionData.messageList.length; i++) {
        let content = currentSessionData.messageList[i].content;
        let contentType = currentSessionData.messageList[i].contentType;
        let replyType = currentSessionData.messageList[i].role;
        let type = currentSessionData.messageList[i].type;
        const parser = new DOMParser();
        const doc = parser.parseFromString(content, 'text/html');
        let textNodes =
          (type !== 'remark' &&
            type !== 'system' &&
            type !== 'aisummary' &&
            type !== 'aiAgent' &&
            contentType === 'text') ||
          contentType === 'markdown'
            ? doc.body.innerText.trim().split('\n')
            : '';
        // let newTextNodes=''
        if (replyType == 'agent') {
          // 客服
          textNodes = 'agent:' + textNodes + '\n';
        } else if (replyType == 'customer') {
          // 客户
          textNodes = 'customer:' + textNodes + '\n';
        }
        extractedTextsNew1 += textNodes;
        // currentSessionData.messageList[i].translationContent = null;
      }
    }
    return extractedTextsNew1;
  };
  // 保存和继续跟进走保存接口，但acw状态继续跟进还要掉解决工单接口
  handleResolved = type => {
    let { contentSummary, customerMood, waitExecuteList } = this.state;
    // workStatus
    let ticketId = localStorage.getItem('workRecordIdSummary');
    let params = {
      workRecordId: ticketId,
      contentSummary: contentSummary,
      customerMood: customerMood,
      waitExecuteList: waitExecuteList,
      workStatus: true,
      // 智能总结保存或者修改 1、新增  2、修改
      interfaceType: 1,
    };

    this.setState({
      loadingSummaryModal: true,
    });
    this.props.dispatch({
      type: 'worktable/saveWorkSummarizeNew',
      payload: params,
      callback: response => {
        if (response.code == 200) {
          let contentList = response.data;
          let mood;
          if (+contentList.mood == 1 || +contentList.mood == 3) {
            mood = +contentList.mood;
          } else {
            mood = 2;
          }
          this.setState({
            // intelligentSummaryModal: false,
            // manualSummaryModal: false,
            // serviceSummaryModal: false,
            // initShowServiceSummaryModal: false,
            contentSummary: contentList.summary,
            customerMood: mood,
            waitExecuteList: contentList.toDoList,
            selectWaitExecuteList: [],
            loadingSummaryModal: false,
          });
          // this.props.getItelligentSummaryContent(contentList); //智能总结结果 确定时将副本值返回给chatlayout
          this.getItelligentSummaryContent(contentList); //智能总结结果 确定时将副本值返回给chatlayout
          notification.success({
            message: response.msg,
          });
          localStorage.removeItem('workRecordIdSummary');
        } else {
          notification.error({
            message: response.msg,
          });
          this.setState({
            loadingSummaryModal: false,
          });
        }
      },
    });
  };

  // 点击修改智能总结内容
  handleClickEditor = (detailDataList, itemData) => {
    let newContent = JSON.parse(itemData.content);
    detailDataList = detailDataList.map(item =>
      item === itemData ? { ...item, summarizeStatus: true } : item,
    );
    let currentSessionData1 = { ...this.state.currentSessionData };
    currentSessionData1.messageList = detailDataList;
    let mood;
    if (+newContent.mood === 1 || +newContent.mood === 3) {
      mood = newContent.mood;
    } else {
      mood = 2;
    }
    const filteredIds = newContent.toDoList
      ?.filter(item => item.waitExecuteStatus === 1) // 过滤 status 为 1 的项
      .map(item => item.waitExecuteId);
    this.setState({
      currentSessionData: currentSessionData1,
      contentSummary: newContent.summary,
      customerMood: mood,
      waitExecuteList: newContent.toDoList,
      selectWaitExecuteList: filteredIds,
    });
  };
  // 获取人工总结内容
  handleChangeTextAreaValue = e => {
    this.setState({
      contentSummary: e.target.value,
    });
  };
  // 切换客户心情
  handleChangeMood = customerMood => {
    this.setState({
      customerMood: customerMood,
    });
  };
  changeTodoInput = e => {
    this.setState({
      waitExecuteEvent: e.target.value,
    });
  };
  // 显示添加待办事项输入框
  handleShowAddTodoList = () => {
    this.setState({
      addTodoStatus: true,
    });
  };
  // 回车添加待办事项
  handleAddTodoList = e => {
    let waitExecuteEvent1 = e.target.value;
    let waitExecuteList = [...this.state.waitExecuteList];
    if (waitExecuteEvent1) {
      if (waitExecuteList.length > 0) {
        let repatValue = waitExecuteList.find((value, index) => {
          if (value.waitExecuteEvent == waitExecuteEvent1) {
            notification.error({
              message: '不能添加重复的待办事项',
            });
            return true;
          }
        });
        if (!repatValue) {
          let waitExecuteId = this.getCode();
          let item = {
            waitExecuteEvent: waitExecuteEvent1,
            waitExecuteStatus: 0,
            waitExecuteId: waitExecuteId,
          };
          waitExecuteList.push(item);
          this.setState({
            addTodoStatus: false,
            waitExecuteEvent: '',
            waitExecuteList: waitExecuteList,
          });
        }
      } else {
        let waitExecuteId = this.getCode();
        let item = {
          waitExecuteEvent: waitExecuteEvent1,
          waitExecuteStatus: 0,
          waitExecuteId: waitExecuteId,
        };
        waitExecuteList.push(item);
        this.setState({
          addTodoStatus: false,
          waitExecuteEvent: '',
          waitExecuteList: waitExecuteList,
        });
      }
    } else {
      notification.error({
        message: '待办事项内容不能为空',
      });
    }
  };

  getRandom = (min, max) => {
    return Math.round(Math.random() * (max - min) + min);
  };

  getCode = () => {
    let code = '';
    for (let i = 0; i < 6; i++) {
      let type = this.getRandom(1, 3);
      switch (type) {
        case 1:
          code += String.fromCharCode(this.getRandom(48, 57)); //数字
          break;
        case 2:
          code += String.fromCharCode(this.getRandom(65, 90)); //大写字母
          break;
        case 3:
          code += String.fromCharCode(this.getRandom(97, 122)); //小写字母
          break;
      }
    }
    return code;
  };
  // 删除待办事项
  handleDeleteTodoList = waitExecuteId => {
    let waitExecuteList = [...this.state.waitExecuteList];
    const filteredArray = waitExecuteList.filter(
      item => item.waitExecuteId !== waitExecuteId,
    );
    this.setState({
      waitExecuteList: filteredArray,
    });
  };
  // 勾选已解决待办事项
  resolvedTodoList = checkedValues => {
    let selectWaitExecuteList = [...this.state.selectWaitExecuteList];
    let waitExecuteList = [...this.state.waitExecuteList];
    const arr = [...selectWaitExecuteList, ...checkedValues];
    const newArr = arr.filter(t => {
      return !(selectWaitExecuteList.includes(t) && checkedValues.includes(t));
    });
    let value = selectWaitExecuteList.indexOf(newArr.toString());
    if (value < 0) {
      let waitExecuteId = newArr.toString();
      const newItem = [...selectWaitExecuteList, waitExecuteId];
      for (let i = 0; i < waitExecuteList.length; i++) {
        if (waitExecuteId === waitExecuteList[i].waitExecuteId) {
          waitExecuteList[i].waitExecuteStatus = 1;
          this.setState({
            waitExecuteList: waitExecuteList,
          });
        }
      }
      this.setState({
        selectWaitExecuteList: newItem,
      });
    } else {
      let waitExecuteId = newArr.toString();
      const filteredArray = selectWaitExecuteList.filter(
        item => item !== waitExecuteId,
      );
      for (let i = 0; i < waitExecuteList.length; i++) {
        if (waitExecuteId === waitExecuteList[i].waitExecuteId) {
          waitExecuteList[i].waitExecuteStatus = 0;
          this.setState({
            waitExecuteList: waitExecuteList,
          });
        }
      }
      this.setState({
        selectWaitExecuteList: filteredArray,
      });
    }
  };
  /**
   * 处理知识库复制图片，视频发送事件
   */
  sendInputImg = value => {
    console.log('sendInputImg', value);
    // 检测value中是否包含blob类型的图片或视频URL
    if (value && value.includes('blob:')) {
      // 创建一个数组来存储所有匹配的对象，包含URL和类型
      const blobMatches = [];

      // 匹配图片的blob URL
      const imgRegex = /<div[^>]*><img[^>]*src="(blob:[^"]+)"[^>]*><\/div>/g;
      let imgMatch;
      while ((imgMatch = imgRegex.exec(value)) !== null) {
        blobMatches.push({
          match: imgMatch[1], // blob URL
          type: 'image',
        });
      }

      // 匹配视频的blob URL
      const videoRegex = /<div[^>]*><video[^>]*src="(blob:[^"]+)"[^>]*><\/video><\/div>/g;
      let videoMatch;
      while ((videoMatch = videoRegex.exec(value)) !== null) {
        blobMatches.push({
          match: videoMatch[1], // blob URL
          type: 'video',
        });
      }

      console.log(`检测到${blobMatches.length}个blob媒体文件`, blobMatches);

      // 处理所有匹配到的blob URL
      if (blobMatches && blobMatches.length > 0) {
        blobMatches.forEach(item => {
          const blobUrl = item.match;
          const fileType = item.type;
          console.log('处理blob媒体文件:', blobUrl, '类型:', fileType);

          // 从blob URL获取文件
          fetch(blobUrl)
            .then(response => response.blob())
            .then(blob => {
              // 创建File对象
              const fileExtension = fileType === 'image' ? 'png' : 'mp4';
              const fileName = `${fileType}_${new Date().getTime()}.${fileExtension}`;
              const mimeType = fileType === 'image' ? 'image/png' : 'video/mp4';

              const file = new File([blob], fileName, {
                type: mimeType,
              });

              // 创建伪造的事件对象
              const mockEvent = {
                target: {
                  files: [file],
                },
              };

              // 调用handleFileUpload处理文件上传
              this.handleFileUpload(mockEvent);
            })
            .catch(error => {
              console.error(`从blob URL获取${fileType}文件失败:`, error);
            });
        });
      }
      //处理文字
      // 提取图片外的文本内容
      let textContent = value
        .replace(/<div[^>]*>.*?<img[^>]*>.*?<\/div>/g, '') // 移除包含图片的div
        .replace(/<br\s*\/?>/gi, '\n\n') // 将<br>替换为换行符
        .replace(/<[^>]*>/g, '') // 移除所有HTML标签
        .trim();
      this.handleSendMessage(textContent);
      return;
    }
  };
  /**
   * div输入
   */
  handleContentInput = value => {
    console.log('content', value, this.state.historyMessage);
    if (value !== this.state.inputValue) {
      // 当输入内容时保持光标位置，防止光标回到开头
      this.setState({ inputValue: value, inputValueAType: '' });
      if (value) {
        this.checkQuickReply(value);
      } else {
        this.setState({
          replyMessageModleData: [],
          replyModle: false,
        });
      }
      //aigc生成的内容被重新编辑，则隐藏撤回按钮
      if (this.state.showUndo && this.state.newAIGCcontent !== value) {
        this.setState({
          showUndo: false,
        });
      }
    }
  };

  // 取消(不掉接口)
  handleContinuingFollow = itemData => {
    // 只修改当前 item，不修改其他项
    let currentSessionData = { ...this.state.currentSessionData };
    const updatedList = currentSessionData.messageList?.map(detailItem => {
      if (detailItem === itemData) {
        return {
          ...detailItem,
          summarizeStatus: false,
        };
      }
      return detailItem; // 其他项保持不变
    });
    currentSessionData.messageList = updatedList;
    this.setState({
      addTodoStatus: false,
      waitExecuteEvent: '',
      customerMood: 2,
      waitExecuteList: [],
      selectWaitExecuteList: [],
      currentSessionData: currentSessionData,
    });
  };
  // 保存修改智能总结接口
  handleEditorSave = itemData => {
    let {
      currentSessionData,
      contentSummary,
      customerMood,
      waitExecuteList,
    } = this.state;
    // workStatus
    let params = {
      workRecordId: currentSessionData.ticketId,
      contentSummary: contentSummary,
      customerMood: customerMood,
      waitExecuteList: waitExecuteList,
      workStatus: true,
      // 智能总结保存或者修改 1、新增  2、修改
      interfaceType: 2,
    };
    this.props.dispatch({
      type: 'worktable/saveWorkSummarizeNew',
      payload: params,
      callback: response => {
        if (response.code == 200) {
          let contentList = response.data;
          let mood;
          if (+contentList.mood == 1 || +contentList.mood == 3) {
            mood = +contentList.mood;
          } else {
            mood = 2;
          }
          // setIntelligentSummaryModal(false);
          // setServiceSummaryModal(false);
          // setManualSummaryModal(false);
          // setInitShowServiceSummaryModal(false);
          this.setState({
            contentSummary: contentList.summary,
            customerMood: mood,
            waitExecuteList: contentList.toDoList,
            selectWaitExecuteList: [],
          });
          itemData.content = JSON.stringify(contentList);
          let detailDataList = currentSessionData.messageList.map(item =>
            item === itemData ? { ...item, summarizeStatus: false } : item,
          );
          let currentSessionData1 = { ...this.state.currentSessionData };
          currentSessionData1.messageList = detailDataList;
          this.setState({
            currentSessionData: currentSessionData1,
          });
          // 更新会话列表数据。
          this.props.updateSessionBySessionId(
            currentSessionData.ticketId,
            'messageList',
            detailDataList,
          );

          notification.success({
            message: response.msg,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  handleHiddenEye = () => {
    localStorage.setItem('worktable_setting_ai', 'false');
    this.setState({
      worktable_setting_ai: false,
    });
  };

  processMarkdownText = (
    node,
    edit = false,
    whatsAppParameters = null,
    type,
  ) => {
    if (!node || !node.children) return null;

    return node.children.map((child, index) => {
      if (child.type === 'text') {
        // 处理文本节点
        if (!child.value) return null;

        // 将文本分割成普通文本和 {{}} 包裹的部分
        const parts = child.value.split(/(\{\{.*?\}\})/g);

        return (
          <React.Fragment key={index}>
            {parts.map((part, i) => {
              if (part.match(/^\{\{.*?\}\}$/)) {
                // 提取 {{}} 中的内容
                const paramKey = part.match(/\{\{(.*?)\}\}/)?.[1];

                // 如果有 whatsAppParameters 并且包含该参数，则显示参数值
                if (
                  whatsAppParameters &&
                  paramKey &&
                  whatsAppParameters?.[type]?.[paramKey]
                ) {
                  return (
                    <span style={{ color: '#000' }}>
                      {whatsAppParameters?.[type]?.[paramKey]}
                    </span>
                  );
                }
                // 如果是 {{}} 包裹的内容，显示为蓝色
                return (
                  <span key={i} style={{ color: '#3463fc' }}>
                    {part}
                  </span>
                );
              }
              // 普通文本
              return <React.Fragment key={i}>{part}</React.Fragment>;
            })}
          </React.Fragment>
        );
      } else if (child.children) {
        // 递归处理子节点
        const childProps = { ...child.properties };
        return React.createElement(
          child.tagName,
          { key: index, ...childProps },
          this.processMarkdownText(child, edit, whatsAppParameters, type),
        );
      }
      return null;
    });
  };

  handleCloseReason = i => {
    let currentSessionDataNew = { ...this.state.currentSessionData };
    if (
      currentSessionDataNew.messageList &&
      currentSessionDataNew.messageList[i]
    ) {
      currentSessionDataNew.messageList[i].closeStatus = !currentSessionDataNew
        .messageList[i].closeStatus;
    }
    this.setState({ currentSessionData: currentSessionDataNew });
  };
  // 对敏感信息(邮箱和电话)进行脱敏处理
  maskSensitiveInfo = content => {
    if (!content) return content;
    let uuid = uuidv4();
    // 先处理邮箱脱敏
    let result = this.maskEmails(content);
    // 再处理电话号码脱敏
    let resultPhone = this.maskPhoneNumbers(result, uuid);
    if (resultPhone.includes(`__${uuid}__`)) {
      // 查找并替换所有匹配的模式 __uuid__masked__
      const pattern = new RegExp(`__${uuid}__([^_]+)__`, 'g');
      // 使用正则表达式匹配所有实例
      const matches = [...resultPhone.matchAll(pattern)];

      if (matches.length > 0) {
        // 构建带有Popover的JSX结构
        let parts = [];
        let lastIndex = 0;

        matches.forEach(match => {
          // 添加匹配前的文本
          if (match.index > lastIndex) {
            parts.push(resultPhone.substring(lastIndex, match.index));
          }

          // 添加带Popover的脱敏值
          const maskedValue = match[1]; // 获取脱敏后的值
          parts.push(
            <Popover
              content={
                <ul className="phonePopover">
                  <li
                    style={{
                      display:
                        this.props.user.agentAccessChannel?.search('3') != -1
                          ? 'inline-block'
                          : 'none',
                    }}
                    onClick={() =>
                      this.handleSelect(1, maskedValue.substring(1))
                    }
                  >
                    {/* <img src={Phone} /> */}
                    {CallPhone()}
                    <span>
                      <FormattedMessage
                        id="work.order.ticket.call"
                        defaultMessage="呼叫"
                      />
                    </span>
                  </li>
                  <li
                    onClick={() =>
                      this.handleSelect(2, maskedValue.substring(1))
                    }
                  >
                    {CustomerIcon()}
                    <span>
                      <FormattedMessage
                        id="work.order.add.to.customer.profile"
                        defaultMessage="添加到客户资料"
                      />
                    </span>
                  </li>
                </ul>
              }
              title={null}
              trigger="click"
              // open={this.state.openStates[parentIndex]}
              // onOpenChange={() => this.handleOpenChange(part,parentIndex)}
              overlayClassName="workTableChatPhone"
            >
              <span
                style={{
                  color: '#3463FC',
                  textDecoration: 'underline',
                  textDecorationColor: '#3463FC',
                  cursor: 'pointer',
                }}
              >
                {maskedValue}
              </span>
            </Popover>,
          );

          // 更新lastIndex为当前匹配结束位置
          lastIndex = match.index + match[0].length;
        });

        // 添加最后一个匹配之后的文本
        if (lastIndex < resultPhone.length) {
          parts.push(resultPhone.substring(lastIndex));
        }

        return <span>{parts}</span>;
      }
      return resultPhone;
    } else {
      return <span>{resultPhone}</span>;
    }
  };

  // 邮箱脱敏处理
  maskEmails = content => {
    if (!content) return content;

    // 邮箱正则表达式 - 匹配文本中的所有邮箱
    const emailRegex = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g;

    return content.replace(emailRegex, email => {
      return this.maskEmail(email);
    });
  };

  // 对单个邮箱进行脱敏
  maskEmail = email => {
    if (!email) return email;
    // 判断是否需要脱敏
    const shouldMaskPhone = this.props.user.workbenchPhoneHide === '1';
    if (!shouldMaskPhone) return email;

    const parts = email.split('@');
    if (parts.length !== 2) return email;

    const localPart = parts[0];
    const domainPart = parts[1];

    // 对本地部分进行脱敏处理
    let maskedLocalPart = '';
    if (localPart.length > 2) {
      maskedLocalPart =
        localPart[0] + '******' + localPart[localPart.length - 1];
    } else {
      maskedLocalPart = localPart[0] + '******';
    }

    // 对域名部分进行脱敏处理
    const domainParts = domainPart.split('.');
    const domainName = domainParts[0];
    const domainSuffix = domainParts.slice(1).join('.');

    let maskedDomainName = '***';
    if (domainName.length > 0) {
      maskedDomainName = '***' + domainName.substr(-3);
    }

    return maskedLocalPart + '@' + maskedDomainName + '.' + domainSuffix;
  };

  // 电话号码脱敏处理
  maskPhoneNumbers = (content, uuid) => {
    if (!content) return content;

    // 判断是否需要脱敏
    const shouldMaskPhone = this.props.user.workbenchPhoneHide === '1';
    if (!shouldMaskPhone) return content;

    // 国际电话号码正则表达式
    const phoneRegex = /(\+\d{1,4})?(\d{5,15})/g;

    return content.replace(
      phoneRegex,
      (match, countryCode = '', phoneNumber) => {
        // 验证是否为有效电话号码
        let validPhoneNumber = false;
        try {
          const fullNumber = (countryCode || '+') + phoneNumber;
          const phoneNumberInfo = parsePhoneNumberFromString(fullNumber);
          validPhoneNumber = phoneNumberInfo?.number;
        } catch (error) {
          validPhoneNumber = false;
        }
        if (
          validPhoneNumber &&
          phoneNumber.length >= 5 &&
          phoneNumber.length <= 15
        ) {
          const maskedNumber =
            match[0] + match[1] + '******' + match[match.length - 1];
          return `__${uuid}__${maskedNumber}__`;
        }
        return match;
      },
    );
  };

  // 保留原PhoneUnderline方法，但修改为使用新的脱敏方法
  // PhoneUnderline = (content, parentIndex) => {
  //   const parts = content?.split(/(\d+)/).filter(Boolean);
  //   const numbers = content?.replace(/\D/g, ''); // 电话号码

  //   return (
  //     <>
  //       {parts &&
  //         parts.length > 0 &&
  //         parts.map((part, index) => {
  //           const isNumber = /^\d{5,15}$/.test(part);
  //           let phoneNumber = '+' + part;
  //           let phoneNumberInfo = parsePhoneNumberFromString(phoneNumber);
  //           //判断是否为有效电话
  //           return isNumber &&
  //             phoneNumberInfo?.country &&
  //             phoneNumberInfo?.number ? (
  //             <Popover
  //               content={
  //                 <ul className="phonePopover">
  //                   <li
  //                     style={{
  //                       display:
  //                         this.props.user.agentAccessChannel?.search('3') != -1
  //                           ? 'inline-block'
  //                           : 'none',
  //                     }}
  //                     onClick={() => this.handleSelect(1, numbers)}
  //                   >
  //                     {/* <img src={Phone} /> */}
  //                     {CallPhone()}
  //                     <span>
  //                       <FormattedMessage
  //                         id="work.order.ticket.call"
  //                         defaultMessage="呼叫"
  //                       />
  //                     </span>
  //                   </li>
  //                   <li onClick={() => this.handleSelect(2, numbers)}>
  //                     {CustomerIcon()}
  //                     <span>
  //                       <FormattedMessage
  //                         id="work.order.add.to.customer.profile"
  //                         defaultMessage="添加到客户资料"
  //                       />
  //                     </span>
  //                   </li>
  //                 </ul>
  //               }
  //               title={null}
  //               trigger="click"
  //               open={this.state.openStates[parentIndex]}
  //               // onOpenChange={() => this.handleOpenChange(part,parentIndex)}
  //               overlayClassName="workTableChatPhone"
  //             >
  //               <span
  //                 key={index}
  //                 style={{
  //                   color: isNumber ? '#3463FC' : '',
  //                   textDecoration: isNumber ? 'underline' : '',
  //                   textDecorationColor: isNumber ? '#3463FC' : '',
  //                   cursor: isNumber ? 'pointer' : '',
  //                 }}
  //               >
  //                 {/* workbenchPhoneHide==='0'不脱敏 */}
  //                 {this.props.user.workbenchPhoneHide === '1'
  //                   ? part[0] + '******' + part[part.length - 1]
  //                   : part}
  //               </span>
  //             </Popover>
  //           ) : (
  //             <span key={index}>{part}</span>
  //           );
  //         })}
  //     </>
  //   );
  // };

  handleNext = cardList => {
    if (cardList?.length === this.state.carouseNum) {
      this.setState({ carouseNum: 1 });
    } else {
      this.setState({ carouseNum: this.state.carouseNum + 1 });
    }
    this.carouselRef.current.next(); // 调用 next() 方法
  };
  handlePrev = cardList => {
    if (this.state.carouseNum === 1) {
      this.setState({ carouseNum: cardList?.length });
    } else {
      this.setState({ carouseNum: this.state.carouseNum - 1 });
    }
    this.carouselRef.current.prev(); // 调用 next() 方法
  };
  handleViewByCondition = (item, index, tag, messageListData) => {
    // 如果是系统消息
    if (item.type === 'system' && item.role === 'system') {
      //(发送满意度)
      if (item.contentType === 'evaluation') {
        return (
          <div className={styles.messageItem_system_evaluation} key={index}>
            <p>
              {getIntl().formatMessage({
                id: 'im.chat.evaluation',
                defaultMessage: '客户满意度评价',
              })}
            </p>
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: 10,
              }}
            >
              {Array.from({ length: +item.content }, (_, i) => (
                <EvaluationIcon key={i} />
              ))}
              {Array.from({ length: 5 - +item.content }, (_, i) => (
                <EvaluationIconNo key={i} />
              ))}
            </div>
          </div>
        );
      } else {
        return (
          <div className={styles.messageItem_system} key={index}>
            <span>{item.content}</span>
          </div>
        );
      }
    }
    // 个人备注
    if (item.type === 'remark' && item.role === 'system') {
      return (
        <div
          className={styles.messageItem_right}
          style={{ marginBottom: tag === 'needTime' ? 20 : '' }}
          key={index}
        >
          <div>{AgentPhoto()}</div>
          <div className={styles.messageItem_right_wrapper}>
            <div className={styles.messageItem_right_wrapper_row1}>
              <span>{item.username}</span>
              {tag === 'needTime' ? (
                <span style={{ marginTop: 3 }}>{item.time}</span>
              ) : (
                ''
              )}
            </div>

            {item.contentType === 'text' ||
            item.contentType === 'text/pain' ||
            item.contentType === 'markdown' ? (
              <div className={styles.messageItem_right_wrapper_row2}>
                <div
                  className={`${styles.messageItem_right_wrapper_row2_body} ${
                    item.type === 'remark' && item.role === 'system'
                      ? styles.remark
                      : ''
                  }`}
                >
                  {/* <div>{item.content}</div> */}
                  <ReactMarkdown>{item.content}</ReactMarkdown>
                </div>
                {/* <div className={styles.messageItem_right_wrapper_row3}>
                  <span>{item.status === 0 ? Unread() : Read()}</span>
                </div> */}
              </div>
            ) : item.uploadStatus == 1 ? (
              item.contentType === 'link' ? (
                <div className={styles.messageItem_right_wrapper_row2}>
                  <div className={styles.messageItem_right_wrapper_row2_body}>
                    <a href={item.filePath || item.content}>
                      {item.fileName || item.content}
                    </a>
                  </div>
                </div>
              ) : item.contentType === 'image' ? (
                <div className={styles.messageItem_right_wrapper_row4}>
                  <div
                    className={styles.messageItem_right_wrapper_row4_imagebox}
                  >
                    <Image
                      src={item.filePath || item.content}
                      alt={item.fileName}
                    />
                  </div>
                </div>
              ) : item.contentType === 'video' ? (
                <div className={styles.messageItem_right_wrapper_row5}>
                  <video src={item.filePath || item.content} controls />
                </div>
              ) : item.contentType === 'audio' ? (
                <div style={{ display: 'inline-block' }}>
                  <AudioPlayer src={item.filePath || item.content} id={index} />
                </div>
              ) : (
                ''
              )
            ) : (
              <div className={styles.messageItem_right_wrapper_upload}>
                <div className={styles.messageItem_right_wrapper_upload_left}>
                  <div>{item.fileName}</div>
                  <div>
                    {getIntl().formatMessage({
                      id: 'im.chat.uploading',
                      defaultMessage: 'Uploading',
                    })}{' '}
                    {item.uploadProgress}%
                  </div>
                </div>
                <div className={styles.messageItem_right_wrapper_upload_right}>
                  <Progress
                    width={45}
                    type="circle"
                    trailColor="#e6f4ff"
                    strokeColor="#ad30e5"
                    percent={item.uploadProgress}
                    strokeWidth={10}
                    circleTextFontSize="15"
                    format={percent => <span></span>}
                  />
                  {/*<span style={{position: 'absolute',top: '24px', left: '16px', display: 'flex',justifyContent: 'center',alignItems: 'center'}}>{percent}%</span>*/}
                </div>
              </div>
            )}
          </div>
        </div>
      );
    }
    /* 会话结束时推送用户心情 */
    if (item.type === 'aisummary' && item.role === 'system') {
      // 过滤出 type 为 'aisummary' 的数据
      const aiSummaryData = messageListData.filter(
        item => item.type === 'aisummary',
      );

      if (aiSummaryData.length === 0) return messageListData;

      // 找到最新的一条 `aisummary`（按时间排序）
      const latestAiSummary = aiSummaryData.reduce((latest, item) =>
        new Date(item.time) > new Date(latest.time) ? item : latest,
      );
      let newContent = item.content ? JSON.parse(item.content) : '';
      return (
        <div className={styles.userMoodWrapper}>
          <div className={styles.histortMsg}>
            {HistoryLineLeft()}
            <span>
              {getIntl().formatMessage({
                id: 'AIGC.chatBox.history',
                defaultMessage: '以上都是历史消息',
              })}
            </span>
            {HistoryLineRight()}
          </div>
          {item.summarizeStatus ? (
            <div className={styles.intelligentSummaryContent}>
              <span className={styles.labelText}>
                <FormattedMessage
                  id="work.order.detail.intelligent.summary.content"
                  defaultMessage="内容总结"
                />
              </span>
              <TextArea
                autoSize={{
                  minRows: 6,
                  maxRows: 6,
                }}
                value={this.state.contentSummary}
                onChange={this.handleChangeTextAreaValue}
              />
              <div className={styles.moodContentNew}>
                <span className={styles.labelText}>
                  <FormattedMessage
                    id="work.order.detail.customer.mood"
                    defaultMessage="客户心情"
                  />
                </span>
                <img
                  onClick={() => this.handleChangeMood(1)}
                  className={styles.moodIcon}
                  src={
                    this.state.customerMood == 1
                      ? MoodGoodActiveIcon
                      : MoodGoodIcon
                  }
                />
                <img
                  onClick={() => this.handleChangeMood(2)}
                  className={styles.moodIcon}
                  src={
                    this.state.customerMood == 2
                      ? MoodNormalActiveIcon
                      : MoodNormalIcon
                  }
                />
                <img
                  onClick={() => this.handleChangeMood(3)}
                  className={styles.moodIcon}
                  src={
                    this.state.customerMood == 3
                      ? MoodBadActiveIcon
                      : MoodBadIcon
                  }
                />
              </div>
              <div className={styles.toDoTitle}>
                <span
                  className={styles.labelText}
                  style={{ lineHeight: '32px' }}
                >
                  <FormattedMessage
                    id="work.order.detail.to.do"
                    defaultMessage="代办事项"
                  />
                </span>
                <Button
                  onClick={this.handleShowAddTodoList}
                  className={styles.addBtn}
                  icon={<PlusOutlined />}
                >
                  <FormattedMessage
                    id="work.order.detail.add.to.do"
                    defaultMessage=" 添加"
                  />
                </Button>
              </div>
              <div className={styles.toDoContent}>
                <Input
                  onChange={this.changeTodoInput}
                  value={this.state.waitExecuteEvent}
                  onPressEnter={this.handleAddTodoList}
                  className={styles.toDoInput}
                  placeholder="输入待办事项回车保存"
                  style={{
                    display: this.state.addTodoStatus ? 'block' : 'none',
                  }}
                />
                <Checkbox.Group
                  onChange={this.resolvedTodoList}
                  value={this.state.selectWaitExecuteList}
                >
                  {this.state.waitExecuteList?.map(item => {
                    return (
                      <Row>
                        <Col span={24}>
                          <Checkbox
                            key={item.waitExecuteId}
                            value={item.waitExecuteId}
                          >
                            {item.waitExecuteEvent}
                          </Checkbox>
                          <div
                            onClick={() =>
                              this.handleDeleteTodoList(item.waitExecuteId)
                            }
                            className="deleteTodoIcon"
                          ></div>
                        </Col>
                      </Row>
                    );
                  })}
                </Checkbox.Group>
              </div>
              <div className={styles.footerBtn}>
                <Button
                  onClick={() => this.handleContinuingFollow(item)}
                  style={{ background: 'none' }}
                >
                  <FormattedMessage
                    id="awsAccountSetting.cancel.btn"
                    defaultMessage="取消"
                  />
                </Button>
                <Button
                  onClick={() => this.handleEditorSave(item)}
                  type={'primary'}
                >
                  <FormattedMessage
                    id="work.order.saved.btn"
                    defaultMessage="保存"
                  />
                </Button>
              </div>
            </div>
          ) : (
            <div className={styles.userMoodContent}>
              <p className={styles.contentSum}>
                <FormattedMessage
                  id="work.order.detail.intelligent.summary.content"
                  defaultMessage="内容总结"
                />
                {item.time === latestAiSummary.time && (
                  <div
                    className={styles.editorContainer}
                    onClick={() =>
                      this.handleClickEditor(messageListData, item)
                    }
                  >
                    <img src={EditorIcon} />
                    <span>
                      <FormattedMessage
                        id="emailMarketing.table.op.1"
                        defaultMessage="修改"
                      />
                    </span>
                  </div>
                )}
              </p>
              <p className={styles.content}>{newContent.summary}</p>
              <div className={styles.userMood}>
                <FormattedMessage
                  id="work.order.detail.customer.mood"
                  defaultMessage="客户心情"
                />
                <div className={styles.moodImg}>
                  <img
                    className={styles.moodIcon}
                    src={
                      +newContent.mood === 1 ? MoodGoodActiveIcon : MoodGoodIcon
                    }
                  />
                  <img
                    className={styles.moodIcon}
                    src={
                      +newContent.mood === 2
                        ? MoodNormalActiveIcon
                        : MoodNormalIcon
                    }
                  />
                  <img
                    className={styles.moodIcon}
                    src={
                      +newContent.mood === 3 ? MoodBadActiveIcon : MoodBadIcon
                    }
                  />
                  {/* {+item.content.mood === 1
                  ? OneMoodIcon()
                  : +item.content.mood === 2
                  ? TwoMoodIcon()
                  : +item.content.mood === 3
                  ? ThreeMoodIcon()
                  : ''} */}
                </div>
              </div>
              <div className={styles.readyTodo}>
                <span>
                  <FormattedMessage
                    id="work.order.detail.to.do"
                    defaultMessage="代办事项"
                  />
                </span>

                <div className={styles.todoItem}>
                  {newContent &&
                    newContent.toDoList &&
                    newContent.toDoList.length > 0 &&
                    newContent.toDoList.map((items, index) => {
                      return (
                        <div
                          className={styles.itemContent}
                          key={items.waitExecuteId}
                        >
                          <span>{CircleIcon()}</span>
                          <span
                            className={styles.itemText}
                            style={{
                              color:
                                +items.waitExecuteStatus === 1
                                  ? '#999'
                                  : '#333',
                            }}
                          >
                            {items.waitExecuteEvent}
                          </span>
                        </div>
                      );
                    })}
                </div>
              </div>
            </div>
          )}
        </div>
      );
    }
    /* 进线意图+深度思考 */
    if (item.type === 'aiAgent' && item.role === 'system') {
      let newContent = JSON.parse(item.content);
      let inboundIntent = newContent.inbound_intent;
      let deepThought = newContent.deep_thought;

      return (
        <div className={styles.entryIntentionContainer}>
          <div className={styles.topReasonContainer}>
            <b>
              <FormattedMessage
                id="work.order.detail.customer.entry.intention.reason"
                defaultMessage="客户进线原因："
              />
            </b>
            <span>{inboundIntent}</span>
          </div>
          <div className={styles.space}></div>
          <div className={styles.bottomReasonContainer}>
            <div
              className={styles.bottomHeader}
              onClick={() => this.handleCloseReason(index)}
            >
              <img src={AiIntentionIcon} />
              <span style={{ color: item.closeStatus ? '#999' : '#333' }}>
                <FormattedMessage
                  id="work.order.detail.deeply.pondered"
                  defaultMessage="已深度思考"
                />
              </span>
              <img
                className={styles.arrowIcon}
                src={item.closeStatus ? NewDownIcon : NewUpIcon}
              />
            </div>
            <div
              className={styles.detailReasonText}
              style={{ display: item.closeStatus ? 'none' : 'block' }}
            >
              <ReactMarkdown>{deepThought}</ReactMarkdown>
            </div>
          </div>
        </div>
      );
    }
    // 智能填单
    if (item.type === 'intelligentFormFilling' && item.role === 'system') {
      // 过滤出 type 为 'aisummary' 的数据
      const intelligentFormFillingData = messageListData.filter(
        item => item.type === 'intelligentFormFilling',
      );

      if (intelligentFormFillingData.length === 0) return messageListData;

      // 找到最新的一条 `aisummary`（按时间排序）
      const latestIntelligentFormFilling = intelligentFormFillingData?.reduce(
        (latest, item) =>
          new Date(item.time) > new Date(latest.time) ? item : latest,
      );
      let newContent = item.content ? JSON.parse(item.content) : '';
      const formValues = {
        aigcTicketSmartFillList: newContent.aigcTicketSmartFillList?.map(
          items => ({
            [items.attrName]: items.attrValue,
          }),
        ),
      };
      setTimeout(() => {
        this.smartFillRef1.current?.setFieldsValue(formValues);
      }, 0);

      return (
        <div className={styles.intelligentFormFillingContainer}>
          <div className={styles.titleContainer}>
            <FormattedMessage
              id="intelligent.form.filling.title"
              defaultMessage="智能填单"
            />
            {item.time === latestIntelligentFormFilling.time &&
              !item.ticketSmartFillListStatus && (
                <div
                  className={styles.editorContainer}
                  onClick={() =>
                    this.handleClickEditorIntelligentFormFilling(
                      messageListData,
                      item,
                    )
                  }
                >
                  <img src={EditorIcon} />
                  <span>
                    <FormattedMessage
                      id="emailMarketing.table.op.1"
                      defaultMessage="修改"
                    />
                  </span>
                </div>
              )}
          </div>
          {!item?.ticketSmartFillListStatus ? (
            <div>
              {newContent.aigcTicketSmartFillList?.map(items => {
                return (
                  <div className={styles.detailItem}>
                    {items.attrName} :{' '}
                    {items.attrValue ? items.attrValue : '--'}
                  </div>
                );
              })}
            </div>
          ) : (
            <Form
              name="basic"
              // onFinish={this.onFinish1}
              autoComplete="off"
              ref={this.smartFillRef1}
            >
              {newContent?.aigcTicketSmartFillList?.map(
                (itemData, itemIndex) => {
                  return (
                    <Form.Item
                      key={itemIndex}
                      label={itemData.attrName}
                      name={[
                        'aigcTicketSmartFillList',
                        itemIndex,
                        itemData.attrName,
                      ]}
                      rules={[
                        {
                          required: false,
                          message: getIntl().formatMessage({
                            id: 'work.order.management.tips',
                            defaultValue: '请输入',
                          }),
                        },
                        {
                          max: 80,
                          message: (
                            <FormattedMessage
                              id="customerInformation.add.basicInformation.maxlength"
                              defaultValue="长度不能超过80个字符"
                            />
                          ),
                        },
                      ]}
                    >
                      <Input
                        maxLength={81}
                        placeholder={getIntl().formatMessage({
                          id: 'work.order.management.tips',
                          defaultValue: '请输入',
                        })}
                      />
                    </Form.Item>
                  );
                },
              )}
              <Form.Item style={{ textAlign: 'center' }}>
                <Button
                  onClick={() =>
                    this.handleCancelFormFill(messageListData, item)
                  }
                  style={{ marginRight: '10px' }}
                >
                  <FormattedMessage
                    id="awsAccountSetting.cancel.btn"
                    defaultMessage="取消"
                  />
                </Button>
                <Button
                  type={'primary'}
                  // htmlType="submit"
                  onClick={() => this.onFinish1(item)}
                  loading={this.state.loadingSave}
                >
                  <FormattedMessage
                    id="work.order.saved.btn"
                    defaultMessage="保存"
                  />
                </Button>
              </Form.Item>
            </Form>
          )}
        </div>
      );
    }
    // 三方会话时，发消息者是该账号
    if (
      item.type === 'role' &&
      (item.role === 'agent' || item.role === 'agent_admin')
    ) {
      return item.dataStatus === '999' ? (
        <div className={styles.messageItem_right_wrapper_row2_withdraw}>
          <span style={{ color: '#999' }}>
            {getIntl().formatMessage({
              id: 'workerOffers.withdraw.message.tips',
              defaultMessage: '您已经撤回一条消息',
            })}
          </span>
          &nbsp; &nbsp;
          {item.contentType === 'text' ||
          item.contentType === 'text/pain' ||
          item.contentType === 'markdown' ? (
            <span
              style={{ color: '#3463fc', cursor: 'pointer' }}
              onClick={() => this.handleCopyMessage(item.content)}
            >
              {getIntl().formatMessage({
                id: 'workerOffers.withdraw.message.edit',
                defaultMessage: '重新编辑',
              })}
            </span>
          ) : (
            ''
          )}
        </div>
      ) : (
        <div className={styles.messageItem_right} key={index}>
          <div>
            {(item.role === 'agent' &&
              this.props.user.roleList?.[0]?.roleId === '1005') ||
            (item.role === 'agent_admin' &&
              this.props.user.roleList?.[0]?.roleId === '1003') ? (
              <div
                className={styles.headAva}
                style={{ backgroundColor: '#3463fc' }}
              >
                <span>{item?.username?.substr(0, 1)}</span>
              </div>
            ) : (
              <img
                style={{ width: '24px', height: '24px', borderRadius: 20 }}
                // src={this.props.userAvatar}
                src={
                  this.state.userProfilePicture
                    ? this.state.userProfilePicture
                    : this.props.userAvatar
                }
              />
            )}
          </div>
          <div className={styles.messageItem_right_wrapper}>
            <div className={styles.messageItem_right_wrapper_row1}>
              <span>
                {(item.role === 'agent' &&
                  this.props.user.roleList?.[0]?.roleId === '1005') ||
                (item.role === 'agent_admin' &&
                  this.props.user.roleList?.[0]?.roleId === '1003')
                  ? item.username
                  : this.state.nickName}
              </span>
              <span>{item.time}</span>
            </div>

            {item.contentType === 'whatsAppTemplate' ? (
              <div className={styles.messageItem_right_wrapper_row2}>
                <div
                  className={`${styles.messageItem_right_wrapper_row2_body} ${
                    item.isRemark ? styles.remark : ''
                  }`}
                >
                  <WhatsAppMessageCard
                    whatsAppMessageItem={
                      JSON.parse(item.content)?.whatsAppMessageItem
                    }
                    whatsAppParameters={
                      JSON.parse(item.content)?.whatsAppParameters
                    }
                    processMarkdownText={this.processMarkdownText}
                  ></WhatsAppMessageCard>
                </div>
                <div className={styles.messageItem_right_wrapper_row3}>
                  {/* 工单--》未分配：全部改成未读 */}
                  {+this.props.workTableTabProcesOrPendValue === 2 ? (
                    <span>{Unread()}</span>
                  ) : (
                    <span>{item.status === 0 ? Unread() : Read()}</span>
                  )}
                </div>
              </div>
            ) : item.contentType === 'text' ||
              item.contentType === 'text/pain' ||
              item.contentType === 'markdown' ? (
              <div className={styles.messageItem_right_wrapper_row2}>
                <div
                  className={`${styles.messageItem_right_wrapper_row2_body} ${
                    item.isRemark ? styles.remark : ''
                  }`}
                  onClick={e =>
                    this.handleFunctionClick(
                      e,
                      (item.role === 'agent' &&
                        this.props.user.roleList?.[0]?.roleId === '1005') ||
                        (item.role === 'agent_admin' &&
                          this.props.user.roleList?.[0]?.roleId === '1003')
                        ? 'customer'
                        : 'agent',
                      item,
                    )
                  }
                >
                  {/* <div>{item.content}</div> */}
                  <ReactMarkdown>{item.content}</ReactMarkdown>
                  {item.translationStatus !== '' &&
                  item.translationStatus !== 0 ? (
                    <div className={styles.translationStatusBody}>
                      {item.translationStatus === 1 ? <Spin /> : ''}
                      {item.translationStatus === 2 ? (
                        <ReactMarkdown>
                          {item.translationContent || item.translate_content}
                        </ReactMarkdown>
                      ) : (
                        ''
                      )}
                      {item.translationStatus === 3 ? (
                        <a
                          onClick={() => this.handleClickRetryTranslation(item)}
                        >
                          {getIntl().formatMessage({
                            id: 'im.chat.translation.retry',
                            defaultMessage: 'Retry',
                          })}
                        </a>
                      ) : (
                        ''
                      )}
                      {item.translationStatus === 4 ? (
                        <span>
                          {getIntl().formatMessage({
                            id: 'piecemealWhatsApp.translation.retry',
                          })}
                        </span>
                      ) : (
                        ''
                      )}
                    </div>
                  ) : (
                    ''
                  )}
                </div>
                <div className={styles.messageItem_right_wrapper_row3}>
                  {/* 工单--》未分配：全部改成未读 */}
                  {+this.props.workTableTabProcesOrPendValue === 2 ? (
                    <span>{Unread()}</span>
                  ) : (
                    <span>
                      {item.status === 0 ? (
                        Unread()
                      ) : item.status === 1 ? (
                        Read()
                      ) : item.status === 2 ? (
                        <Spin indicator={<LoadingOutlined spin />} />
                      ) : item.status === 3 ? (
                        SendFail()
                      ) : (
                        ''
                      )}
                    </span>
                  )}
                </div>
              </div>
            ) : item.uploadStatus == 1 ? (
              item.contentType === 'link' ? (
                <div
                  className={styles.messageItem_right_wrapper_row2}
                  onClick={e =>
                    this.handleFunctionClick(
                      e,
                      (item.role === 'agent' &&
                        this.props.user.roleList?.[0]?.roleId === '1005') ||
                        (item.role === 'agent_admin' &&
                          this.props.user.roleList?.[0]?.roleId === '1003')
                        ? 'customer-noCopy'
                        : 'agent-noCopy',
                      item,
                    )
                  }
                >
                  <div className={styles.messageItem_right_wrapper_row2_body}>
                    <a href={item.filePath || item.content}>
                      {decodeURIComponent(item.fileName) || item.content}
                    </a>
                  </div>
                </div>
              ) : item.contentType === 'image' ? (
                <div
                  className={styles.messageItem_right_wrapper_row4}
                  onClick={e =>
                    this.handleFunctionClick(
                      e,
                      (item.role === 'agent' &&
                        this.props.user.roleList?.[0]?.roleId === '1005') ||
                        (item.role === 'agent_admin' &&
                          this.props.user.roleList?.[0]?.roleId === '1003')
                        ? 'customer-noCopy'
                        : 'agent-noCopy',
                      item,
                    )
                  }
                >
                  <div
                    className={styles.messageItem_right_wrapper_row4_imagebox}
                  >
                    <Image
                      src={item.filePath}
                      alt={item.fileName}
                      style={{ width: '250px', height: 'auto' }}
                      preview={{
                        visible:
                          this.state.imagePreviewVisible &&
                          this.state.imagePreviewId === item.id,
                        onVisibleChange: visible => {
                          if (!visible) {
                            this.handleImagePreviewClose();
                          }
                        },
                      }}
                    />
                  </div>
                </div>
              ) : item.contentType === 'video' ? (
                <div
                  className={styles.messageItem_right_wrapper_row5}
                  onClick={e =>
                    this.handleFunctionClick(
                      e,
                      (item.role === 'agent' &&
                        this.props.user.roleList?.[0]?.roleId === '1005') ||
                        (item.role === 'agent_admin' &&
                          this.props.user.roleList?.[0]?.roleId === '1003')
                        ? 'customer-noCopy'
                        : 'agent-noCopy',
                      item,
                    )
                  }
                >
                  <video src={item.filePath} controls />
                </div>
              ) : item.contentType === 'audio' ? (
                <div
                  style={{ display: 'inline-block' }}
                  onClick={e =>
                    this.handleFunctionClick(
                      e,
                      (item.role === 'agent' &&
                        this.props.user.roleList?.[0]?.roleId === '1005') ||
                        (item.role === 'agent_admin' &&
                          this.props.user.roleList?.[0]?.roleId === '1003')
                        ? 'customer-noCopy'
                        : 'agent-noCopy',
                      item,
                    )
                  }
                >
                  <AudioPlayer src={item.filePath} id={index} />
                </div>
              ) : (
                ''
              )
            ) : (
              <div className={styles.messageItem_right_wrapper_upload}>
                <div className={styles.messageItem_right_wrapper_upload_left}>
                  <div>{item.fileName}</div>
                  <div>
                    {getIntl().formatMessage({
                      id: 'im.chat.uploading',
                      defaultMessage: 'Uploading',
                    })}
                    {item.uploadProgress}%
                  </div>
                </div>
                <div className={styles.messageItem_right_wrapper_upload_right}>
                  <Progress
                    width={45}
                    type="circle"
                    trailColor="#e6f4ff"
                    strokeColor="#ad30e5"
                    percent={item.uploadProgress}
                    strokeWidth={10}
                    circleTextFontSize="15"
                    format={percent => <span></span>}
                  />
                  {/*<span style={{position: 'absolute',top: '24px', left: '16px', display: 'flex',justifyContent: 'center',alignItems: 'center'}}>{percent}%</span>*/}
                </div>
              </div>
            )}
            {item.referenceContentId && (
              <div className={styles.messageItem_right_reference}>
                <div
                  style={{
                    display: 'flex',
                    width: '100%',
                    maxHeight: 300,
                  }}
                >
                  <span>{item.referenceReplyPerson}</span>
                  {item.referenceReplyPerson && `:`}&nbsp;
                  {['2', 'image'].includes(
                    String(item.referenceContentType),
                  ) ? (
                    <Image
                      src={item.referenceContentFile || item.referenceContent}
                      style={{
                        width: 50,
                        height: 'auto',
                      }}
                    />
                  ) : ['3', 'video'].includes(
                      String(item.referenceContentType),
                    ) ? (
                    <video
                      src={item.referenceContentFile || item.referenceContent}
                      style={{
                        width: 200,
                        height: 'auto',
                      }}
                      controls
                    />
                  ) : ['5', 'music', 'audio'].includes(
                      String(item.referenceContentType),
                    ) ? (
                    <AudioPlayer
                      src={item.referenceContentFile || item.referenceContent}
                      id={item.referenceContentId}
                      height="auto"
                    />
                  ) : ['4', 'link'].includes(
                      String(item.referenceContentType),
                    ) ? (
                    <a
                      href={item.referenceContentFile || item.referenceContent}
                      style={{
                        overflowWrap: 'break-word',
                        wordBreak: 'break-all',
                      }}
                    >
                      {item.referenceContent}
                    </a>
                  ) : (
                    <span
                      style={{
                        overflow: 'scroll',
                        overflowWrap: 'break-word',
                        wordBreak: 'break-all',
                      }}
                    >
                      {item.referenceContent}
                    </span>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      );
    }

    // 如果是机器人消息
    if (item.type === 'robot' && item.role === 'system') {
      return (
        <div className={styles.messageItem_right} key={index}>
          <div>{AgentPhoto()}</div>
          <div className={styles.messageItem_right_wrapper}>
            <div className={styles.messageItem_right_wrapper_row1}>
              <span>{item.username}</span>
              <span>{item.time}</span>
            </div>

            {item.contentType === 'text' ||
            item.contentType === 'text/pain' ||
            item.contentType === 'markdown' ? (
              <div className={styles.messageItem_right_wrapper_row2}>
                <div
                  className={`${styles.messageItem_right_wrapper_row2_body} ${
                    item.isRemark ? styles.remark : ''
                  }`}
                  onClick={e => this.handleFunctionClick(e, 'customer', item)}
                >
                  {/* <div>{item.content}</div> */}
                  <ReactMarkdown>{item.content}</ReactMarkdown>

                  {item.translationStatus !== '' &&
                  item.translationStatus !== 0 ? (
                    <div>
                      {item.translationStatus === 1 ? <Spin /> : ''}
                      {item.translationStatus === 2 ? (
                        <span>
                          {item.translationContent || item.translate_content}
                        </span>
                      ) : (
                        ''
                      )}
                      {item.translationStatus === 3 ? (
                        <a
                          onClick={() => this.handleClickRetryTranslation(item)}
                        >
                          {getIntl().formatMessage({
                            id: 'im.chat.translation.retry',
                            defaultMessage: 'Retry',
                          })}
                        </a>
                      ) : (
                        ''
                      )}
                      {item.translationStatus === 4 ? (
                        <span>
                          {getIntl().formatMessage({
                            id: 'piecemealWhatsApp.translation.retry',
                          })}
                        </span>
                      ) : (
                        ''
                      )}
                    </div>
                  ) : (
                    ''
                  )}
                </div>
                <div className={styles.messageItem_right_wrapper_row3}>
                  {+this.props.workTableTabProcesOrPendValue === 2 ? (
                    <span>{Unread()}</span>
                  ) : (
                    <span>{item.status === 0 ? Unread() : Read()}</span>
                  )}
                </div>
              </div>
            ) : item.uploadStatus == 1 ? (
              item.contentType === 'link' ? (
                <div
                  className={styles.messageItem_right_wrapper_row2}
                  onClick={e =>
                    this.handleFunctionClick(e, 'customer-noCopy', item)
                  }
                >
                  <div className={styles.messageItem_right_wrapper_row2_body}>
                    <a href={item.filePath || item.content}>
                      {decodeURIComponent(item.fileName) || item.content}
                    </a>
                  </div>
                </div>
              ) : item.contentType === 'image' ? (
                <div
                  className={styles.messageItem_right_wrapper_row4}
                  onClick={e =>
                    this.handleFunctionClick(e, 'customer-noCopy', item)
                  }
                >
                  <div
                    className={styles.messageItem_right_wrapper_row4_imagebox}
                  >
                    <Image src={item.content} alt={item.fileName} />
                  </div>
                </div>
              ) : item.contentType === 'video' ? (
                <div
                  className={styles.messageItem_right_wrapper_row5}
                  onClick={e =>
                    this.handleFunctionClick(e, 'customer-noCopy', item)
                  }
                >
                  <video src={item.content} controls />
                </div>
              ) : item.contentType === 'audio' ? (
                <div
                  style={{ display: 'inline-block' }}
                  onClick={e =>
                    this.handleFunctionClick(e, 'customer-noCopy', item)
                  }
                >
                  <AudioPlayer src={item.content} id={index} />
                </div>
              ) : item.contentType === 'card' ? (
                (() => {
                  let newContent = JSON.parse(item.content);
                  // cardLayout  1 轮播，2 列表
                  if (newContent?.cardLayout === 1) {
                    return (
                      <div className={styles.agentItem}>
                        <div className={styles.agentContent}>
                          <div className={styles.dynamicCard}>
                            <p className={styles.botMessage}>
                              {newContent?.botMessage
                                ? newContent?.botMessage
                                : null}
                            </p>
                            <Carousel
                              ref={this.carouselRef}
                              effect="fade"
                              dots={false}
                            >
                              {newContent?.cardList?.map(itemData => {
                                return (
                                  <div className={styles.carouselItem}>
                                    <img src={itemData.cardImageUrl} />
                                    <p
                                      title={itemData?.cardTitle}
                                      className={styles.topTitleText}
                                    >
                                      {itemData?.cardTitle
                                        ? itemData?.cardTitle
                                        : '--'}
                                    </p>
                                    <div className={styles.bottomContainer}>
                                      <p
                                        className={styles.priceContainer}
                                        title={itemData?.cardPrice}
                                      >
                                        {/*<span className={styles.unit}>￥</span>*/}
                                        {itemData?.cardPrice && (
                                          <span className={styles.num}>
                                            {itemData?.cardPrice
                                              ? itemData?.cardPrice
                                              : '0.00'}
                                          </span>
                                        )}
                                      </p>
                                      {itemData?.cardNumber && (
                                        <p className={styles.numText}>
                                          x{' '}
                                          {itemData?.cardNumber
                                            ? itemData?.cardNumber
                                            : 0}
                                        </p>
                                      )}
                                      {itemData?.cardStatus && (
                                        <p className={styles.logisticsStatus}>
                                          {itemData?.cardStatus
                                            ? itemData?.cardStatus
                                            : '--'}
                                        </p>
                                      )}
                                    </div>
                                  </div>
                                );
                              })}
                            </Carousel>
                            <div className={styles.operationContainer}>
                              <p>
                                <span>{this.state.carouseNum}</span>
                                {''}/{''}
                                {newContent?.cardList?.length}
                              </p>
                              <span
                                onClick={() =>
                                  this.handleNext(newContent?.cardList)
                                }
                                className={styles.arrowCircleIcon}
                              >
                                <RightOutlined />
                              </span>
                              <span
                                onClick={() =>
                                  this.handlePrev(newContent?.cardList)
                                }
                                className={styles.arrowCircleIcon}
                              >
                                <LeftOutlined />
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  } else {
                    return (
                      <div className={styles.agentItem}>
                        <div className={styles.agentContent}>
                          <div className={styles.dynamicCard}>
                            <p className={styles.botMessage}>
                              {newContent?.botMessage
                                ? newContent?.botMessage
                                : null}
                            </p>
                            {newContent?.cardList?.map(itemData => {
                              return (
                                <Card>
                                  <div className={styles.leftContainer}>
                                    <img src={itemData.cardImageUrl} />
                                  </div>
                                  <div className={styles.rightContainer}>
                                    <div className={styles.topContainer}>
                                      <p
                                        title={itemData?.cardTitle}
                                        className={styles.topTitleText}
                                      >
                                        {itemData?.cardTitle
                                          ? itemData?.cardTitle
                                          : '--'}
                                      </p>
                                      <p className={styles.priceContent}>
                                        {/*<span className={styles.priceUnit}>￥</span>*/}
                                        {itemData?.cardPrice && (
                                          <span
                                            title={itemData?.cardPrice}
                                            className={styles.priceText}
                                          >
                                            {itemData?.cardPrice
                                              ? itemData?.cardPrice
                                              : '0.00'}
                                          </span>
                                        )}
                                      </p>
                                      {itemData?.cardNumber && (
                                        <p className={styles.numText}>
                                          x{' '}
                                          {itemData?.cardNumber
                                            ? itemData?.cardNumber
                                            : 0}
                                        </p>
                                      )}
                                    </div>
                                    {itemData?.cardStatus && (
                                      <p className={styles.logisticsStatus}>
                                        {itemData?.cardStatus
                                          ? itemData.cardStatus
                                          : '--'}
                                      </p>
                                    )}
                                  </div>
                                </Card>
                              );
                            })}
                          </div>
                        </div>
                      </div>
                    );
                  }
                })()
              ) : item.contentType === 'hotIssues' ? (
                (() => {
                  let newContent = JSON.parse(item.content);
                  // configType 1 手动；2 自动
                  // showType 1 横；2 竖
                  if (newContent.configType === 1) {
                    if (newContent.showType === 1) {
                      const tabItems = newContent.issues.map((issue, index) => {
                        // 为每个 issueContents 字符串创建一个段落或其他元素
                        const childrenContent = (
                          <div>
                            {issue.issueContents.map(
                              (content, contentIndex) => (
                                <p
                                  title={content}
                                  key={contentIndex}
                                  className={styles.panelText}
                                >
                                  {' '}
                                  {/* 使用 styles.panelText 类名如果需要的话 */}
                                  {/* 您可以根据需要添加编号，例如：`${contentIndex + 1}. ${content}` */}
                                  {`${contentIndex + 1}. ${content}`}
                                </p>
                              ),
                            )}
                          </div>
                        );

                        return {
                          label: issue.issueName,
                          key: String(index + 1), // 使用索引加1作为 key，确保唯一性
                          children: childrenContent,
                        };
                      });

                      return (
                        <div className={styles.agentItem}>
                          <div className={styles.agentContent}>
                            <div className={styles.hotspotIssuesContainer}>
                              <div className={styles.headerTitle}>
                                <span className={styles.titleIcon}>
                                  {HotspotIssuesIcon()}
                                </span>
                                <span>
                                  <FormattedMessage
                                    id="work.order.detail.content.type.hot.spot.issues"
                                    defaultMessage="热点问题"
                                  />
                                </span>
                              </div>
                              <Tabs defaultActiveKey="1" items={tabItems} />
                            </div>
                          </div>
                        </div>
                      );
                    } else {
                      let issuesData = newContent.issues;
                      return (
                        <div className={styles.agentItem}>
                          <div className={styles.agentContent}>
                            <div className={styles.hotspotIssuesContainer}>
                              <div className={styles.headerTitle}>
                                <span className={styles.titleIcon}>
                                  {HotspotIssuesIcon()}
                                </span>
                                <span>
                                  <FormattedMessage
                                    id="work.order.detail.content.type.hot.spot.issues"
                                    defaultMessage="热点问题"
                                  />
                                </span>
                              </div>
                              <Collapse
                                accordion
                                expandIconPosition={'end'}
                                ghost={true}
                              >
                                {issuesData?.map((itemData, index) => {
                                  return (
                                    <Panel
                                      header={itemData?.issueName}
                                      key={index + 1}
                                    >
                                      {itemData?.issueContents?.map(
                                        (detailData, detailIndex) => {
                                          return (
                                            <p
                                              title={detailData}
                                              className={styles.panelText}
                                            >
                                              {detailIndex + 1}. {detailData}
                                            </p>
                                          );
                                        },
                                      )}
                                    </Panel>
                                  );
                                })}
                              </Collapse>
                            </div>
                          </div>
                        </div>
                      );
                    }
                  } else {
                    return (
                      <div className={styles.agentItem}>
                        <div className={styles.agentContent}>
                          <div className={styles.hotspotIssuesContainer}>
                            <div className={styles.headerTitle}>
                              <span className={styles.titleIcon}>
                                {HotspotIssuesIcon()}
                              </span>
                              <span>
                                <FormattedMessage
                                  id="work.order.detail.content.type.hot.spot.issues"
                                  defaultMessage="热点问题"
                                />
                              </span>
                            </div>
                            {newContent?.issues[0]?.issueContents?.map(
                              (itemData, itemIndex) => {
                                return (
                                  <p
                                    title={itemData}
                                    className={styles.panelText1}
                                  >
                                    {itemIndex + 1}.{''}
                                    {itemData}
                                  </p>
                                );
                              },
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  }
                })()
              ) : item.contentType === 'agentForm' ? (
                (() => {
                  let newContent = JSON.parse(item.content);
                  {
                    /*form表单*/
                  }
                  return (
                    <div className={styles.customItem}>
                      <div className={styles.customContent}>
                        <div className={styles.formContainer}>
                          {newContent?.formList?.map(itemData => {
                            if (itemData.attributeType === '1') {
                              // 单行输入框
                              return (
                                <div className={styles.formItem}>
                                  <p className={styles.formTitle}>
                                    {itemData?.valueName
                                      ? itemData?.valueName
                                      : '--'}
                                  </p>
                                  <div className={styles.formValueText}>
                                    {itemData?.value ? itemData?.value : '--'}
                                  </div>
                                </div>
                              );
                            } else if (itemData.attributeType === '2') {
                              // 多行输入框
                              return (
                                <div className={styles.formItem}>
                                  <p className={styles.formTitle}>
                                    {itemData?.valueName
                                      ? itemData?.valueName
                                      : '--'}
                                  </p>
                                  <div className={styles.formValueText}>
                                    <ReactMarkdown>
                                      {itemData.value ? itemData.value : '--'}
                                    </ReactMarkdown>
                                  </div>
                                </div>
                              );
                            } else if (itemData.attributeType === '3') {
                              // 单选下拉框
                              return (
                                <div className={styles.formItem}>
                                  <p className={styles.formTitle}>
                                    {itemData?.valueName
                                      ? itemData?.valueName
                                      : '--'}
                                  </p>
                                  <div className={styles.formValueText}>
                                    {itemData?.value ? itemData?.value : '--'}
                                  </div>
                                </div>
                              );
                            } else if (itemData.attributeType === '4') {
                              let newData = itemData?.value;
                              // 多选下拉框
                              return (
                                <div className={styles.formItem}>
                                  <p className={styles.formTitle}>
                                    {itemData?.valueName
                                      ? itemData?.valueName
                                      : '--'}
                                  </p>
                                  <div className={styles.formValueText}>
                                    {newData?.length > 0
                                      ? newData?.map(tagItem => {
                                          return <Tag>{tagItem}</Tag>;
                                        })
                                      : '--'}
                                  </div>
                                </div>
                              );
                            } else if (itemData.attributeType === '5') {
                              // 单选框
                              return (
                                <div className={styles.formItem}>
                                  <p className={styles.formTitle}>
                                    {itemData?.valueName
                                      ? itemData?.valueName
                                      : '--'}
                                  </p>
                                  <div className={styles.formValueText}>
                                    <Radio.Group
                                      value={itemData?.value}
                                      disabled
                                    >
                                      {itemData?.attributeValues?.map(
                                        option => {
                                          return (
                                            <Radio value={option.value}>
                                              {option.name}
                                            </Radio>
                                          );
                                        },
                                      )}
                                    </Radio.Group>
                                  </div>
                                </div>
                              );
                            } else if (itemData.attributeType === '6') {
                              let newData = itemData?.value;
                              // 复选框
                              return (
                                <div className={styles.formItem}>
                                  <p className={styles.formTitle}>
                                    {itemData?.valueName
                                      ? itemData?.valueName
                                      : '--'}
                                  </p>
                                  <div className={styles.formValueText}>
                                    <Checkbox.Group
                                      disabled={true}
                                      value={newData}
                                    >
                                      {itemData?.attributeValues?.map(
                                        option => {
                                          return (
                                            <Checkbox value={option.value}>
                                              {option.name}
                                            </Checkbox>
                                          );
                                        },
                                      )}
                                    </Checkbox.Group>
                                  </div>
                                </div>
                              );
                            } else if (
                              itemData.attributeType === '7' ||
                              itemData.attributeType === '8'
                            ) {
                              // 时间选择
                              return (
                                <div className={styles.formItem}>
                                  <p className={styles.formTitle}>
                                    {itemData?.valueName
                                      ? itemData?.valueName
                                      : '--'}
                                  </p>
                                  <div className={styles.formValueText}>
                                    {itemData?.value ? itemData?.value : '--'}
                                  </div>
                                </div>
                              );
                            } else if (
                              itemData.attributeType === '9' ||
                              itemData.attributeType === '10'
                            ) {
                              let newData = itemData?.value
                                ? itemData?.value?.join(' - ')
                                : '--';
                              // 时间选择范围
                              return (
                                <div className={styles.formItem}>
                                  <p className={styles.formTitle}>
                                    {itemData?.valueName
                                      ? itemData?.valueName
                                      : '--'}
                                  </p>
                                  <div className={styles.formValueText}>
                                    {newData ? newData : '--'}
                                  </div>
                                </div>
                              );
                            } else if (itemData.attributeType === '11') {
                              // 文件上传
                              return (
                                <div className={styles.formItem}>
                                  <p className={styles.formTitle}>
                                    {itemData?.valueName
                                      ? itemData?.valueName
                                      : '--'}
                                  </p>
                                  <div className={styles.formValueText}>
                                    {itemData?.value?.map(fileItem => {
                                      return (
                                        <div className={styles.fileListItem}>
                                          <span className={styles.documentIcon}>
                                            {DocumentIcon()}
                                          </span>
                                          <p className={styles.fileName}>
                                            {fileItem.fileName}
                                          </p>
                                          <a
                                            download
                                            href={fileItem.path}
                                            className={styles.downloadIcon}
                                          >
                                            {DownloadIcon()}
                                          </a>
                                        </div>
                                      );
                                    })}
                                  </div>
                                </div>
                              );
                            }
                          })}
                        </div>
                      </div>
                    </div>
                  );
                })()
              ) : (
                ''
              )
            ) : (
              <div className={styles.messageItem_right_wrapper_upload}>
                <div className={styles.messageItem_right_wrapper_upload_left}>
                  <div>{item.fileName}</div>
                  <div>
                    {getIntl().formatMessage({
                      id: 'im.chat.uploading',
                      defaultMessage: 'Uploading',
                    })}{' '}
                    {item.uploadProgress}%
                  </div>
                </div>
                <div className={styles.messageItem_right_wrapper_upload_right}>
                  <Progress
                    width={45}
                    type="circle"
                    trailColor="#e6f4ff"
                    strokeColor="#ad30e5"
                    percent={item.uploadProgress}
                    strokeWidth={10}
                    circleTextFontSize="15"
                    format={percent => <span></span>}
                  />
                  {/*<span style={{position: 'absolute',top: '24px', left: '16px', display: 'flex',justifyContent: 'center',alignItems: 'center'}}>{percent}%</span>*/}
                </div>
              </div>
            )}
          </div>
        </div>
      );
    }
    // 如果是客户消息
    if (item.type === 'role' && item.role === 'customer') {
      return (
        <div className={styles.messageItem_left} key={index}>
          {item.username ? (
            <div
              className={styles.headAva}
              style={{
                backgroundColor: this.state.currentSessionData
                  .customerAvatorColor
                  ? this.state.currentSessionData.customerAvatorColor
                  : '#3463FC',
              }}
            >
              <span>{item?.username?.substr(0, 1)}</span>
            </div>
          ) : (
            <div className={styles.headAva}>{CustomerPhoto()}</div>
          )}
          <div className={styles.messageItem_left_wrapper}>
            {/* 展示客户名称和消息时间 */}
            {!item.time && !item.username ? (
              ''
            ) : (
              <div className={styles.messageItem_left_wrapper_row1}>
                <span>{item?.username}</span>
                <span>{item.time}</span>
              </div>
            )}
            {/* 展示聊天内容，区分文字，markdown，图片，视频，音频，链接 */}
            {item.contentType === 'text' ||
            item.contentType === 'text/pain' ||
            item.contentType === 'markdown' ? (
              <div className={styles.messageItem_left_wrapper_row2}>
                <div
                  className={styles.messageItem_left_wrapper_row2_body}
                  onClick={e => this.handleFunctionClick(e, 'customer', item)}
                >
                  {/* 展示聊天文字内容，maskSensitiveInfo用于正则判断是否是电话，是的话加下划线电话 */}

                  <div onClick={() => this.handleOpenChange(item.content)}>
                    {this.maskSensitiveInfo(item.content)}
                  </div>
                  {/* 下面是翻译 */}
                  {item.translationStatus !== '' &&
                  item.translationStatus !== 0 ? (
                    <div>
                      {item.translationStatus === 1 ? <Spin /> : ''}
                      {item.translationStatus === 2 ? (
                        <span>
                          {this.maskSensitiveInfo(item.translationContent)}
                        </span>
                      ) : (
                        ''
                      )}
                      {item.translationStatus === 3 ? (
                        <a
                          onClick={() => this.handleClickRetryTranslation(item)}
                        >
                          {getIntl().formatMessage({
                            id: 'im.chat.translation.retry',
                            defaultMessage: 'Retry',
                          })}
                        </a>
                      ) : (
                        ''
                      )}
                      {item.translationStatus === 4 ? (
                        <span>
                          {getIntl().formatMessage({
                            id: 'piecemealWhatsApp.translation.retry',
                          })}
                        </span>
                      ) : (
                        ''
                      )}
                    </div>
                  ) : (
                    ''
                  )}
                </div>
                {/* <div className={styles.messageItem_left_wrapper_row3_text}>
                  <span>{item.status === 0 ? Unread() : Read()}</span>
                </div> */}
              </div>
            ) : item.contentType === 'link' ? (
              <div
                className={styles.messageItem_left_wrapper_row2}
                onClick={e =>
                  this.handleFunctionClick(e, 'customer-noCopy', item)
                }
              >
                <div className={styles.messageItem_left_wrapper_row2_body}>
                  <a href={item.filePath || item.content}>
                    {decodeURIComponent(item.fileName) || item.content}
                  </a>
                </div>
              </div>
            ) : item.contentType === 'image' ? (
              <div
                className={styles.messageItem_left_wrapper_row4}
                onClick={e =>
                  this.handleFunctionClick(e, 'customer-noCopy', item)
                }
              >
                <img
                  src={item.content}
                  alt={item.fileName}
                  style={{ width: '250px', height: 'auto' }}
                />
              </div>
            ) : item.contentType === 'video' ? (
              <div
                className={styles.messageItem_left_wrapper_row5}
                onClick={e =>
                  this.handleFunctionClick(e, 'customer-noCopy', item)
                }
              >
                <video src={item.content} controls />
              </div>
            ) : item.contentType === 'audio' ? (
              <div
                style={{ display: 'inline-block' }}
                onClick={e =>
                  this.handleFunctionClick(e, 'customer-noCopy', item)
                }
              >
                <AudioPlayer src={item.content} id={index} />
              </div>
            ) : item.contentType === 'audioText' ? (
              <div
                style={{ display: 'inline-block' }}
                onClick={e =>
                  this.handleFunctionClick(e, 'customer-noCopy', item)
                }
              >
                <AudioPlayer src={JSON.parse(item.content).url} id={index} />
                {JSON.parse(item.content).content ? (
                  <div className={styles.transcriptionContainer}>
                    <span>{JSON.parse(item.content).content}</span>
                  </div>
                ) : null}
              </div>
            ) : item.contentType === 'card' ? (
              (() => {
                let newContent = JSON.parse(item.content);
                // cardLayout  1 轮播，2 列表
                if (newContent?.cardLayout === 1) {
                  return (
                    <div className={styles.customItem}>
                      <div className={styles.customTitle}>
                        <img src={CustomIcon} />
                        <span className={styles.customName}>
                          <span>{item.reply_person}</span>
                          <FormattedMessage
                            id="work.order.reply.custom.name"
                            defaultMessage="（客户）"
                          />
                        </span>
                        <div className={styles.timeText}>
                          <span>{item.reply_time}</span>
                        </div>
                      </div>
                      <div className={styles.customContent}>
                        <div className={styles.dynamicCard}>
                          <div className={styles.carouselItem}>
                            <img src={newContent.cardImageUrl} />
                            <p
                              title={newContent?.cardTitle}
                              className={styles.topTitleText}
                            >
                              {newContent?.cardTitle
                                ? newContent?.cardTitle
                                : '--'}
                            </p>
                            <div className={styles.bottomContainer}>
                              <p
                                className={styles.priceContainer}
                                title={newContent?.cardPrice}
                              >
                                {/*<span className={styles.unit}>￥</span>*/}
                                {newContent?.cardPrice && (
                                  <span className={styles.num}>
                                    {newContent?.cardPrice
                                      ? newContent?.cardPrice
                                      : '0.00'}
                                  </span>
                                )}
                              </p>
                              {newContent?.cardNumber && (
                                <p className={styles.numText}>
                                  x{' '}
                                  {newContent?.cardNumber
                                    ? newContent?.cardNumber
                                    : 0}
                                </p>
                              )}
                              {newContent?.cardStatus && (
                                <p className={styles.logisticsStatus}>
                                  {newContent?.cardStatus
                                    ? newContent?.cardStatus
                                    : '--'}
                                </p>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                } else {
                  return (
                    <div className={styles.customItem}>
                      {/* <div className={styles.customTitle}>
                        <img src={CustomIcon} />
                        <span className={styles.customName}>
                          <span>{item.reply_person}</span>
                          <FormattedMessage
                            id="work.order.reply.custom.name"
                            defaultMessage="（客户）"
                          />
                        </span>
                        <div className={styles.timeText}>
                          <span>{item.reply_time}</span>
                        </div>
                      </div> */}
                      <div className={styles.customContent}>
                        <div className={styles.dynamicCard}>
                          <Card>
                            <div className={styles.leftContainer}>
                              <img src={newContent.cardImageUrl} />
                            </div>
                            <div className={styles.rightContainer}>
                              <div className={styles.topContainer}>
                                <p
                                  title={newContent?.cardTitle}
                                  className={styles.topTitleText}
                                >
                                  {newContent?.cardTitle
                                    ? newContent?.cardTitle
                                    : '--'}
                                </p>
                                <p className={styles.priceContent}>
                                  {/*<span className={styles.priceUnit}>￥</span>*/}
                                  {newContent?.cardPrice && (
                                    <span
                                      title={newContent?.cardPrice}
                                      className={styles.priceText}
                                    >
                                      {newContent?.cardPrice
                                        ? newContent?.cardPrice
                                        : '0.00'}
                                    </span>
                                  )}
                                </p>
                                {newContent?.cardNumber && (
                                  <p className={styles.numText}>
                                    x{' '}
                                    {newContent?.cardNumber
                                      ? newContent?.cardNumber
                                      : 0}
                                  </p>
                                )}
                              </div>
                              {newContent?.cardStatus && (
                                <p className={styles.logisticsStatus}>
                                  {newContent?.cardStatus
                                    ? newContent.cardStatus
                                    : '--'}
                                </p>
                              )}
                            </div>
                          </Card>
                        </div>
                      </div>
                    </div>
                  );
                }
              })()
            ) : item.contentType === 'hotIssues' ? (
              (() => {
                let newContent = JSON.parse(item.content);
                // configType 1 手动；2 自动
                // showType 1 横；2 竖
                if (newContent.configType === 1) {
                  if (newContent.showType === 1) {
                    const tabItems = newContent.issues.map((issue, index) => {
                      // 为每个 issueContents 字符串创建一个段落或其他元素
                      const childrenContent = (
                        <div>
                          {issue.issueContents.map((content, contentIndex) => (
                            <p
                              title={content}
                              key={contentIndex}
                              className={styles.panelText}
                            >
                              {' '}
                              {/* 使用 styles.panelText 类名如果需要的话 */}
                              {/* 您可以根据需要添加编号，例如：`${contentIndex + 1}. ${content}` */}
                              {`${contentIndex + 1}. ${content}`}
                            </p>
                          ))}
                        </div>
                      );

                      return {
                        label: issue.issueName,
                        key: String(index + 1), // 使用索引加1作为 key，确保唯一性
                        children: childrenContent,
                      };
                    });

                    return (
                      <div className={styles.agentItem}>
                        <div className={styles.agentTitle}>
                          <img src={AgentIcon} />
                          <span className={styles.agentName}>
                            <span>{item.reply_person}</span>
                            <FormattedMessage
                              id="work.order.reply.robot.name"
                              defaultMessage="（机器人）"
                            />
                          </span>
                          <div className={styles.timeText}>
                            {item.reply_time}
                          </div>
                        </div>
                        <div className={styles.agentContent}>
                          <div className={styles.hotspotIssuesContainer}>
                            <div className={styles.headerTitle}>
                              <span className={styles.titleIcon}>
                                {HotspotIssuesIcon()}
                              </span>
                              <span>
                                <FormattedMessage
                                  id="work.order.detail.content.type.hot.spot.issues"
                                  defaultMessage="热点问题"
                                />
                              </span>
                            </div>
                            <Tabs defaultActiveKey="1" items={tabItems} />
                          </div>
                        </div>
                      </div>
                    );
                  } else {
                    let issuesData = newContent.issues;
                    return (
                      <div className={styles.agentItem}>
                        <div className={styles.agentTitle}>
                          <img src={AgentIcon} />
                          <span className={styles.agentName}>
                            <span>{item.reply_person}</span>
                            <FormattedMessage
                              id="work.order.reply.robot.name"
                              defaultMessage="（机器人）"
                            />
                          </span>
                          <div className={styles.timeText}>
                            {item.reply_time}
                          </div>
                        </div>
                        <div className={styles.agentContent}>
                          <div className={styles.hotspotIssuesContainer}>
                            <div className={styles.headerTitle}>
                              <span className={styles.titleIcon}>
                                {HotspotIssuesIcon()}
                              </span>
                              <span>
                                <FormattedMessage
                                  id="work.order.detail.content.type.hot.spot.issues"
                                  defaultMessage="热点问题"
                                />
                              </span>
                            </div>
                            <Collapse
                              accordion
                              expandIconPosition={'end'}
                              ghost={true}
                            >
                              {issuesData?.map((itemData, index) => {
                                return (
                                  <Panel
                                    header={itemData?.issueName}
                                    key={index + 1}
                                  >
                                    {itemData?.issueContents?.map(
                                      (detailData, detailIndex) => {
                                        return (
                                          <p
                                            title={detailData}
                                            className={styles.panelText}
                                          >
                                            {detailIndex + 1}. {detailData}
                                          </p>
                                        );
                                      },
                                    )}
                                  </Panel>
                                );
                              })}
                            </Collapse>
                          </div>
                        </div>
                      </div>
                    );
                  }
                } else {
                  return (
                    <div className={styles.agentItem}>
                      <div className={styles.agentTitle}>
                        <img src={AgentIcon} />
                        <span className={styles.agentName}>
                          <span>{item.reply_person}</span>
                          <FormattedMessage
                            id="work.order.reply.robot.name"
                            defaultMessage="（机器人）"
                          />
                        </span>
                        <div className={styles.timeText}>2025-05-14</div>
                      </div>
                      <div className={styles.agentContent}>
                        <div className={styles.hotspotIssuesContainer}>
                          <div className={styles.headerTitle}>
                            <span className={styles.titleIcon}>
                              {HotspotIssuesIcon()}
                            </span>
                            <span>
                              <FormattedMessage
                                id="work.order.detail.content.type.hot.spot.issues"
                                defaultMessage="热点问题"
                              />
                            </span>
                          </div>
                          {newContent?.issues[0]?.issueContents?.map(
                            (itemData, itemIndex) => {
                              return (
                                <p
                                  title={itemData}
                                  className={styles.panelText1}
                                >
                                  {itemIndex + 1}.{''}
                                  {itemData}
                                </p>
                              );
                            },
                          )}
                        </div>
                      </div>
                    </div>
                  );
                }
              })()
            ) : item.contentType === 'agentForm' ? (
              (() => {
                let newContent = JSON.parse(item.content);
                {
                  /*form表单*/
                }
                return (
                  <div className={styles.customItem}>
                    <div className={styles.customContent}>
                      <div className={styles.formContainer}>
                        {newContent?.formList?.map(itemData => {
                          if (itemData.attributeType === '1') {
                            // 单行输入框
                            return (
                              <div className={styles.formItem}>
                                <p className={styles.formTitle}>
                                  {itemData?.valueName
                                    ? itemData?.valueName
                                    : '--'}
                                </p>
                                <div className={styles.formValueText}>
                                  {itemData?.value ? itemData?.value : '--'}
                                </div>
                              </div>
                            );
                          } else if (itemData.attributeType === '2') {
                            // 多行输入框
                            return (
                              <div className={styles.formItem}>
                                <p className={styles.formTitle}>
                                  {itemData?.valueName
                                    ? itemData?.valueName
                                    : '--'}
                                </p>
                                <div className={styles.formValueText}>
                                  <ReactMarkdown>
                                    {itemData.value ? itemData.value : '--'}
                                  </ReactMarkdown>
                                </div>
                              </div>
                            );
                          } else if (itemData.attributeType === '3') {
                            // 单选下拉框
                            return (
                              <div className={styles.formItem}>
                                <p className={styles.formTitle}>
                                  {itemData?.valueName
                                    ? itemData?.valueName
                                    : '--'}
                                </p>
                                <div className={styles.formValueText}>
                                  {itemData?.value ? itemData?.value : '--'}
                                </div>
                              </div>
                            );
                          } else if (itemData.attributeType === '4') {
                            let newData = itemData?.value;
                            // 多选下拉框
                            return (
                              <div className={styles.formItem}>
                                <p className={styles.formTitle}>
                                  {itemData?.valueName
                                    ? itemData?.valueName
                                    : '--'}
                                </p>
                                <div className={styles.formValueText}>
                                  {newData?.length > 0
                                    ? newData?.map(tagItem => {
                                        return <Tag>{tagItem}</Tag>;
                                      })
                                    : '--'}
                                </div>
                              </div>
                            );
                          } else if (itemData.attributeType === '5') {
                            // 单选框
                            return (
                              <div className={styles.formItem}>
                                <p className={styles.formTitle}>
                                  {itemData?.valueName
                                    ? itemData?.valueName
                                    : '--'}
                                </p>
                                <div className={styles.formValueText}>
                                  <Radio.Group value={itemData?.value} disabled>
                                    {itemData?.attributeValues?.map(option => {
                                      return (
                                        <Radio value={option.value}>
                                          {option.name}
                                        </Radio>
                                      );
                                    })}
                                  </Radio.Group>
                                </div>
                              </div>
                            );
                          } else if (itemData.attributeType === '6') {
                            let newData = itemData?.value;
                            // 复选框
                            return (
                              <div className={styles.formItem}>
                                <p className={styles.formTitle}>
                                  {itemData?.valueName
                                    ? itemData?.valueName
                                    : '--'}
                                </p>
                                <div className={styles.formValueText}>
                                  <Checkbox.Group
                                    disabled={true}
                                    value={newData}
                                  >
                                    {itemData?.attributeValues?.map(option => {
                                      return (
                                        <Checkbox value={option.value}>
                                          {option.name}
                                        </Checkbox>
                                      );
                                    })}
                                  </Checkbox.Group>
                                </div>
                              </div>
                            );
                          } else if (
                            itemData.attributeType === '7' ||
                            itemData.attributeType === '8'
                          ) {
                            // 时间选择
                            return (
                              <div className={styles.formItem}>
                                <p className={styles.formTitle}>
                                  {itemData?.valueName
                                    ? itemData?.valueName
                                    : '--'}
                                </p>
                                <div className={styles.formValueText}>
                                  {itemData?.value ? itemData?.value : '--'}
                                </div>
                              </div>
                            );
                          } else if (
                            itemData.attributeType === '9' ||
                            itemData.attributeType === '10'
                          ) {
                            let newData = itemData?.value
                              ? itemData?.value?.join(' - ')
                              : '--';
                            // 时间选择范围
                            return (
                              <div className={styles.formItem}>
                                <p className={styles.formTitle}>
                                  {itemData?.valueName
                                    ? itemData?.valueName
                                    : '--'}
                                </p>
                                <div className={styles.formValueText}>
                                  {newData ? newData : '--'}
                                </div>
                              </div>
                            );
                          } else if (itemData.attributeType === '11') {
                            // 文件上传
                            return (
                              <div className={styles.formItem}>
                                <p className={styles.formTitle}>
                                  {itemData?.valueName
                                    ? itemData?.valueName
                                    : '--'}
                                </p>
                                <div className={styles.formValueText}>
                                  {itemData?.value?.map(fileItem => {
                                    return (
                                      <div className={styles.fileListItem}>
                                        <span className={styles.documentIcon}>
                                          {DocumentIcon()}
                                        </span>
                                        <p className={styles.fileName}>
                                          {fileItem.fileName}
                                        </p>
                                        <a
                                          download
                                          href={fileItem.path}
                                          className={styles.downloadIcon}
                                        >
                                          {DownloadIcon()}
                                        </a>
                                      </div>
                                    );
                                  })}
                                </div>
                              </div>
                            );
                          }
                        })}
                      </div>
                    </div>
                  </div>
                );
              })()
            ) : (
              ''
            )}
            {/* 展示进线意图 */}
            {this.state.worktable_setting_ai &&
              item.incomingIntentName &&
              item.intelligentAgentName && (
                <div className={styles.messageItem_left_wrapper_row6}>
                  <img src={workTableSettingAi} width={12} height={12} />
                  <span>
                    <FormattedMessage
                      id="new.worktable.layout.intend.info"
                      values={{
                        incomingIntentName: item.incomingIntentName,
                        intelligentAgentName: item.intelligentAgentName,
                      }}
                    />
                  </span>
                  <Popconfirm
                    title={getIntl().formatMessage({
                      id: 'new.worktable.layout.popconfirm',
                      defaultValue:
                        '隐藏后，后续可到工作台的「设置」重新打开当前提醒',
                    })}
                    onConfirm={() => this.handleHiddenEye()}
                  >
                    <span style={{ cursor: 'pointer', color: '#3463FC' }}>
                      <img
                        src={worktableLayoutHiddenEye}
                        width={12}
                        height={12}
                      />
                      <FormattedMessage id="contact.customers.consultation.record.hide" />
                    </span>
                  </Popconfirm>
                </div>
              )}
          </div>
        </div>
      );
    }
  };

  // 显示智能填单选择语言弹窗
  showIntelligentFormFillingSelectLanguage = () => {
    let { translationCodeList } = this.state;
    this.props.dispatch({
      type: 'worktable/getTicketSmartFillLanguage',
      callback: response => {
        if (response.code === 200) {
          let languagesCodeList = response.data.languagesCodeList;
          const filteredLanguages = translationCodeList.filter(language =>
            languagesCodeList?.includes(language.value),
          );
          const deduplicated = Array.from(
            new Map(
              filteredLanguages?.map(item => [item.value, item]),
            ).values(),
          );
          const language = localStorage.getItem('lang');
          let lang = '';
          if (language === 'zh-CN') {
            lang = 'zh-CN';
          } else if (language === 'en-US') {
            lang = 'en-US';
          } else if (language === 'de-DE') {
            lang = 'de';
          } else if (language === 'ja') {
            lang = 'ja';
          } else if (language === 'id-ID') {
            lang = 'id-ID';
          }
          const hasZhCN = languagesCodeList.includes(lang);
          this.setState({
            selectLanguageModal: true,
            lang: hasZhCN ? lang : '',
            filteredLanguages: deduplicated,
          });
        } else {
          this.setState({
            expand: false,
            inputLoading: false,
          });
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  handleCancelIntelligentFormFilling = () => {
    this.setState({
      selectLanguageModal: false,
    });
  };
  // 选择语言
  handleChangeLanguage = e => {
    this.setState({
      lang: e,
    });
  };

  // 智能填单
  handleIntelligentFormFilling = () => {
    const { currentSessionData, lang } = this.state;
    if (!lang) {
      notification.warning({
        message: getIntl().formatMessage({
          id: 'home.set.language.select',
          defaultValue: '请选择语言',
        }),
      });
      return;
    }
    const summaryResult = this.handleCountChatMsg();
    if (summaryResult.length > 0 && lang) {
      this.setState({
        loadingIntelligentFormFilling: true,
        selectLanguageModal: false,
      });
      let params = {
        content: summaryResult,
        customerId: currentSessionData.uid,
        workRecordId: currentSessionData.ticketId,
        language: lang,
      };
      this.setState({
        intelligentFormFillingTicketId: currentSessionData.ticketId,
      });
      this.props.dispatch({
        type: 'intelligentFormFilling/aigcTicketSmartFill',
        payload: params,
        callback: response => {
          if (response.code === 200) {
            // notification.success({
            //   message: response.msg,
            // });
            this.onFinish(response.data, params);
            this.setState(
              {
                ticketSmartFill: response.data,
                ticketSmartFillInitData: response.data,
              },
              // () => {
              //   // 将数据转为可回显到form表单中的格式
              //   const transformedList = {
              //     aigcTicketSmartFillList: response.data?.aigcTicketSmartFillList.map(
              //       item => ({
              //         attrName: item.attrName,
              //         storeAttrType: item.storeAttrType,
              //         [item.attrName]: item.attrValue,
              //       }),
              //     ),
              //   };
              //   this.smartFillRef.current?.setFieldsValue(transformedList);
              // },
            );
          } else if (response.code === 1001) {
            this.setState({
              loadingIntelligentFormFilling: false,
            });
            notification.warning({
              message: response.msg,
            });
          } else {
            this.setState({
              loadingIntelligentFormFilling: false,
            });
            notification.error({
              message: response.msg,
            });
          }
        },
      });
    } else {
      notification.warning({
        message: getIntl().formatMessage({
          id: 'intelligent.form.filling.worktable.effective.content.tips',
          defaultValue: '暂无有效内容用来智能填单！',
        }),
      });
    }
  };
  // 保存智能填单
  onFinish = (values, paramsData) => {
    let params = {
      customerId: paramsData.customerId,
      workRecordId: paramsData.workRecordId,
      aigcTicketSmartFillList: values.aigcTicketSmartFillList,
      interfaceType: 1, //1--新增  2--修改
    };
    this.props.dispatch({
      type: 'intelligentFormFilling/addAigcTicketSmartFill',
      payload: params,
      callback: response => {
        this.setState({
          loadingIntelligentFormFilling: false,
        });
        if (response.code === 200) {
          this.setState({
            ticketSmartFill: {},
          });
          this.getIntelligentFormFillingContent(params);
          notification.success({
            message: response.msg,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 取消保存智能填单
  handleCancelSaveFormFill = () => {
    this.setState({
      ticketSmartFill: {},
    });
  };

  // 将智能存单内容存放到聊天内容中
  getIntelligentFormFillingContent = async params => {
    let {
      currentSessionData,
      ticketSmartFillInitData,
      intelligentFormFillingTicketId,
    } = this.state;
    let { workRecordId, aigcTicketSmartFillList, customerId } = params;

    let messageData = {
      ticketId: intelligentFormFillingTicketId,
      content: '',
      contentType: 'text',
      role: 'system',
      type: 'intelligentFormFilling',
      username: this.props.user.userName,
      uid: customerId,
      ticketSmartFillListStatus: false, // 修改智能填单状态
      translationContent: '', // 翻译的内容
      translationStatus: '',
    };
    messageData.content = {
      aigcTicketSmartFillList: aigcTicketSmartFillList,
    };
    messageData.content = JSON.stringify(messageData.content);
    // 添加消息数据到到消息列表中
    await this.props.handleAddMessageNew(messageData, false);
  };

  // 点击修改智能填单内容
  handleClickEditorIntelligentFormFilling = (detailDataList, itemData) => {
    detailDataList = detailDataList.map(item =>
      item === itemData ? { ...item, ticketSmartFillListStatus: true } : item,
    );
    let currentSessionData1 = { ...this.state.currentSessionData };
    currentSessionData1.messageList = detailDataList;

    this.setState({
      currentSessionData: currentSessionData1,
    });
  };
  // 取消修改智能填单
  handleCancelFormFill = (detailDataList, itemData) => {
    detailDataList = detailDataList.map(item =>
      item === itemData ? { ...item, ticketSmartFillListStatus: false } : item,
    );
    let currentSessionData1 = { ...this.state.currentSessionData };
    currentSessionData1.messageList = detailDataList;

    this.setState({
      currentSessionData: currentSessionData1,
    });
  };

  // 保存修改智能填单
  onFinish1 = itemData => {
    this.setState({
      loadingSave: true,
    });
    let { currentSessionData } = this.state;
    const values = this.smartFillRef1?.current?.getFieldsValue();
    let newContent = itemData.content ? JSON.parse(itemData.content) : '';
    // 将 updateData 扁平化为 key-value 映射
    const updates = values.aigcTicketSmartFillList.reduce((acc, item) => {
      const key = Object.keys(item)[0];
      acc[key] = item[key];
      return acc;
    }, {});
    // 生成新数据（不修改原始）
    const newData = {
      aigcTicketSmartFillList: newContent.aigcTicketSmartFillList.map(item => ({
        ...item,
        attrValue: updates[item.attrName] ?? item.attrValue, // 如果有更新就替换
      })),
    };
    let params = {
      customerId: currentSessionData.uid,
      workRecordId: currentSessionData.ticketId,
      aigcTicketSmartFillList: newData.aigcTicketSmartFillList,
      interfaceType: 2, //1--新增  2--修改
    };
    this.props.dispatch({
      type: 'intelligentFormFilling/addAigcTicketSmartFill',
      payload: params,
      callback: response => {
        this.setState({
          loadingSave: false,
        });
        if (response.code === 200) {
          this.setState({
            ticketSmartFill: {},
          });
          itemData.content = JSON.stringify(newData);
          let detailDataList = currentSessionData.messageList.map(item =>
            item === itemData
              ? { ...item, ticketSmartFillListStatus: false }
              : item,
          );
          let currentSessionData1 = { ...this.state.currentSessionData };
          currentSessionData1.messageList = detailDataList;
          this.setState({
            currentSessionData: currentSessionData1,
          });

          // 更新会话列表数据。
          this.props.updateSessionBySessionId(
            currentSessionData.ticketId,
            'messageList',
            detailDataList,
          );

          notification.success({
            message: response.msg,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  //复制
  handleCopyMessage = content => {
    console.log(
      this.state.popoverPosition,
      content,
      this.state.inputValue,
      'this.state.popoverPosition',
    );
    this.inputRef.current?.focus();
    //单纯处理带传参数的文字数据，比如重新编辑
    if (content) {
      this.inputRef.current?.setContent(this.state.inputValue + content);
      return;
    }
    //处理所有类型的复制
    if (
      ['markdown'].includes(this.state.popoverPosition?.content?.contentType)
    ) {
      //下面的逻辑是复制图片
      // const formattedContent = convertMarkdownImageToHtmlImage(
      //   this.state.popoverPosition?.content?.content,
      // );
      // this.inputRef.current?.handlePaste(formattedContent);
      this.inputRef.current?.setContent(
        this.state.inputValue + this.state.popoverPosition?.content?.content,
      );
    } else {
      this.inputRef.current?.setContent(
        this.state.inputValue + this.state.popoverPosition?.content?.content,
      );
    }
  };
  //引用
  handleReferencesMessage = () => {
    this.inputRef.current?.focus();
    this.setState({
      isReferencesMessage: true,
      referencesMessageContent: this.state.popoverPosition?.content,
    });
  };
  //撤回
  handleWithdrawMessage = () => {
    console.log(this.state.popoverPosition, 'this.state.popoverPosition');
    IMWebClient.withdrawMessage({
      uid: this.props.user.userId,
      tenantId: this.props.user.companyId,
      sessionId: this.state.currentSessionData.ticketId,
      role:
        this.props.user.roleList?.[0]?.roleId === '1005'
          ? 'agent_admin'
          : 'agent',
      contentId: this.state.popoverPosition?.content?.id,
    });
    //后面接受im事件，成功的话前端改变数据,关注父组件handleMessageWithdrawEvent函数
  };
  //标记为撤回的信息
  handleMarkAsRead = id => {
    // 找到当前消息在messageList中的索引
    const messageList = this.state.currentSessionData.messageList || [];
    // 更新消息状态为撤回（contentType设为999）
    const updatedMessageList = messageList.map(message => {
      if (message.id === id) {
        return {
          ...message,
          dataStatus: '999',
        };
      }
      return message;
    });

    // 更新state中的消息列表
    this.setState(
      {
        currentSessionData: {
          ...this.state.currentSessionData,
          messageList: updatedMessageList,
        },
      },
      () => {
        console.log(
          this.state.currentSessionData,
          'this.state.currentSessionData',
        );
      },
    );
  };
  //正在输入
  IMHandleDown = () => {
    IMWebClient.typingMessage({
      sessionId: this.state.currentSessionData.ticketId,
      role:
        this.props.user.roleList?.[0]?.roleId === '1005'
          ? 'agent_admin'
          : 'agent',
    });
  };
  //模版类型选择
  handleAddSelectWhatsApp = value => {
    this.setState(
      {
        whatsAppType: value,
      },
      () => {
        this.queryWhatsAppMessage();
      },
    );
  };
  //选择模版
  handleClickWhatsAppMessage = item => {
    let whatsAppMessage = item;
    // 初始化whatsAppParameters对象
    let parameters = {};
    //记录内容
    let content = '';
    item.templateContentList?.forEach((itemSon, indexSon) => {
      whatsAppMessage[itemSon.type] = {
        ...itemSon,
      };
      if (itemSon.type === 'BODY') {
        content = itemSon.text;
      }
      parameters[itemSon.type] = {};
      // 检查text中是否存在{{}}格式的内容
      if (itemSon.text) {
        // 使用正则表达式匹配所有{{}}格式的内容
        const matches = itemSon.text.match(/\{\{([^}]+)\}\}/g);

        if (matches && matches.length > 0) {
          // 遍历所有匹配项
          matches.forEach(match => {
            // 提取括号中的内容作为key
            const key = match.replace(/\{\{|\}\}/g, '');
            // 将key初始化为空字符串
            parameters[itemSon.type][key] = '';
          });
        }
      }
    });
    console.log(whatsAppMessage, parameters, 'whatsAppMessage');

    this.setState({
      whatsAppMessageItemContent: content,
      whatsAppMessageItem: whatsAppMessage,
      whatsAppParameters: parameters,
      templateModal: true,
    });
  };

  //发送whatsApp模版
  handleSendWhatsApp = () => {
    this.setState({
      loadingModal: true,
    });
    let contentId = uuidv4();
    this.props.dispatch({
      type: 'worktable/sendWhatsAppMessage',
      payload: {
        channelId: this.state.currentSessionData.channelConfigId, //渠道id
        workRecordId: this.state.currentSessionData.ticketId, //工单id
        customerId: this.state.currentSessionData.customerId, //客户id
        templateUuid: this.state.whatsAppMessageItem?.templateUuid,
        parameters: this.state.whatsAppParameters, //发送消息参数 key 为需要替换的模版名字
        content: JSON.stringify({
          whatsAppMessageItem: this.state.whatsAppMessageItem,
          whatsAppParameters: this.state.whatsAppParameters,
        }), //完整消息，需要保存到工单的
        replyType: this.props.user.roleList?.[0]?.roleId === '1005' ? '8' : '1',
        contentType: '10', //whatsapp模版
        workRecordContentId: contentId,
      },
      callback: response => {
        if (response.code == 200) {
          this.setState({
            loadingModal: false,
            templateModal: false,
            whatsApp: false,
          });
          notification.success({
            message: response.msg,
          });
          //加到前端数据
          this.sendMessageWhatsApp(contentId);
        } else {
          this.setState({
            loadingModal: false,
          });
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  sendMessageWhatsApp = contentId => {
    let messageData = {
      ticketId: this.state.currentSessionData.ticketId,
      content: JSON.stringify({
        whatsAppMessageItem: this.state.whatsAppMessageItem,
        whatsAppParameters: this.state.whatsAppParameters,
      }),
      contactId: this.state.currentSessionData.contactId, //联系id
      contentType: 'whatsAppTemplate',
      role:
        this.props.user.roleList?.[0]?.roleId === '1005'
          ? 'agent_admin'
          : 'agent', //  agent
      type: 'role',
      username: this.props.user.userName,
      uid: this.state.currentSessionData.uid,
      id: contentId,
      uploadStatus: 1,
      translationContent: '', // 翻译的内容
      translationStatus: 0, // 0不翻译，1翻译中，2翻译成功，3翻译失败
      referenceContentId: this.state.referencesMessageContent.id,
      referenceReplyPerson: this.state.referencesMessageContent.username,
      referenceContent: ['4', 'link'].includes(
        String(this.state.referencesMessageContent.contentType),
      )
        ? this.state.referencesMessageContent.fileName
        : this.state.referencesMessageContent.content,
      referenceContentType: this.state.referencesMessageContent.contentType,
      referenceContentFile: ['4', 'link'].includes(
        String(this.state.referencesMessageContent.contentType),
      )
        ? this.state.referencesMessageContent.content
        : this.state.referencesMessageContent.filePath,
    };
    this.props.handleAddMessageNew(messageData, false);
  };
  render() {
    const {
      activeTab,
      currentSessionData,
      loading,
      socialMediaTimeStatus,
      replyModle,
      replyMessageModleData,
      loadingSummaryModal,
      contentSummary,
      customerMood,
      waitExecuteEvent,
      selectWaitExecuteList,
      addTodoStatus,
      waitExecuteList,
      loadingIntelligentFormFilling,
      ticketSmartFill,
      loadingBtn,
      selectLanguageModal,
      translationCodeList,
      filteredLanguages,
      lang,
    } = this.state;
    const content = () => {
      if (this.state.showQuickReply) {
        return (
          <QuickReply
            handleMouseInterUpdateInput={this.handleMouseInterUpdateInput}
            ref={this.quickReplyChild}
            onHandleCancel={this.handleCancel}
          />
        );
      }
    };

    return (
      <div
        className={styles.chat_Box}
        style={{
          maxWidth: `calc(100% - ${this.props.leftWidth +
            this.props.rightWidth}px)`,
          // maxWidth: 685,
        }}
      >
        <Spin spinning={loading}>
          {/************************************************************************************* 邮件的************************************************************************************/}
          {this.props.workTableTabValue === 3 ? (
            <div className={styles.chat_wrapper} id="chat_Box">
              {!this.props.emailSelectTicketData.workRecordId ? (
                <div className={styles.noDataWrapper}>
                  {/* <img src={NodataBg()} alt="" /> */}
                  {NodataBg()}
                  <span>
                    {getIntl().formatMessage({
                      id: 'im.chat.nodata',
                      defaultMessage:
                        'You currently have no unread messages...',
                    })}
                  </span>
                </div>
              ) : this.props.emailSelectTicketData.workRecordId ? (
                <React.Fragment>
                  <UserNav
                    customerTelephone={
                      this.props.emailSelectTicketData.customerTelephone
                    }
                    deleteTicket={this.props.deleteTicket}
                    currentSessionData={this.props.currentSessionData}
                    handleFinish={this.handleFinish}
                    workTypeDetail={this.state.workTypeDetail}
                    ticketId={this.props.emailSelectTicketData.workRecordId}
                    handleNavBarPhone={this.handleNavBarPhone}
                  />
                  <EmailDetailContent
                    emailTicketId={
                      this.props.emailSelectTicketData.workRecordId
                    }
                    customerTelephone={
                      this.props.emailSelectTicketData.customerTelephone
                    }
                    workTypeDetail={this.state.workTypeDetail}
                  />
                </React.Fragment>
              ) : (
                ''
              )}
            </div>
          ) : //**********************************************************************/ 电话的只用于展示备注,twilio展示转录信息**********************************************************************/
          this.props.workTableTabValue === 1 ? (
            <div className={styles.chat_wrapper} id="chat_Box">
              {!this.props.ticketId || !this.props.phoneNumberId ? (
                <div className={styles.noDataWrapper}>
                  {/* <img src={NodataBg()} alt="" /> */}
                  {NodataBg()}
                  <span>
                    {getIntl().formatMessage({
                      id: 'im.chat.nodata',
                      defaultMessage:
                        'You currently have no unread messages...',
                    })}
                  </span>
                </div>
              ) : this.props.connectType === 'Twilio' ? (
                <React.Fragment>
                  <UserNav
                    deleteTicket={this.props.deleteTicket}
                    currentSessionData={this.props.currentSessionData}
                    handleFinish={this.handleFinish}
                    workTypeDetail={this.state.workTypeDetail}
                    handleNavBarPhone={this.handleNavBarPhone}
                    ticketId={this.props.currentSessionData.ticketId}
                  />
                  {/* 消息体 */}
                  <div
                    className={styles.messageWrapper}
                    ref={this.imContainerRefs}
                  >
                    {currentSessionData.messageList &&
                    currentSessionData.messageList.length > 0 ? (
                      <div
                        className={styles.messageContainer}
                        ref={this.imContainerRef}
                      >
                        {/* 会话消息列表 */}
                        {currentSessionData.messageList &&
                        currentSessionData.messageList.length > 0
                          ? currentSessionData.messageList.map((item, index) =>
                              this.handleViewByCondition(
                                item,
                                index,
                                '',
                                currentSessionData.messageList,
                              ),
                            )
                          : ''}
                      </div>
                    ) : (
                      <div className={styles.noDataWrapper}>
                        <Spin spinning={this.props.isResyncLoading}>
                          {/* <img src={NodataBg()} alt="" /> */}
                          {NodataBg()}
                        </Spin>

                        {this.props.isResync ? (
                          <Button
                            icon={<ResyncIcon />}
                            onClick={() => this.props.handleResync()}
                            style={{
                              marginTop: 10,
                              display: 'flex',
                              alignItems: 'center',
                            }}
                          >
                            <span
                              style={{
                                marginLeft: 5,
                                marginTop: 0,
                                color: '#3463FC',
                              }}
                            >
                              <FormattedMessage id="im.twilio.components.resync" />
                            </span>
                          </Button>
                        ) : (
                          ''
                        )}
                      </div>
                    )}
                  </div>
                </React.Fragment>
              ) : (
                <React.Fragment>
                  <UserNav
                    deleteTicket={this.props.deleteTicket}
                    currentSessionData={this.props.currentSessionData}
                    handleFinish={this.handleFinish}
                    workTypeDetail={this.state.workTypeDetail}
                    ticketId={this.props.ticketId}
                    handleNavBarPhone={this.handleNavBarPhone}
                  />
                  <div
                    className={styles.messageWrapperPhone}
                    ref={this.imContainerRefs}
                  >
                    {currentSessionData.messageList &&
                    currentSessionData.messageList.length > 0 ? (
                      <div
                        className={styles.messageContainer}
                        ref={this.imContainerRef}
                      >
                        {/* 会话消息列表 */}
                        {currentSessionData.messageList &&
                        currentSessionData.messageList.length > 0
                          ? currentSessionData.messageList.map((item, index) =>
                              this.handleViewByCondition(
                                item,
                                index,
                                'needTime',
                              ),
                            )
                          : ''}
                      </div>
                    ) : (
                      ''
                    )}
                  </div>
                  <div className={styles.input_cotainer_phone}>
                    <div className={styles.input_cotainer_box}>
                      <div className={styles.input_content}>
                        <TextArea
                          ref={this.inputRef}
                          style={{
                            resize: 'none',
                            backgroundColor: 'transparent',
                            border: 'none',
                            fontSize: 12,
                            outline: 'none',
                          }}
                          className={styles.delOutline}
                          value={this.state.inputNoteValue}
                          placeholder={getIntl().formatMessage({
                            id: 'comment.feedback.replay.content.placeholder.2',
                            defaultMessage: 'Press Enter to send your message',
                          })}
                          autoSize={{
                            minRows: 1,
                            maxRows: 5,
                          }}
                          maxLength={1024}
                          onChange={e =>
                            this.handleSetInputNoteValue(e.target.value)
                          }
                          onKeyPress={e => this.handleNoteKeyPress(e)}
                        ></TextArea>

                        <div onClick={() => this.handleNoteKeyPress('send')}>
                          {SendIcon()}
                        </div>
                      </div>
                    </div>
                  </div>
                </React.Fragment>
              )}
            </div>
          ) : (
            //**********************************************************************/ 聊天的**********************************************************************/
            <div className={styles.chat_wrapper} id="chat_Box">
              {!this.props.currentSessionData.ticketId ? (
                <div className={styles.noDataWrapper}>
                  {/* <img src={NodataBg()} alt="" /> */}
                  {NodataBg()}
                  <span>
                    {getIntl().formatMessage({
                      id: 'im.chat.nodata',
                      defaultMessage:
                        'You currently have no unread messages...',
                    })}
                  </span>
                </div>
              ) : this.props.currentSessionData.ticketId ? (
                <React.Fragment>
                  <UserNav
                    deleteTicket={this.props.deleteTicket}
                    currentSessionData={this.props.currentSessionData}
                    handleFinish={this.handleFinish}
                    workTypeDetail={this.state.workTypeDetail}
                    handleNavBarPhone={this.handleNavBarPhone}
                    ticketId={this.props.currentSessionData.ticketId}
                    queryProcessingWorkOrDerListSchedule={
                      this.props.queryProcessingWorkOrDerListSchedule
                    }
                    isBeInputting={this.props.isBeInputting}
                  />
                  {/* 消息体 */}
                  <div
                    className={styles.messageWrapper}
                    ref={this.imContainerRefs}
                  >
                    {currentSessionData.messageList &&
                    currentSessionData.messageList.length > 0 ? (
                      <div
                        className={styles.messageContainer}
                        ref={this.imContainerRef}
                      >
                        {/* 会话消息列表 */}
                        {currentSessionData.messageList &&
                        currentSessionData.messageList.length > 0
                          ? currentSessionData.messageList.map((item, index) =>
                              this.handleViewByCondition(
                                item,
                                index,
                                '',
                                currentSessionData.messageList,
                              ),
                            )
                          : ''}
                        {/* 功能按钮 */}
                        {(this.state.isPopoverVisibleReference ||
                          this.state.isPopoverVisibleCopy ||
                          this.state.isPopoverVisibleWithdraw) && (
                          <div
                            className={styles.messageItem_wrapper_function}
                            ref={this.popoverRefAgent}
                            style={{
                              top: this.state.popoverPosition.top,
                              left: this.state.popoverPosition.left,
                              zIndex: 1000,
                            }}
                          >
                            {this.state.isPopoverVisibleReference && (
                              <div
                                className={
                                  styles.messageItem_wrapper_function_item
                                }
                                title={getIntl().formatMessage({
                                  id: 'workerOffers.references.message',
                                  defaultMessage: '引用',
                                })}
                                onClick={() => this.handleReferencesMessage()}
                              >
                                <ReferencesIcon />
                              </div>
                            )}
                            {this.state.isPopoverVisibleCopy && (
                              <div
                                className={
                                  styles.messageItem_wrapper_function_item
                                }
                                onClick={() => this.handleCopyMessage()}
                                title={getIntl().formatMessage({
                                  id: 'workerOffers.copy.message',
                                  defaultMessage: '复制',
                                })}
                              >
                                <CopyIcon />
                              </div>
                            )}
                            {this.state.isPopoverVisibleWithdraw && (
                              <div
                                className={
                                  styles.messageItem_wrapper_function_item
                                }
                                title={getIntl().formatMessage({
                                  id: 'workerOffers.withdraw.message',
                                  defaultMessage: '撤回',
                                })}
                                onClick={() => this.handleWithdrawMessage()}
                              >
                                <WithdrawIcon />
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className={styles.noDataWrapper}>
                        {/* <img src={NodataBg()} alt="" /> */}
                        {NodataBg()}
                        <span>
                          {getIntl().formatMessage({
                            id: 'im.chat.nodata',
                            defaultMessage:
                              'You currently have no unread messages...',
                          })}
                        </span>
                      </div>
                    )}
                  </div>
                  {/* workTableTabProcesOrPendValue：2==》未分配，1待处理,3机器人 */}
                  {+this.props.workTableTabProcesOrPendValue ===
                  3 ? null : +this.props.workTableTabProcesOrPendValue === 2 ? (
                    <div className={styles.footer_container}>
                      <div className={styles.acwContainer}>
                        <div className={styles.acwContainerBorder}>
                          {this.props.user.roleList?.[0].roleId === '1005' ? (
                            <FormattedMessage id="im.chat.allocation.ticket" />
                          ) : (
                            <FormattedMessage id="im.chat.collect.ticket" />
                          )}
                        </div>
                      </div>
                      <div
                        style={{
                          position: 'absolute',
                          top: '-50px',
                          right: 0,
                          display: 'flex',
                          flexDirection: 'column',
                        }}
                      >
                        <img
                          src={WorkerLayoutTopIcon}
                          alt=""
                          style={{ cursor: 'pointer' }}
                          onClick={() => this.scrollToTop()}
                        />
                        <img
                          src={WorkerLayoutBottomIcon}
                          alt=""
                          style={{ cursor: 'pointer' }}
                          onClick={() => this.scrollToBottom()}
                        />
                      </div>
                    </div>
                  ) : this.props.user.roleList?.[0].roleId === '1005' &&
                    !this.state.currentSessionData.isAdminJoinChat &&
                    +this.props.currentSessionData?.ticketStatus === 1 ? (
                    <div
                      className={styles.footer_container}
                      style={{ textAlign: 'center', paddingBottom: 20 }}
                    >
                      <Button
                        type="primary"
                        style={{
                          display: 'inline-flex',
                          padding: '5px 42px',
                          justifyContent: 'center',
                          alignItems: 'center',
                          gap: 10,
                          width: 'fit-content',
                          marginBottom: 20,
                          margin: 'auto',
                        }}
                        onClick={() => this.handleJoinChat()}
                      >
                        {getIntl().formatMessage({
                          id: 'im.chat.join.btn',
                        })}
                      </Button>
                      <div
                        style={{
                          position: 'absolute',
                          top: '-50px',
                          right: 0,
                          display: 'flex',
                          flexDirection: 'column',
                        }}
                      >
                        <img
                          src={WorkerLayoutTopIcon}
                          alt=""
                          style={{ cursor: 'pointer' }}
                          onClick={() => this.scrollToTop()}
                        />
                        <img
                          src={WorkerLayoutBottomIcon}
                          alt=""
                          style={{ cursor: 'pointer' }}
                          onClick={() => this.scrollToBottom()}
                        />
                      </div>
                    </div>
                  ) : (
                    <div className={styles.footer_container}>
                      <div
                        style={{
                          position: 'absolute',
                          top: '-50px',
                          right: 0,
                          display: 'flex',
                          flexDirection: 'column',
                        }}
                      >
                        <img
                          src={WorkerLayoutTopIcon}
                          alt=""
                          style={{ cursor: 'pointer' }}
                          onClick={() => this.scrollToTop()}
                        />
                        <img
                          src={WorkerLayoutBottomIcon}
                          alt=""
                          style={{ cursor: 'pointer' }}
                          onClick={() => this.scrollToBottom()}
                        />
                      </div>
                      {loadingSummaryModal && (
                        <div className={styles.loadingSummaryContainer}>
                          <span>
                            <FormattedMessage
                              id="work.order.detail.ai.intelligence.summary.loading"
                              defaultMessage="正在总结"
                            />
                          </span>
                          <div className={styles.spinner}>
                            <span></span>
                            <span></span>
                            <span></span>
                          </div>
                        </div>
                      )}
                      {loadingIntelligentFormFilling && (
                        <div
                          className={styles.loadingSummaryContainer}
                          style={{
                            bottom:
                              [3, 2].includes(
                                +this.props.currentSessionData?.ticketStatus,
                              ) && !socialMediaTimeStatus
                                ? '100px'
                                : '135px',
                          }}
                        >
                          <span>
                            <FormattedMessage id="intelligent.form.filling.title">
                              智能填单
                            </FormattedMessage>
                          </span>
                          <div className={styles.spinner}>
                            <span></span>
                            <span></span>
                            <span></span>
                          </div>
                        </div>
                      )}
                      {/*{Object.keys(ticketSmartFill).length > 0 && (*/}
                      {/*  <div className={styles.intelligentFormFillingContainer}>*/}
                      {/*    <div className={styles.titleContainer}>*/}
                      {/*      <FormattedMessage*/}
                      {/*        id="intelligent.form.filling.title"*/}
                      {/*        defaultMessage="智能填单"*/}
                      {/*      />*/}
                      {/*    </div>*/}
                      {/*    <Form*/}
                      {/*      name="basic"*/}
                      {/*      onFinish={this.onFinish}*/}
                      {/*      autoComplete="off"*/}
                      {/*      ref={this.smartFillRef}*/}
                      {/*    >*/}
                      {/*      {ticketSmartFill?.aigcTicketSmartFillList?.map(*/}
                      {/*        (itemData, itemIndex) => {*/}
                      {/*          return (*/}
                      {/*            <Form.Item*/}
                      {/*              key={itemIndex}*/}
                      {/*              label={itemData.attrName}*/}
                      {/*              name={[*/}
                      {/*                'aigcTicketSmartFillList',*/}
                      {/*                itemIndex,*/}
                      {/*                itemData.attrName,*/}
                      {/*              ]}*/}
                      {/*              rules={[*/}
                      {/*                {*/}
                      {/*                  required: false,*/}
                      {/*                  message: getIntl().formatMessage({*/}
                      {/*                    id: 'work.order.management.tips',*/}
                      {/*                    defaultValue: '请输入',*/}
                      {/*                  }),*/}
                      {/*                },*/}
                      {/*                {*/}
                      {/*                  max: 80,*/}
                      {/*                  message: (*/}
                      {/*                    <FormattedMessage*/}
                      {/*                      id="customerInformation.add.basicInformation.maxlength"*/}
                      {/*                      defaultValue="长度不能超过80个字符"*/}
                      {/*                    />*/}
                      {/*                  ),*/}
                      {/*                },*/}
                      {/*              ]}*/}
                      {/*            >*/}
                      {/*              <Input*/}
                      {/*                maxLength={81}*/}
                      {/*                placeholder={getIntl().formatMessage({*/}
                      {/*                  id: 'work.order.management.tips',*/}
                      {/*                  defaultValue: '请输入',*/}
                      {/*                })}*/}
                      {/*              />*/}
                      {/*            </Form.Item>*/}
                      {/*          );*/}
                      {/*        },*/}
                      {/*      )}*/}
                      {/*      <Form.Item style={{ textAlign: 'center' }}>*/}
                      {/*        <Button*/}
                      {/*          onClick={this.handleCancelSaveFormFill}*/}
                      {/*          style={{ marginRight: '10px' }}*/}
                      {/*        >*/}
                      {/*          <FormattedMessage*/}
                      {/*            id="awsAccountSetting.cancel.btn"*/}
                      {/*            defaultMessage="取消"*/}
                      {/*          />*/}
                      {/*        </Button>*/}
                      {/*        <Button*/}
                      {/*          type={'primary'}*/}
                      {/*          htmlType="submit"*/}
                      {/*          loading={this.state.loadingSave}*/}
                      {/*        >*/}
                      {/*          <FormattedMessage*/}
                      {/*            id="work.order.saved.btn"*/}
                      {/*            defaultMessage="保存"*/}
                      {/*          />*/}
                      {/*        </Button>*/}
                      {/*      </Form.Item>*/}
                      {/*    </Form>*/}
                      {/*  </div>*/}
                      {/*)}*/}
                      <div className={styles.shortcut_operate}>
                        <Tooltip
                          title={getIntl().formatMessage({
                            id: 'work.order.detail.ai.btn.translate',
                            defaultMessage: '翻译',
                          })}
                          style={{
                            color: 'rgba(0, 0, 0, 0.80)',
                            border: '1px solid #fff',
                          }}
                        >
                          <div
                            className={`${styles.translate_icon_style} ${
                              this.state.currentSessionData?.translateStatus
                                ? styles.checked
                                : ''
                            }`}
                            style={{
                              cursor: 'pointer',
                              opacity: ![2, 3].includes(
                                +this.props.currentSessionData.ticketStatus,
                              )
                                ? 1
                                : 0.3,
                            }}
                            onClick={e => this.handleTranslationClick(e)}
                          >
                            <div className={styles.filled}></div>
                            {!this.state.currentSessionData?.translateStatus
                              ? TransFerIcon(
                                  this.state.currentSessionData.translateStatus,
                                )
                              : CheckedIcon(
                                  this.state.currentSessionData
                                    ?.translateStatus,
                                )}
                          </div>
                        </Tooltip>
                        {/* 简化 */}
                        <Tooltip
                          title={getIntl().formatMessage({
                            id: 'work.order.detail.ai.btn.meihua',
                            defaultMessage: '优化',
                          })}
                          style={{
                            color: 'rgba(0, 0, 0, 0.80)',
                            border: '1px solid #fff',
                          }}
                        >
                          <div
                            className={`${styles.icon_style} ${
                              this.state.simplify ? styles.checked : ''
                            }`}
                            style={{
                              cursor: this.state.simplify
                                ? 'not-allowed'
                                : 'pointer',
                              opacity: ![2, 3].includes(
                                +this.props.currentSessionData.ticketStatus,
                              )
                                ? 1
                                : 0.3,
                            }}
                            onClick={() => this.handleSimplify()}
                          >
                            <div className={styles.filled}></div>
                            {this.state.simplify
                              ? SimplifyIconEd()
                              : SimplifyIcon()}
                          </div>
                        </Tooltip>
                        {/* 扩写 */}
                        <Tooltip
                          title={getIntl().formatMessage({
                            id: 'new.worktable.email.elaborate',
                            defaultMessage: '扩写',
                          })}
                          style={{
                            color: 'rgba(0, 0, 0, 0.80)',
                            border: '1px solid #fff',
                          }}
                        >
                          <div
                            className={`${styles.icon_style} ${
                              this.state.expand ? styles.checked : ''
                            }`}
                            style={{
                              cursor: this.state.expand
                                ? 'not-allowed'
                                : 'pointer',
                              opacity: ![2, 3].includes(
                                +this.props.currentSessionData.ticketStatus,
                              )
                                ? 1
                                : 0.3,
                            }}
                            onClick={() => this.handleExpand()}
                          >
                            <div className={styles.filled}></div>
                            {this.state.expand
                              ? ExpandIconChecked()
                              : ExpandIcon()}
                          </div>
                        </Tooltip>
                        {/* 缩写 */}
                        <Tooltip
                          title={getIntl().formatMessage({
                            id: 'new.worktable.email.abbreviation',
                            defaultMessage: '缩写',
                          })}
                          style={{
                            color: 'rgba(0, 0, 0, 0.80)',
                            border: '1px solid #fff',
                          }}
                        >
                          <div
                            className={`${styles.icon_style} ${
                              this.state.abbrev ? styles.checked : ''
                            }`}
                            style={{
                              cursor: this.state.abbrev
                                ? 'not-allowed'
                                : 'pointer',
                              opacity: ![2, 3].includes(
                                +this.props.currentSessionData.ticketStatus,
                              )
                                ? 1
                                : 0.3,
                            }}
                            onClick={() => this.handleAbbrev()}
                          >
                            <div className={styles.filled}></div>
                            {this.state.abbrev ? AbbrevChecked() : Abbrev()}
                          </div>
                        </Tooltip>
                        {/* 语法纠错 */}
                        <Popover
                          onOpenChange={open => this.openGrammer(open)}
                          open={this.state.grammerState}
                          content={
                            <div className="grammerOperationList">
                              <ul>
                                <li onClick={() => this.handleSelectGrammer(1)}>
                                  <span>{GrammarCorrectionIcon()}</span>
                                  <span>
                                    <FormattedMessage
                                      id="new.worktable.email.grammar.correction"
                                      defaultMessage="语法纠错"
                                    />
                                  </span>
                                </li>
                                <li onClick={() => this.handleSelectGrammer(2)}>
                                  <span>{GentleIcon()}</span>
                                  <span>
                                    <FormattedMessage
                                      id="new.worktable.email.gentle"
                                      defaultMessage="温柔"
                                    />
                                  </span>
                                </li>
                                <li onClick={() => this.handleSelectGrammer(3)}>
                                  <span>{NeutralityIcon()}</span>
                                  <span>
                                    <FormattedMessage
                                      id="new.worktable.email.neutrality"
                                      defaultMessage="中立"
                                    />
                                  </span>
                                </li>
                                <li onClick={() => this.handleSelectGrammer(4)}>
                                  <span>{FormalIcon()}</span>
                                  <span>
                                    <FormattedMessage
                                      id="new.worktable.email.formal"
                                      defaultMessage="正式"
                                    />
                                  </span>
                                </li>
                              </ul>
                            </div>
                          }
                          placement="topLeft"
                          overlayClassName="settingGrammerPopover"
                        >
                          <div
                            className={`${styles.icon_style1} ${
                              this.state.grammerState ? styles.checked : ''
                            }`}
                            style={{
                              cursor: 'pointer',
                              opacity: ![2, 3].includes(
                                +this.props.currentSessionData.ticketStatus,
                              )
                                ? 1
                                : 0.3,
                            }}
                          >
                            {this.state.grammerState
                              ? ChatGrammerSelected()
                              : ChatGrammer()}
                          </div>
                        </Popover>
                        {/* whatsApp */}
                        {this.props.currentSessionData.channelId == '4' && (
                          <Tooltip
                            title={getIntl().formatMessage({
                              id: 'new.worktable.whatsApp.title',
                              defaultMessage: 'whatsApp',
                            })}
                            style={{
                              color: 'rgba(0, 0, 0, 0.80)',
                              border: '1px solid #fff',
                            }}
                          >
                            <div
                              className={`${styles.icon_style} ${
                                this.state.whatsApp ? styles.checked : ''
                              }`}
                              style={{
                                cursor: this.state.whatsApp
                                  ? 'not-allowed'
                                  : 'pointer',
                              }}
                              onClick={() => this.handleWhatsApp()}
                            >
                              <div className={styles.filled}></div>
                              {this.state.whatsApp
                                ? WhatsappCheckIcon()
                                : WhatsappIcon()}
                            </div>
                          </Tooltip>
                        )}
                        {/* 快捷回复 */}
                        <Popover
                          onOpenChange={this.openQuickReply}
                          open={this.state.showQuickReply}
                          content={content}
                          placement="topLeft"
                          overlayClassName="settingTranslatePopover"
                        >
                          <div
                            className={`${styles.icon_style1} ${
                              this.state.showQuickReply ? styles.checked : ''
                            }`}
                            style={{
                              cursor: 'pointer',
                              opacity: ![2, 3].includes(
                                +this.props.currentSessionData.ticketStatus,
                              )
                                ? 1
                                : 0.3,
                            }}
                          >
                            {this.state.showQuickReply
                              ? BeautifyIconChecked()
                              : BeautifyIcon()}
                          </div>
                        </Popover>
                        <Tooltip
                          title={getIntl().formatMessage({
                            id:
                              'chatVoice.channel.configuration.chat3.evaluate',
                            defaultMessage: '满意度评价',
                          })}
                          style={{
                            color: 'rgba(0, 0, 0, 0.80)',
                            border: '1px solid #fff',
                          }}
                        >
                          <div
                            className={`${styles.icon_style} ${
                              this.state.currentSessionData
                                .invitationEvaluationStatus
                                ? styles.manyiduchecked
                                : ''
                            }`}
                            style={{
                              border: this.state.currentSessionData
                                .invitationEvaluationStatus
                                ? '#FCA534'
                                : '',
                              cursor: this.state.currentSessionData
                                .invitationEvaluationStatus
                                ? 'not-allowed'
                                : 'pointer',
                              opacity:
                                ['8', '9'].includes(
                                  this.state.currentSessionData.channelId,
                                ) &&
                                ![2, 3].includes(
                                  +this.props.currentSessionData.ticketStatus,
                                )
                                  ? 1
                                  : 0.3,
                            }}
                            onClick={() => this.handleInviteRatingsClick()}
                          >
                            <div className={styles.filled}></div>
                            {this.state.currentSessionData
                              .invitationEvaluationStatus
                              ? CollectIconChecked()
                              : CollectIcon()}
                          </div>
                        </Tooltip>

                        <Button
                          className={styles.ai}
                          loading={loadingSummaryModal}
                          icon={AiIcon()}
                          onClick={this.handleAiIntelligenceSummary}
                        >
                          <FormattedMessage id="new.worktable.chatLayout.AI.intelligence.summary">
                            AI智能总结
                          </FormattedMessage>
                        </Button>
                        <Button
                          className={styles.ai}
                          loading={loadingIntelligentFormFilling}
                          icon={AiIcon()}
                          onClick={
                            this.showIntelligentFormFillingSelectLanguage
                          }
                        >
                          <FormattedMessage id="intelligent.form.filling.title">
                            智能填单
                          </FormattedMessage>
                        </Button>
                      </div>

                      {/* 会话处于acw状态 */}
                      {/* {+this.props.currentSessionData?.ticketStatus === 2 &&
                      !socialMediaTimeStatus ? (
                        <div className={styles.acwContainer}>
                          <div>
                            <FormattedMessage
                              id="im.chat.acw.open"
                              values={{
                                acw: (
                                  <Tooltip
                                    color="#92a4ec"
                                    placement="top"
                                    title={getIntl().formatMessage({
                                      id: 'im.chat.acw.prompt',
                                    })}
                                  >
                                    <span>ACW </span>
                                  </Tooltip>
                                ),
                              }}
                            />
                          </div>
                          <div>
                            <div onClick={() => this.handleCloseChat()}>
                              <span>
                                {getIntl().formatMessage({
                                  id: 'im.chat.acw.close',
                                  defaultMessage: 'Close chat',
                                })}
                              </span>
                            </div>
                          </div>
                        </div>
                      ) : (
                        ''
                      )} */}
                      {/* 会话处于结束状态 */}
                      {[3, 2].includes(
                        +this.props.currentSessionData?.ticketStatus,
                      ) && !socialMediaTimeStatus ? (
                        <div className={styles.acwContainer}>
                          <div>
                            <FormattedMessage id="im.chat.acw.finish" />
                          </div>
                        </div>
                      ) : (
                        ''
                      )}
                      {/* 会话进行 */}
                      {+this.props.currentSessionData?.ticketStatus === 1 &&
                      !socialMediaTimeStatus ? (
                        <Spin spinning={this.state.inputLoading}>
                          <div className={styles.input_cotainer}>
                            <div className={styles.input_cotainer_box}>
                              <div className={styles.tab_box}>
                                <span
                                  className={
                                    activeTab === 1 ? styles.active : ''
                                  }
                                  onClick={() => this.handleTabClick(1)}
                                  style={{
                                    cursor: 'pointer',
                                  }}
                                >
                                  <FormattedMessage
                                    id="work.record.reply"
                                    defaultMessage="回复"
                                  />
                                </span>
                                <span>｜</span>
                                <span
                                  className={
                                    activeTab === 2 ? styles.active : ''
                                  }
                                  onClick={() => this.handleTabClick(2)}
                                  style={{
                                    cursor: 'pointer',
                                  }}
                                >
                                  <FormattedMessage
                                    id="comment.feedback.personal.remarks"
                                    defaultMessage="个人备注"
                                  />
                                </span>
                              </div>
                              <div className={styles.input_content}>
                                {+activeTab === 1 && (
                                  <div
                                    onClick={() => this.handleUploadIconClick()}
                                  >
                                    {LinkIcon()}
                                  </div>
                                )}
                                {/* {[2, 3].includes(+this.state.inputValueAType) &&
                                +activeTab === 1 ?  */}

                                {/* 输入框区域 */}
                                <AdvancedEditable
                                  customStyles={{
                                    resize: 'none',
                                    backgroundColor: 'transparent',
                                    border: 'none',
                                    fontSize: 12,
                                    outline: 'none',
                                    minHeight: '32px',
                                    padding: '7px 0px 4px',
                                    display: 'block',
                                    // alignItems: 'center',
                                    // justifyContent: 'flex-start',
                                    width: '100%',
                                    maxHeight: 200,
                                    overflowY: 'scroll',
                                  }}
                                  placeholder={
                                    activeTab === 2
                                      ? getIntl().formatMessage({
                                          id:
                                            'comment.feedback.replay.content.placeholder.2',
                                          defaultMessage:
                                            'Press Enter to send your message',
                                        })
                                      : getIntl().formatMessage({
                                          id:
                                            'comment.feedback.replay.content.placeholder',
                                          defaultMessage:
                                            'Press Enter to send your message',
                                        })
                                  }
                                  ref={this.inputRef}
                                  className={styles.delOutline}
                                  onInputCustom={e =>
                                    this.handleContentInput(e)
                                  }
                                  handleKeyPress={e => this.handleKeyPress(e)}
                                  IMHandleDown={() => this.IMHandleDown()}
                                />

                                {/* <TextArea
                                  ref={this.inputRef}
                                  style={{
                                    resize: 'none',
                                    backgroundColor: 'transparent',
                                    border: 'none',
                                    fontSize: 12,
                                    outline: 'none',
                                  }}
                                  className={styles.delOutline}
                                  value={this.state.inputValue}
                                  placeholder={
                                    activeTab === 2
                                      ? getIntl().formatMessage({
                                          id:
                                            'comment.feedback.replay.content.placeholder.2',
                                          defaultMessage:
                                            'Press Enter to send your message',
                                        })
                                      : getIntl().formatMessage({
                                          id:
                                            'comment.feedback.replay.content.placeholder',
                                          defaultMessage:
                                            'Press Enter to send your message',
                                        })
                                  }
                                  autoSize={{
                                    minRows: 1,
                                    maxRows: 5,
                                  }}
                                  maxLength={1024}
                                  onChange={e =>
                                    this.handleSetInputValue(e.target.value)
                                  }
                                  onKeyPress={e => this.handleKeyPress(e)}
                                ></TextArea> */}
                                {/* null : (
                                   <TextArea
                                //     ref={this.inputRef}
                                //     style={{
                                //       resize: 'none',
                                //       backgroundColor: 'transparent',
                                //       border: 'none',
                                //       fontSize: 12,
                                //       outline: 'none',
                                //     }}
                                //     className={styles.delOutline}
                                //     value={this.state.inputValue}
                                //     placeholder={
                                //       activeTab === 2
                                //         ? getIntl().formatMessage({
                                //             id:
                                //               'comment.feedback.replay.content.placeholder.2',
                                //             defaultMessage:
                                //               'Press Enter to send your message',
                                //           })
                                //         : getIntl().formatMessage({
                                //             id:
                                //               'comment.feedback.replay.content.placeholder',
                                //             defaultMessage:
                                //               'Press Enter to send your message',
                                //           })
                                //     }
                                //     autoSize={{
                                //       minRows: 1,
                                //       maxRows: 5,
                                //     }}
                                //     maxLength={1024}
                                //     onChange={e =>
                                //       this.handleSetInputValue(e.target.value)
                                //     }
                                //     onKeyPress={e => this.handleKeyPress(e)}
                                 ></TextArea>
                                 )} */}
                                {this.state.showUndo && (
                                  <ReloadOutlined
                                    style={{
                                      cursor: 'pointer',
                                      fontSize: 18,
                                      color: '#999',
                                    }}
                                    onClick={() => this.handleUndoClick()}
                                  />
                                )}
                                {+activeTab === 1 && (
                                  <div
                                    style={{ marginLeft: 4, cursor: 'pointer' }}
                                    onClick={() => this.handleEmojiIconClick()}
                                  >
                                    {SmileIcon()}
                                  </div>
                                )}
                                {this.state.emojiModal && (
                                  <EmojiPicker
                                    handleCloseEmojiModalClick={
                                      this.handleCloseEmojiModalClick
                                    }
                                    handleEmojiClick={this.handleEmojiClick}
                                  />
                                )}
                                <div
                                  style={{ cursor: 'pointer' }}
                                  onClick={() =>
                                    this.handleSendMessage(
                                      this.state.inputValue,
                                    )
                                  }
                                >
                                  {SendIcon()}
                                </div>
                              </div>
                              {this.state.isReferencesMessage && (
                                <div
                                  style={{
                                    borderRadius: 4,
                                    background: '#FFF',
                                    width: '100%',
                                    display: 'flex',
                                    padding: 4,
                                    alignItems: 'center',
                                    alignSelf: 'stretch',
                                    gap: 10,
                                  }}
                                >
                                  <div
                                    style={{
                                      width: '100%',
                                      maxHeight: 100,
                                      overflowY: 'scroll',
                                      display: 'flex',
                                    }}
                                  >
                                    {
                                      this.state.referencesMessageContent
                                        ?.username
                                    }
                                    :&nbsp;
                                    {['2', 'image'].includes(
                                      String(
                                        this.state.referencesMessageContent
                                          ?.contentType,
                                      ),
                                    ) ? (
                                      <img
                                        src={
                                          this.state.referencesMessageContent
                                            ?.filePath ||
                                          this.state.referencesMessageContent
                                            ?.content
                                        }
                                        style={{
                                          width: 50,
                                          height: 'auto',
                                        }}
                                      ></img>
                                    ) : ['3', 'video'].includes(
                                        String(
                                          this.state.referencesMessageContent
                                            ?.contentType,
                                        ),
                                      ) ? (
                                      <video
                                        src={
                                          this.state.referencesMessageContent
                                            ?.filePath ||
                                          this.state.referencesMessageContent
                                            ?.content
                                        }
                                        style={{
                                          width: 200,
                                          height: 'auto',
                                        }}
                                        controls
                                      />
                                    ) : ['5', 'music', 'audio'].includes(
                                        String(
                                          this.state.referencesMessageContent
                                            ?.contentType,
                                        ),
                                      ) ? (
                                      <AudioPlayer
                                        src={
                                          this.state.referencesMessageContent
                                            ?.filePath ||
                                          this.state.referencesMessageContent
                                            ?.content
                                        }
                                        id={
                                          this.state.referencesMessageContent
                                            ?.referenceContentId
                                        }
                                      />
                                    ) : ['4', 'link'].includes(
                                        String(
                                          this.state.referencesMessageContent
                                            ?.contentType,
                                        ),
                                      ) ? (
                                      <a
                                        href={
                                          this.state.referencesMessageContent
                                            ?.filePath ||
                                          this.state.referencesMessageContent
                                            ?.content
                                        }
                                        style={{
                                          overflowWrap: 'break-word',
                                          wordBreak: 'break-all',
                                        }}
                                      >
                                        {
                                          this.state.referencesMessageContent
                                            ?.fileName
                                        }
                                      </a>
                                    ) : (
                                      this.state.referencesMessageContent
                                        ?.content
                                    )}
                                  </div>
                                  <div
                                    style={{ cursor: 'pointer', height: 12 }}
                                    onClick={() =>
                                      this.setState({
                                        isReferencesMessage: false,
                                        referencesMessageContent: {},
                                        popoverPosition: {
                                          top: 0,
                                          left: 0,
                                        },
                                      })
                                    }
                                  >
                                    <ReferencesCloseIcon />
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        </Spin>
                      ) : (
                        ''
                      )}
                      {/* 会话超时状态 */}
                      {socialMediaTimeStatus
                        ? (() => {
                            switch (+this.state.currentSessionData.channelId) {
                              case 4:
                                return (
                                  <div className={styles.acwContainer}>
                                    <div>
                                      <FormattedMessage id="im.chat.acw.timeout" />
                                    </div>
                                  </div>
                                );
                              case 15:
                              case 16:
                                return (
                                  <div className={styles.acwContainer}>
                                    <div>
                                      <FormattedMessage
                                        id="im.chat.acw.timeout.public"
                                        values={{
                                          time: 48,
                                        }}
                                      />
                                    </div>
                                  </div>
                                );
                              default:
                                return (
                                  <div className={styles.acwContainer}>
                                    <div>
                                      <FormattedMessage
                                        id="im.chat.acw.timeout.public"
                                        values={{
                                          time: 24,
                                        }}
                                      />
                                    </div>
                                  </div>
                                );
                            }
                          })()
                        : ''}

                      {/* 弹出快捷回复 */}
                      {replyModle && replyMessageModleData.length > 0 ? (
                        <div>
                          <div
                            onClick={this.handleCloseReplyModel}
                            style={{
                              position: 'fixed',
                              top: 0,
                              left: 0,
                              bottom: '10px',
                              width: '100%',
                              zIndex: 99,
                            }}
                          ></div>
                          <ShhowReplyModle
                            handleMouseInterUpdateInput={
                              this.handleMouseInterUpdateInput
                            }
                            inputValue={this.state.inputValue}
                            replyMessageModleData={replyMessageModleData}
                          ></ShhowReplyModle>
                        </div>
                      ) : (
                        ''
                      )}
                    </div>
                  )}
                </React.Fragment>
              ) : (
                ''
              )}

              {/* 翻译 */}
              {this.state.isTranslationExpanded ? (
                <div>
                  <div
                    onClick={e => this.handleCloseTranslate(e)}
                    style={{
                      position: 'fixed',
                      top: '10px',
                      left: 0,
                      bottom: 0,
                      width: '100%',
                      zIndex: '100',
                    }}
                  ></div>
                  <div
                    className={`${styles.translationContainer} ${
                      this.state.isTranslationExpanded
                        ? styles.incomingCallBigInBr
                        : styles.incomingCallBigOutBr
                    }`}
                  >
                    <div>
                      <span>
                        {getIntl().formatMessage({
                          id: 'im.chat.translation.title',
                          defaultMessage: 'Auto Translation',
                        })}
                      </span>
                      <span onClick={e => this.handleTranslationClick(e)}>
                        {IncomingCallCloseIcon()}
                      </span>
                    </div>
                    <div>
                      <Checkbox
                        checked={this.state.currentSessionData.translateStatus}
                        onChange={e => this.handleTranslationChange(e)}
                      >
                        <span style={{ color: '#333' }}>
                          {getIntl().formatMessage({
                            id: 'im.chat.translation.content',
                            defaultMessage: 'Enable Auto Translation',
                          })}
                        </span>
                      </Checkbox>
                    </div>
                    <div>
                      <Select
                        showSearch
                        placeholder="Search to Select"
                        // optionFilterProp="label"
                        value={this.state.currentSessionData.translateCode}
                        onChange={e => this.handleTranslationCodeChange(e)}
                        // filterSort={(optionA, optionB) =>
                        //   (optionA?.label ?? '')
                        //     .toLowerCase()
                        //     .localeCompare((optionB?.label ?? '').toLowerCase())
                        // }
                        filterOption={(inputValue, option) =>
                          option.label
                            .toLowerCase()
                            .indexOf(inputValue.toLowerCase()) >= 0
                        }
                        options={this.state.translationCodeList}
                      />
                    </div>
                  </div>
                </div>
              ) : (
                ''
              )}
            </div>
          )}
        </Spin>

        <Modal
          title={getIntl().formatMessage({
            id: 'phone.header.select.language',
            defaultValue: '选择语言',
          })}
          open={selectLanguageModal}
          className="selectIntelligentFormFillingLanguageModal"
          onOk={this.handleIntelligentFormFilling}
          onCancel={this.handleCancelIntelligentFormFilling}
          maskClosable={false}
          mask={false}
        >
          <Select
            style={{ width: '90%', marginBottom: '10px' }}
            showSearch
            placeholder={getIntl().formatMessage({
              id: 'home.set.language.select',
              defaultValue: '请选择语言',
            })}
            value={lang}
            onChange={e => this.handleChangeLanguage(e)}
            filterOption={(inputValue, option) =>
              option.label.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
            }
            options={filteredLanguages?.map(item => ({
              label: item.label,
              value: item.value,
              key: item.value,
            }))}
          />
        </Modal>

        {/*whatsApp*/}
        <Modal
          title={getIntl().formatMessage({
            id: 'new.worktable.whatsApp.title',
            defaultValue: 'whatsApp',
          })}
          className="WorkOrderUpgrade2"
          footer={null}
          mask={false}
          open={this.state.whatsApp}
          onCancel={() => {
            this.setState({ whatsApp: false });
          }}
        >
          <Spin spinning={this.state.whatsAppLoading}>
            <Row>
              <Col span={10}>
                <Select
                  placeholder={getIntl().formatMessage({
                    id: 'input.select',
                    defaultValue: '请选择',
                  })}
                  options={[
                    { label: 'marketing', value: 'MARKETING' },
                    { label: 'utility', value: 'UTILITY' },
                    { label: 'authentication', value: 'AUTHENTICATION' },
                  ]}
                  onChange={value => this.handleAddSelectWhatsApp(value)}
                  value={this.state.whatsAppType}
                  allowClear
                />
              </Col>
              <Col span={13}>
                <Input
                  placeholder={getIntl().formatMessage({
                    id: 'document.knowledge.base.input.tips',
                  })}
                  value={this.state.whatsAppInput}
                  ref={this.questionInput}
                  prefix={<Search />}
                  style={{ width: '100%', height: 32, marginLeft: 20 }}
                  onChange={this.onChangeInputWhatsApp}
                  onPressEnter={this.onPressEnterWhatsApp}
                />
              </Col>
            </Row>
            <Row>
              {this.state.whatsAppMessageList?.map((item, index) => {
                return (
                  <div
                    className={`whatsAppMessageItem ${
                      this.state.whatsAppMessageItem?.templateUuid ===
                      item.templateUuid
                        ? 'whatsAppMessageItemHover'
                        : ''
                    }`}
                    onClick={() => this.handleClickWhatsAppMessage(item)}
                  >
                    {item.templateContentList?.map((itemSon, indexSon) => {
                      if (itemSon.type === 'BODY') {
                        return (
                          <ReactMarkdown
                            components={{
                              p: ({ node }) => (
                                <p>
                                  {this.processMarkdownText(
                                    node,
                                    false,
                                    null,
                                    'BODY',
                                  )}
                                </p>
                              ),
                              em: ({ node }) => (
                                <em>
                                  {this.processMarkdownText(
                                    node,
                                    false,
                                    null,
                                    'BODY',
                                  )}
                                </em>
                              ),
                              strong: ({ node }) => (
                                <strong>
                                  {this.processMarkdownText(
                                    node,
                                    false,
                                    null,
                                    'BODY',
                                  )}
                                </strong>
                              ),
                              li: ({ node }) => (
                                <li>
                                  {this.processMarkdownText(
                                    node,
                                    false,
                                    null,
                                    'BODY',
                                  )}
                                </li>
                              ),
                            }}
                          >
                            {itemSon.text}
                          </ReactMarkdown>
                        );
                      }
                    })}
                    <div style={{ color: '#999' }}>
                      {getIntl().formatMessage({
                        id: 'new.worktable.whatsApp.templateType',
                        defaultValue: '模版类型',
                      })}
                      &nbsp;: &nbsp;
                      <span
                        style={{
                          color: '#3463fc',
                          borderRadius: 4,
                          background: 'rgba(52, 99, 252, 0.15)',
                          padding: '2px',
                        }}
                      >
                        {item.templateType}
                      </span>
                    </div>
                  </div>
                );
              })}
            </Row>
            {/* <Row style={{ textAlign: 'center', marginTop: 20 }}>
              <Col span={24} style={{ marginBottom: 20 }}>
                <Button
                  onClick={() => {
                    this.setState({
                      whatsAppType: null,
                      whatsAppInput: '',
                      whatsApp: false,
                    });
                  }}
                >
                  <FormattedMessage
                    id="work.order.management.btn.cancel"
                    defaultMessage="取消"
                  />
                </Button>
                <Button type="primary" onClick={this.handleWhatsAppConfirm}>
                  <FormattedMessage
                    id="user.management.btn.confirm"
                    defaultMessage="确认"
                  />
                </Button>
              </Col>
            </Row> */}
          </Spin>
        </Modal>
        {/*whatsApp模版参数*/}
        <Modal
          title={getIntl().formatMessage({
            id: 'document.knowledge.base.table.operation.preview',
            defaultValue: '预览',
          })}
          className="WorkOrderWhatsApp"
          footer={null}
          mask={false}
          open={this.state.templateModal}
          onCancel={() => {
            this.setState({ templateModal: false });
          }}
        >
          <WhatsAppMessageCard
            whatsAppMessageItem={this.state.whatsAppMessageItem}
            processMarkdownText={this.processMarkdownText}
          ></WhatsAppMessageCard>
          {Object.keys(this.state.whatsAppParameters)?.length > 0 &&
            Object.keys(this.state.whatsAppParameters)?.some(
              itemParam =>
                Object.keys(this.state.whatsAppParameters[itemParam])?.length >
                0,
            ) && (
              <Row style={{ textAlign: 'center', marginTop: 20 }}>
                <p style={{ marginBottom: 10, fontSize: 12 }}>
                  <FormattedMessage
                    id="new.worktable.whatsApp.templateType.edit"
                    defaultMessage="输入变量值"
                  />
                </p>
                {Object.keys(this.state.whatsAppParameters)?.map(
                  (itemParam, indexParam) => {
                    return Object.keys(
                      this.state.whatsAppParameters[itemParam],
                    )?.map((itemParamSon, indexParamSon) => {
                      return (
                        <Row gutter={12} style={{ marginBottom: 10 }}>
                          <Col span={6}>
                            <Input
                              disabled
                              defaultValue={`{{${itemParamSon}}}`}
                            />
                          </Col>
                          <Col span={18}>
                            <Input
                              placeholder={getIntl().formatMessage({
                                id: 'new.worktable.whatsApp.templateType.edit',
                                defaultValue: '输入变量值',
                              })}
                              value={
                                this.state.whatsAppParameters[itemParam][
                                  itemParamSon
                                ]
                              }
                              onChange={e => {
                                this.setState({
                                  whatsAppParameters: {
                                    ...this.state.whatsAppParameters,
                                    [itemParam]: {
                                      ...this.state.whatsAppParameters[
                                        itemParam
                                      ],
                                      [itemParamSon]: e.target.value,
                                    },
                                  },
                                });
                              }}
                            />
                          </Col>
                        </Row>
                      );
                    });
                  },
                )}
              </Row>
            )}
          <Row style={{ textAlign: 'center', marginTop: 20 }}>
            <Col span={24} style={{ marginBottom: 20 }}>
              <Button
                onClick={() => {
                  this.setState({
                    templateModal: false,
                  });
                }}
              >
                <FormattedMessage
                  id="work.order.management.btn.cancel"
                  defaultMessage="取消"
                />
              </Button>
              <Button
                type="primary"
                loading={this.state.loadingModal}
                onClick={this.handleSendWhatsApp}
              >
                <FormattedMessage
                  id="customerInformation.contactCustomer.button.send"
                  defaultMessage="发送"
                />
              </Button>
            </Col>
          </Row>
        </Modal>
      </div>
    );
  }
}
const WhatsAppMessageCard = ({
  whatsAppMessageItem,
  processMarkdownText,
  whatsAppParameters,
}) => {
  console.log(whatsAppMessageItem, whatsAppParameters, 'whatsAppMessageItem');
  return (
    <Card
      hoverable
      style={{ width: 240 }}
      cover={
        whatsAppMessageItem?.HEADER?.text ? (
          whatsAppMessageItem?.HEADER?.format === 'IMAGE' ? (
            <img alt="example" src={whatsAppMessageItem?.HEADER?.text} />
          ) : whatsAppMessageItem?.HEADER?.format === 'TEXT' ? (
            <ReactMarkdown
              components={{
                p: ({ node }) => (
                  <p>
                    {processMarkdownText(
                      node,
                      true,
                      whatsAppParameters,
                      'HEADER',
                    )}
                  </p>
                ),
                em: ({ node }) => (
                  <em>
                    {processMarkdownText(
                      node,
                      true,
                      whatsAppParameters,
                      'HEADER',
                    )}
                  </em>
                ),
                strong: ({ node }) => (
                  <strong>
                    {processMarkdownText(
                      node,
                      true,
                      whatsAppParameters,
                      'HEADER',
                    )}
                  </strong>
                ),
                li: ({ node }) => (
                  <li>
                    {processMarkdownText(
                      node,
                      true,
                      whatsAppParameters,
                      'HEADER',
                    )}
                  </li>
                ),
              }}
            >
              {whatsAppMessageItem?.HEADER?.text}
            </ReactMarkdown>
          ) : whatsAppMessageItem?.HEADER?.format === 'VIDEO' ? (
            <video alt="example" src={whatsAppMessageItem?.HEADER?.text} />
          ) : whatsAppMessageItem?.HEADER?.format === 'DOCUMENT' ? (
            <div
              style={{
                width: 169,
                height: 50,
                borderRadius: 4,
                border: '1px solid #E6E6E6',
                background: 'rgba(255, 255, 255, 0.50)',
                padding: 10,
                display: 'flex',
                alignItems: 'center',
              }}
            >
              <FileFrame />
              <span
                style={{
                  width: '80%',
                  overflow: 'hidden',
                  fontSize: 12,
                  whiteSpace: 'nowrap',
                  textOverflow: 'ellipsis',
                }}
                href={whatsAppMessageItem?.HEADER?.text}
                target="_blank"
              >
                {whatsAppMessageItem?.HEADER?.text}
              </span>
            </div>
          ) : (
            <></>
          )
        ) : null
      }
    >
      <div className="whatsAppMessageItemBody">
        <ReactMarkdown
          components={{
            p: ({ node }) => (
              <p>
                {processMarkdownText(node, true, whatsAppParameters, 'BODY')}
              </p>
            ),
            em: ({ node }) => (
              <em>
                {processMarkdownText(node, true, whatsAppParameters, 'BODY')}
              </em>
            ),
            strong: ({ node }) => (
              <strong>
                {processMarkdownText(node, true, whatsAppParameters, 'BODY')}
              </strong>
            ),
            li: ({ node }) => (
              <li>
                {processMarkdownText(node, true, whatsAppParameters, 'BODY')}
              </li>
            ),
          }}
        >
          {whatsAppMessageItem?.BODY?.text}
        </ReactMarkdown>
        <span>
          <ReactMarkdown
            components={{
              p: ({ node }) => (
                <p>
                  {processMarkdownText(
                    node,
                    true,
                    whatsAppParameters,
                    'FOOTER',
                  )}
                </p>
              ),
              em: ({ node }) => (
                <em>
                  {processMarkdownText(
                    node,
                    true,
                    whatsAppParameters,
                    'FOOTER',
                  )}
                </em>
              ),
              strong: ({ node }) => (
                <strong>
                  {processMarkdownText(
                    node,
                    true,
                    whatsAppParameters,
                    'FOOTER',
                  )}
                </strong>
              ),
              li: ({ node }) => (
                <li>
                  {processMarkdownText(
                    node,
                    true,
                    whatsAppParameters,
                    'FOOTER',
                  )}
                </li>
              ),
            }}
          >
            {whatsAppMessageItem?.FOOTER?.text}
          </ReactMarkdown>
        </span>
      </div>
      {whatsAppMessageItem?.BUTTONS?.buttons &&
        JSON.parse(whatsAppMessageItem?.BUTTONS?.buttons)?.map(
          (item, index) => {
            return (
              <div className="whatsAppMessageItemBodyBtn">
                {item.type === 'URL' ? (
                  <ExportOutlined />
                ) : item.type === 'QUICK_REPLY' ? (
                  <svg
                    width="12"
                    height="12"
                    viewBox="0 0 200 200"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M81.1587 62.5366V18.8293L3.90293 85.5953C-1.31073 90.1034 -1.29199 97.4213 3.90918 101.917L81.1587 168.683V124.976C124.866 124.976 165.451 134.341 199.793 181.171C184.183 93.7561 118.622 62.5366 81.1587 62.5366Z"
                      fill="#3463FC"
                    />
                  </svg>
                ) : item.type === 'MPM' ? (
                  <PhoneOutlined />
                ) : item.type === 'COPY_CODE' ? (
                  <CopyOutlined />
                ) : (
                  ''
                )}
                <span>{item.text}</span>
              </div>
            );
          },
        )}
    </Card>
  );
};
const EmojiPicker = props => {
  return (
    <div
      className={styles.emojiContainer}
      onClick={e => props.handleCloseEmojiModalClick(e)}
    >
      <div className={styles.emojiWrapper}>
        {emojiData.map((item, index) => (
          <span onClick={() => props.handleEmojiClick(item)} key={index}>
            {item}
          </span>
        ))}
      </div>
    </div>
  );
};

const mapStateToProps = ({
  worktable,
  workOrderCenter,
  layouts,
  intelligentFormFilling,
}) => {
  return {
    ...worktable,
    ...workOrderCenter,
    ...layouts,
    ...intelligentFormFilling,
    authAccess: layouts.auth,
  };
};
export default umiConnect(mapStateToProps)(ChatLayout);
