import React, { useState, useRef, useEffect } from 'react';
import {
  But<PERSON>,
  DatePicker,
  Input,
  notification,
  Popover,
  Radio,
  Select,
  Spin,
  Table,
  Tabs,
} from 'antd';
import styles from './index.less';
import { useDispatch, getIntl, FormattedMessage } from 'umi';
import AreaChart from './AreaChart';
import NoDataImg from '../../../assets/no-data-img.jpg';
import RoseChart from './RoseChart';
import { NumBalanceIcon, RightArrowIcon } from '../overView/icon';
import { formatThousand } from '../../../utils/utils';
import { NumIcon } from './icon';
const { RangePicker } = DatePicker;

const TelephoneExpenses = () => {
  const dispatch = useDispatch();
  const editorRef = useRef(null);
  const [tabKey, setTabKey] = useState('1');

  useEffect(() => {}, []);

  // 切换tab
  const onChangeTab = key => {
    setTabKey(key);
  };

  return (
    <div className={styles.telephoneExpensesContainer}>
      <p className="blueBorder">
        <FormattedMessage id="metering.billing.5" defaultValue="话务费" />
      </p>
      <Tabs
        activeKey={tabKey}
        onChange={onChangeTab}
        items={[
          {
            label: getIntl().formatMessage({
              id: 'external.intelligent.agent.AIGC.package.data.analysis',
              defaultValue: '数据分析',
            }),
            key: '1',
            children: <DataAnalysisContent tabKey={tabKey} />,
          },
          {
            label: getIntl().formatMessage({
              id: 'telephone.expenses.tab.1',
              defaultValue: '充值记录',
            }),
            key: '2',
            children: <RechargeRecordContent tabKey={tabKey} />,
          },
        ]}
      />
    </div>
  );
};

// 数据分析
const DataAnalysisContent = props => {
  const dispatch = useDispatch();
  const [spinning, setSpinning] = useState(false);
  const [spinning1, setSpinning1] = useState(false);
  const [spinning2, setSpinning2] = useState(false);
  const [timeList, setTimeList] = useState([
    {
      value: 1,
      label: getIntl().formatMessage({
        id: 'external.intelligent.agent.AIGC.package.select.time.one.month',
        defaultValue: '近一个月',
      }),
    },
    {
      value: 2,
      label: getIntl().formatMessage({
        id: 'external.intelligent.agent.AIGC.package.select.time.six.month',
        defaultValue: '近六个月',
      }),
    },
    {
      value: 3,
      label: getIntl().formatMessage({
        id: 'external.intelligent.agent.AIGC.package.select.time.one.year',
        defaultValue: '近一年',
      }),
    },
    {
      value: 4,
      label: getIntl().formatMessage({
        id: 'external.intelligent.agent.AIGC.package.select.time.customize',
        defaultValue: '自定义',
      }),
    },
  ]);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [selectTime, setSelectTime] = useState(false);
  // 使用 useState 来存储日期范围
  const [dates, setDates] = useState(null);
  //时间单位 1-日 2-周 3-月 4-时
  const [timeDimension, setTimeDimension] = useState('2');
  const [timeDifferenceInHours, setTimeDifferenceInHours] = useState('');
  // 时间类型
  const [timeRangeCode, setTimeRangeCode] = useState(1);
  const [dataSource, setDataSource] = useState([]); //调用次数明细
  const [pageNum, setPageNum] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [outerAgentTrendData, setOuterAgentTrendData] = useState([]); //话务费趋势
  const [outerAgentPercentData, setOuterAgentPercentData] = useState([]); //话务费使用占比
  const [countryRegionList, setCountryRegionList] = useState([]); //国家地区列表
  const [chargeTypeList, setChargeTypeList] = useState([]); //充值类型列表
  const [phoneNumList, setPhoneNumList] = useState([]); //号码列表
  const [phoneExpensesDetail, setPhoneExpensesDetail] = useState([]); //号码话务费明细
  const [telephonePrefix, setTelephonePrefix] = useState(''); //国家代码区号
  const [countryCode, setCountryCode] = useState(''); //国家代码
  const [chargeTypeCode, setChargeTypeCode] = useState([]); //收费类型代码
  const [systemPhoneNum, setSystemPhoneNum] = useState([]); //系统电话号码
  const colorList = [
    '#3463FC',
    '#AD30E5',
    '#FCB830',
    '#722ED1',
    '#4FCCFF',
    '#6FE621',
  ];

  useEffect(() => {
    setTimeRangeCode(1);
    setStartDate('');
    setEndDate('');
    setDates(null);
    setTimeDimension('2');
    setCountryCode('');
    setTelephonePrefix('');
    setChargeTypeCode([]);
    setSystemPhoneNum([]);
    if (props.tabKey === '1') {
      queryCountryRegionList();
      queryChargeTypeList();
      queryPhoneNumList();
    }
  }, [props.tabKey]);

  useEffect(() => {
    if (!selectTime) {
      const startDateTime = new Date(startDate);
      const endDateTime = new Date(endDate);
      const timeDifferenceInMilliseconds = endDateTime - startDateTime;
      const timeDifferenceInHours1 =
        timeDifferenceInMilliseconds / (1000 * 60 * 60);
      setTimeDifferenceInHours(timeDifferenceInHours1);
    }
  }, [selectTime, endDate]);

  useEffect(() => {
    if (props.tabKey === '1') {
      queryPhoneExpensesTrend();
    }
  }, [timeRangeCode, timeDimension, startDate, endDate, props.tabKey]);
  useEffect(() => {
    if (props.tabKey === '1') {
      queryPhoneExpensesPercent();
    }
  }, [timeRangeCode, startDate, endDate, props.tabKey]);

  useEffect(() => {
    if (props.tabKey === '1') {
      queryPhoneExpensesDetail();
    }
  }, [
    props.tabKey,
    timeRangeCode,
    startDate,
    endDate,
    countryCode,
    chargeTypeCode,
    systemPhoneNum,
    pageNum,
    pageSize,
  ]);

  // 查询话务费趋势
  const queryPhoneExpensesTrend = () => {
    setSpinning(true);
    let params = {
      timeRangeCode,
      timeDimension,
      startDate,
      endDate,
    };
    dispatch({
      type: 'meteringBilling/queryPhoneExpensesTrend',
      payload: params,
      callback: response => {
        setSpinning(false);
        if (response.code === 200) {
          if (response.data) {
            setOuterAgentTrendData(response.data);
          } else {
            setOuterAgentTrendData([]);
          }
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询话务费使用占比
  const queryPhoneExpensesPercent = () => {
    setSpinning1(true);
    let params = {
      timeRangeCode,
      timeDimension,
      startDate,
      endDate,
    };
    dispatch({
      type: 'meteringBilling/queryPhoneExpensesPercent',
      payload: params,
      callback: response => {
        setSpinning1(false);
        if (response.code === 200) {
          setOuterAgentPercentData(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询国家地区列表
  const queryCountryRegionList = () => {
    dispatch({
      type: 'meteringBilling/queryCountryRegionList',
      callback: response => {
        if (response.code === 200) {
          setCountryRegionList(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询充值类型列表
  const queryChargeTypeList = () => {
    dispatch({
      type: 'meteringBilling/queryChargeTypeList',
      callback: response => {
        if (response.code === 200) {
          setChargeTypeList(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询号码列表
  const queryPhoneNumList = () => {
    dispatch({
      type: 'meteringBilling/queryPhoneNumList',
      callback: response => {
        if (response.code === 200) {
          if (response.data?.length > 0) {
            const selectOptions = response.data.map(phone => ({
              label: phone,
              value: phone,
            }));
            setPhoneNumList(selectOptions);
          }
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询号码话务费明细
  const queryPhoneExpensesDetail = () => {
    setSpinning2(true);
    let params = {
      timeRangeCode: timeRangeCode,
      startDate,
      endDate,
      telephonePrefix: telephonePrefix,
      countryCode: countryCode,
      chargeTypeCode,
      telephone: systemPhoneNum,
      pageNum: pageNum,
      pageSize: pageSize,
    };
    dispatch({
      type: 'meteringBilling/queryPhoneExpensesDetail',
      payload: params,
      callback: response => {
        setSpinning2(false);
        if (response.code === 200) {
          setPhoneExpensesDetail(response.data?.records);
          setTotal(response.data?.total);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 选择国家
  const handleChangeCountry = (value, option) => {
    if (value) {
      setCountryCode(option.countryCode);
      setTelephonePrefix(value);
      setPageNum(1);
    } else {
      setCountryCode('');
      setTelephonePrefix('');
      setPageNum(1);
    }
  };
  // 选择收费类型
  const handleChangeChargeType = value => {
    setChargeTypeCode(value);
    setPageNum(1);
  };
  // 选择系统电话号码
  const handleChangeSystemPhoneNum = value => {
    setSystemPhoneNum(value);
    setPageNum(1);
  };
  // 选择时间类型
  const handleChange = value => {
    if (value === 4) {
      setTimeRangeCode(value);
      setTimeDimension('2');
      setPageNum(1);
    } else {
      setTimeRangeCode(value);
      setStartDate('');
      setEndDate('');
      setDates(null);
      setTimeDimension('2');
    }
  };
  // 切换时间范围
  const rangePickerChange = (value, dateString) => {
    setStartDate(dateString[0]);
    setEndDate(dateString[1]);
    setDates(value);
    setPageNum(1);
  };
  const onOpenChange = open => {
    setSelectTime(open);
  };
  // 切换时、日、周、月
  const onChangeChart = ({ target: { value } }) => {
    setTimeDimension(value);
  };

  // 选择时间范围小于24小时提示
  const hourContent = (
    <div>
      <p>
        <FormattedMessage
          id="work.efficiency.statistics.select.time.tips.text"
          defaultMessage="选择时间范围需小于24小时"
        />
      </p>
    </div>
  );

  const columns = [
    {
      title: getIntl().formatMessage({
        id: 'external.intelligent.agent.AIGC.package.table.date',
        defaultValue: '日期',
      }),
      dataIndex: 'date',
      key: 'date',
    },
    {
      title: getIntl().formatMessage({
        id: 'customerInformation.table.nation',
        defaultValue: '国家',
      }),
      dataIndex: 'country',
      key: 'country',
    },
    {
      title: getIntl().formatMessage({
        id: 'phone.robot.package.table.1.system',
        defaultValue: '电话号码',
      }),
      dataIndex: 'telephone',
      key: 'telephone',
    },
    {
      title: getIntl().formatMessage({
        id: 'telephone.expenses.table.fee.type',
        defaultValue: '收费类型',
      }),
      dataIndex: 'chargeTypeName',
      key: 'chargeTypeName',
    },
    {
      title: getIntl().formatMessage({
        id: 'telephone.expenses.table.usage.unit.price',
        defaultValue: '用量单价',
      }),
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      render: (text, record) => {
        return (
          <span>
            {record.unitPrice ? record.unitPrice : 0}{' '}
            {+record.currency === 1 ? (
              <FormattedMessage
                id="telephone.expenses.table.money.unit"
                defaultMessage="美元"
              />
            ) : (
              <FormattedMessage
                id="over.view.user.num.unit.yuan"
                defaultMessage="元"
              />
            )}
          </span>
        );
      },
    },
    {
      title: getIntl().formatMessage({
        id: 'telephone.expenses.table.subtotal.expenses',
        defaultValue: '费用小计',
      }),
      dataIndex: 'totalCost',
      key: 'totalCost',
      sorter: {
        compare: (a, b) => a.totalCost - b.totalCost,
      },
      render: (text, record) => {
        return (
          <span>
            {record.totalCost ? record.totalCost : 0}{' '}
            {+record.currency === 1 ? (
              <FormattedMessage
                id="telephone.expenses.table.money.unit"
                defaultMessage="美元"
              />
            ) : (
              <FormattedMessage
                id="over.view.user.num.unit.yuan"
                defaultMessage="元"
              />
            )}
          </span>
        );
      },
    },
  ];

  return (
    <div className={styles.dataAnalysisContainer}>
      <div className={styles.selectContainer}>
        <span className={styles.labelText}>
          <FormattedMessage
            id="external.intelligent.agent.AIGC.package.select.time"
            defaultMessage="选择时间"
          />
        </span>
        <Select
          value={timeRangeCode}
          style={{ width: '25%' }}
          onChange={handleChange}
          options={timeList}
          placeholder={getIntl().formatMessage({
            id:
              'external.intelligent.agent.AIGC.package.select.time.placeholder',
            defaultValue: '请选择时间',
          })}
        />
        {timeRangeCode === 4 && (
          <>
            <span className={styles.labelText}>
              <FormattedMessage
                id="external.intelligent.agent.AIGC.package.time.frame"
                defaultMessage="时间范围"
              />
            </span>
            <RangePicker
              allowClear={false}
              onChange={rangePickerChange}
              onOpenChange={onOpenChange}
              value={dates}
              // 显示时分秒
              showTime={{
                format: 'HH:mm:ss',
              }}
            />
          </>
        )}
      </div>
      <div className={styles.topContainer}>
        <div className={styles.topLeftContainer}>
          <Spin spinning={spinning}>
            <div className={styles.secondTitle} style={{ height: '30px' }}>
              <span>
                <FormattedMessage
                  id="telephone.expenses.trend.telephone.charges"
                  defaultMessage="话务费趋势"
                />
              </span>
              <Radio.Group
                style={{ marginRight: '10px' }}
                value={timeDimension}
                onChange={onChangeChart}
              >
                <Popover
                  overlayClassName="agentWorkEfficiencyStatisticsPopover"
                  content={
                    timeDifferenceInHours < 24 &&
                    timeRangeCode === 4 &&
                    timeDifferenceInHours > 0
                      ? null
                      : hourContent
                  }
                  title={null}
                >
                  <Radio.Button
                    value="4"
                    disabled={
                      timeDifferenceInHours < 24 &&
                      timeRangeCode === 4 &&
                      timeDifferenceInHours > 0
                        ? ''
                        : 'true'
                    }
                  >
                    <FormattedMessage
                      id="agent.work.efficiency.statistics.hour.text"
                      defaultMessage="时"
                    />
                  </Radio.Button>
                </Popover>
                <Radio.Button value="1">
                  <FormattedMessage
                    id="agent.work.efficiency.statistics.day.text"
                    defaultMessage="日"
                  />
                </Radio.Button>
                <Radio.Button value="2">
                  <FormattedMessage
                    id="agent.work.efficiency.statistics.week.text"
                    defaultMessage="周"
                  />
                </Radio.Button>
                <Radio.Button value="3">
                  <FormattedMessage
                    id="agent.work.efficiency.statistics.month.text"
                    defaultMessage="月"
                  />
                </Radio.Button>
              </Radio.Group>
            </div>
            {/* <p className={styles.moneyUnit}>
                元
            </p> */}
            {Object.keys(outerAgentTrendData).length > 0 ? (
              <AreaChart data={outerAgentTrendData} />
            ) : (
              <div className={styles.noDataContent}>
                <img src={NoDataImg} />
                <p>
                  <FormattedMessage
                    id="work.order.reply.no.data"
                    defaultMessage="暂无数据"
                  />
                </p>
              </div>
            )}
          </Spin>
        </div>
        <div className={styles.topRightContainer}>
          <Spin spinning={spinning1}>
            <div className={styles.secondTitle} style={{ height: '30px' }}>
              <FormattedMessage
                id="telephone.expenses.proportion.telephone.call.usage"
                defaultMessage="话务费使用占比"
              />
            </div>
            {outerAgentPercentData?.length > 0 ? (
              <div style={{ height: '225px', marginTop: '10px' }}>
                <div className={styles.roseChart}>
                  <RoseChart data={outerAgentPercentData} />
                </div>
                <div className={styles.dataContainer}>
                  {outerAgentPercentData?.map((item, index) => {
                    return (
                      <div className={styles.dataItem}>
                        <div
                          className={styles.circle}
                          style={{
                            background: colorList[index]
                              ? colorList[index]
                              : colorList[index % 6],
                          }}
                        ></div>
                        <div className={styles.channelName}>
                          {item.groupName}
                        </div>
                        <div
                          className={styles.numText}
                          style={{
                            color: colorList[index]
                              ? colorList[index]
                              : colorList[index % 6],
                          }}
                        >
                          {item.num + '%'}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            ) : (
              <div className={styles.noDataContent}>
                <img src={NoDataImg} />
                <p>
                  <FormattedMessage
                    id="work.order.reply.no.data"
                    defaultMessage="暂无数据"
                  />
                </p>
              </div>
            )}
          </Spin>
        </div>
      </div>
      <div className={styles.bottomContainer}>
        <p className={styles.secondTitle}>
          <FormattedMessage
            id="telephone.expenses.cost.details.title"
            defaultMessage="费用明细"
          />
        </p>
        <div className={styles.selectContainer}>
          <span className={styles.labelText}>
            <FormattedMessage
              id="customerInformation.table.nation"
              defaultMessage="国家"
            />
          </span>
          <Select
            allowClear
            showArrow
            showSearch
            value={telephonePrefix}
            style={{ width: '25%' }}
            onChange={(value, options) => handleChangeCountry(value, options)}
            options={countryRegionList}
            fieldNames={{
              label: 'country',
              value: 'telephonePrefix',
              key: 'telephonePrefix',
              countryCode: 'countryCode',
            }}
            placeholder={getIntl().formatMessage({
              id: 'customerInformation.table.nation.placeholder',
              defaultValue: '请选择国家',
            })}
            filterOption={(inputValue, option) =>
              option.country.toLowerCase().indexOf(inputValue.toLowerCase()) >=
              0
            }
          />
          <span className={styles.labelText}>
            <FormattedMessage
              id="telephone.expenses.table.fee.type"
              defaultMessage="收费类型"
            />
          </span>
          <Select
            allowClear
            showArrow
            showSearch
            mode="multiple"
            value={chargeTypeCode}
            style={{ width: '25%' }}
            onChange={handleChangeChargeType}
            options={chargeTypeList}
            placeholder={getIntl().formatMessage({
              id: 'telephone.expenses.table.fee.type.placeholder',
              defaultValue: '请选择收费类型',
            })}
            fieldNames={{
              label: 'chargeTypeName',
              value: 'chargeTypeCode',
              key: 'chargeTypeCode',
            }}
            filterOption={(inputValue, option) =>
              option.label.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
            }
            getPopupContainer={triggerNode => {
              return triggerNode.parentNode;
            }}
          />
          <span className={styles.labelText}>
            <FormattedMessage
              id="phone.robot.package.table.1.system"
              defaultMessage="电话号码"
            />
          </span>
          <Select
            mode="multiple"
            allowClear
            showArrow
            showSearch
            value={systemPhoneNum}
            style={{ width: '25%' }}
            onChange={handleChangeSystemPhoneNum}
            options={phoneNumList}
            placeholder={getIntl().formatMessage({
              id: 'phone.robot.package.table.1.system.placeholder',
              defaultValue: '请选择系统电话号码',
            })}
            filterOption={(inputValue, option) =>
              option.label.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
            }
            getPopupContainer={triggerNode => {
              return triggerNode.parentNode;
            }}
          />
        </div>
        <Table
          dataSource={phoneExpensesDetail}
          columns={columns}
          loading={spinning2}
          pagination={{
            total: total,
            pageSize: pageSize,
            current: pageNum,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showTotal: total => (
              <FormattedMessage
                id="page.total.num"
                defaultMessage={`共 ${total} 条`}
                values={{ total }}
              />
            ),
            onChange: (pageNum, pageSize) => {
              setPageNum(pageNum);
              setPageSize(pageSize);
            },
          }}
        />
      </div>
    </div>
  );
};
// 充值记录
const RechargeRecordContent = props => {
  const dispatch = useDispatch();
  const [spinning, setSpinning] = useState(false);
  const [loadingTable, setLoadingTable] = useState(false);
  const [pageNum, setPageNum] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [selectTime, setSelectTime] = useState(false);
  const [remainCount, setRemainCount] = useState('');
  const [remainList, setRemainList] = useState([]);

  // 使用 useState 来存储日期范围
  const [dates, setDates] = useState(null);
  //状态维度 0-所有（默认） 1-生效中 2-已过期 3-已耗尽
  const [statusCode, setStatusCode] = useState(0);
  const optionsCheckBox = [
    {
      label: getIntl().formatMessage({
        id: 'home.set.options.1',
        defaultValue: '全部',
      }),
      value: 0,
    },
    {
      label: getIntl().formatMessage({
        id: 'external.intelligent.agent.AIGC.package.select.status.in.effect',
        defaultValue: '生效中',
      }),
      value: 1,
    },
    {
      label: getIntl().formatMessage({
        id: 'external.intelligent.agent.AIGC.package.select.status.exhausted',
        defaultValue: '已耗尽',
      }),
      value: 3,
    },
  ];

  useEffect(() => {
    setStatusCode(0);
    setDates(null);
    setStartDate('');
    setEndDate('');
    if (props.tabKey === '2') {
      queryPhoneExpensesRemainBalance();
    }
  }, [props.tabKey]);

  useEffect(() => {
    if (props.tabKey === '2') {
      queryPhoneExpensesRechargeRecord();
    }
  }, [startDate, endDate, statusCode, props.tabKey, pageNum, pageSize]);

  // 当前余额
  const queryPhoneExpensesRemainBalance = () => {
    setSpinning(true);
    dispatch({
      type: 'meteringBilling/queryPhoneExpensesRemainBalance',
      callback: response => {
        setSpinning(false);
        if (response.code === 200) {
          setRemainCount(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 分页查询充值记录
  const queryPhoneExpensesRechargeRecord = () => {
    setLoadingTable(true);
    let params = {
      startDate,
      endDate,
      statusCode,
      pageNum,
      pageSize,
    };
    dispatch({
      type: 'meteringBilling/queryPhoneExpensesRechargeRecord',
      payload: params,
      callback: response => {
        setLoadingTable(false);
        if (response.code === 200) {
          setRemainList(response.data ? response.data.records : []);
          setTotal(response.data?.total);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 切换时间范围
  const rangePickerChange = (value, dateString) => {
    setStartDate(dateString[0]);
    setEndDate(dateString[1]);
    setDates(value);
    setPageNum(1);
  };
  const onOpenChange = open => {
    setSelectTime(open);
  };

  // 切换状态
  const onChangeCheckBox = checkedValues => {
    setStatusCode(checkedValues);
    setPageNum(1);
  };

  const columns = [
    {
      title: (
        <span style={{ fontWeight: 'bolder' }}>
          {getIntl().formatMessage({
            id: 'external.intelligent.agent.AIGC.package.table.type',
            defaultValue: '类型',
          })}
        </span>
      ),
      dataIndex: 'type',
      key: 'type',
      render: (text, record) => {
        return (
          <div className={styles.circleContainer}>
            {record.type === 2
              ? getIntl().formatMessage({
                  id: 'agent.AIGC.package.table.type.1',
                  defaultValue: '系统赠送',
                })
              : getIntl().formatMessage({
                  id: 'agent.AIGC.package.table.type.2',
                  defaultValue: '企业购买',
                })}
          </div>
        );
      },
    },
    {
      title: (
        <span style={{ fontWeight: 'bolder' }}>
          {getIntl().formatMessage({
            id: 'recharge.record.table.recharge.time',
            defaultValue: '充值时间',
          })}
        </span>
      ),
      dataIndex: 'rechargeTime',
      key: 'rechargeTime',
      sorter: (a, b) => new Date(a.rechargeTime) - new Date(b.rechargeTime),
    },
    {
      title: (
        <span style={{ fontWeight: 'bolder' }}>
          {getIntl().formatMessage({
            id: 'recharge.record.table.recharge.amount',
            defaultValue: '充值金额',
          })}
        </span>
      ),
      dataIndex: 'initPayNum',
      key: 'initPayNum',
      sorter: {
        compare: (a, b) => a.initPayNum - b.initPayNum,
      },
      render: (text, record) => {
        return (
          <span>
            {text}{' '}
            {+record.currency === 1 ? (
              <FormattedMessage
                id="telephone.expenses.table.money.unit"
                defaultMessage="美元"
              />
            ) : (
              <FormattedMessage
                id="over.view.user.num.unit.yuan"
                defaultMessage="元"
              />
            )}
          </span>
        );
      },
    },
    {
      title: (
        <span style={{ fontWeight: 'bolder' }}>
          {getIntl().formatMessage({
            id: 'recharge.record.table.remaining.amount',
            defaultValue: '剩余金额',
          })}
        </span>
      ),
      dataIndex: 'remainNum',
      key: 'remainNum',
      minWidth: 400,
      sorter: {
        compare: (a, b) => a.remainNum - b.remainNum,
      },
      render: (text, record) => {
        return (
          <div className={styles.numberCallsContainer}>
            <div className={styles.progressContainer}>
              <div
                className={styles.progressInnerContainer}
                style={{
                  background: +record.remainNum > 0 ? '#13c825' : '#F22417',
                  width:
                    (
                      (Number(record.remainNum) / record.initPayNum) *
                      100
                    ).toFixed(2) + '%',
                }}
              ></div>
            </div>
            <span
              className={styles.numText1}
              style={{
                color: +record.remainNum > 0 ? '#13c825' : '#999999',
              }}
            >
              {formatThousand(record.remainNum)}
            </span>
            <span className={styles.numText2}>
              / {formatThousand(record.initPayNum)}
              &nbsp; &nbsp;
              {+record.currency === 1 ? (
                <FormattedMessage
                  id="telephone.expenses.table.money.unit"
                  defaultMessage="美元"
                />
              ) : (
                <FormattedMessage
                  id="over.view.user.num.unit.yuan"
                  defaultMessage="元"
                />
              )}
            </span>
          </div>
        );
      },
    },
    {
      title: (
        <span style={{ fontWeight: 'bolder' }}>
          {getIntl().formatMessage({
            id: 'external.intelligent.agent.AIGC.package.table.status',
            defaultValue: '状态',
          })}
        </span>
      ),
      dataIndex: 'status',
      key: 'status',
      fixed: 'right',
      render: (text, record) => {
        return (
          <div className={styles.circleContainer}>
            {+record.remainNum > 0 ? (
              <>
                <div className={styles.circle}></div>
                <span>
                  {getIntl().formatMessage({
                    id:
                      'external.intelligent.agent.AIGC.package.select.status.in.effect',
                    defaultValue: '生效中',
                  })}
                </span>
              </>
            ) : +record.status === 0 ? (
              <>
                <div className={styles.circle2}></div>
                <span>
                  {getIntl().formatMessage({
                    id:
                      'external.intelligent.agent.AIGC.package.select.status.exhausted',
                    defaultValue: '已耗尽',
                  })}
                </span>
              </>
            ) : (
              ''
            )}
          </div>
        );
      },
    },
  ];

  return (
    <div className={styles.rechargeRecordContainer}>
      <Spin spinning={spinning}>
        <div className={styles.balanceContainer}>
          <div className={styles.iconContainer}>{NumIcon()}</div>
          <div className={styles.balanceNumContainer}>
            <p>
              <span className={styles.currentBalance}>
                <FormattedMessage
                  id="over.view.current.balance"
                  defaultMessage="当前余额"
                />
              </span>
              {/* <span className={styles.goRecharge}>
              <span>
                <FormattedMessage
                  id="external.intelligent.agent.AIGC.package.go.buy"
                  defaultMessage="去购买"
                />
              </span>
              {RightArrowIcon()}
            </span> */}
            </p>
            <div>
              <span className={styles.balanceNum}>
                {remainCount.remainPay
                  ? formatThousand(remainCount.remainPay)
                  : 0}
              </span>
              <span className={styles.unitYuan}>
                {+remainCount.currency === 1 ? (
                  <FormattedMessage
                    id="telephone.expenses.table.money.unit"
                    defaultMessage="美元"
                  />
                ) : (
                  <FormattedMessage
                    id="over.view.user.num.unit.yuan"
                    defaultMessage="元"
                  />
                )}
              </span>
            </div>
          </div>
        </div>
      </Spin>
      <div className={styles.selectContainer}>
        <span className={styles.labelText}>
          <FormattedMessage
            id="external.intelligent.agent.AIGC.package.select.buy.time"
            defaultMessage="购买时间"
          />
        </span>
        <RangePicker
          allowClear={true}
          onChange={rangePickerChange}
          onOpenChange={onOpenChange}
          value={dates}
          // 显示时分秒
          showTime={{
            format: 'HH:mm:ss',
          }}
          cl
        />
        <span className={styles.labelText}>
          <FormattedMessage
            id="external.intelligent.agent.AIGC.package.select.status"
            defaultMessage="状态："
          />
        </span>
        <Select
          style={{ width: '20%' }}
          options={optionsCheckBox}
          onChange={onChangeCheckBox}
          value={statusCode}
        />
      </div>
      <div className={styles.tableContainer}>
        <Table
          dataSource={remainList}
          columns={columns}
          loading={loadingTable}
          pagination={{
            total: total,
            pageSize: pageSize,
            current: pageNum,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showTotal: total => (
              <FormattedMessage
                id="page.total.num"
                defaultMessage={`共 ${total} 条`}
                values={{ total }}
              />
            ),
            onChange: (pageNum, pageSize) => {
              setPageNum(pageNum);
              setPageSize(pageSize);
            },
          }}
        />
      </div>
    </div>
  );
};

export default TelephoneExpenses;
