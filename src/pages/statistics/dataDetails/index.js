import React, { useState, useRef, useEffect } from 'react';
import { FormattedMessage, useDispatch, getIntl, useSelector } from 'umi';
import {
  DatePicker,
  Input,
  notification,
  Table,
  Select,
  Spin,
  Button,
  Checkbox,
  Tabs,
  Popover,
  Modal,
  Form,
  Row,
  Col,
  Radio,
  Switch,
  message,
} from 'antd';
import styles from './index.less';
import { ChannelTypeSelect } from '@/components/channelSelect'; // 导入通用组件
import { PlusOutlined, SearchOutlined } from '@ant-design/icons';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import { ReactComponent as Search } from '@/assets/Search.svg';
import TableEmailIcon from '@/assets/table-email-icon.png';
import TablePhoneIcon from '@/assets/table-phone-icon.png';
import TableInfoIcon from '@/assets/table-info-icon.png';
import WhatsAppIcon from '@/assets/whats-app.svg';
import ChatIcon from '@/assets/chat-icon.jpg';
import AppChatOutlinedIcon from '@/assets/AppChatOutlined.svg';
import WebVideoOutlinedIcon from '@/assets/WebVideoOutlined.svg';
import AppVideoOutlinedIcon from '@/assets/AppVideoOutlined.svg';
import EmailIcon from '@/assets/email.svg';
import FacebookIcon from '@/assets/facebook.svg';
import TwitterIcon from '@/assets/twitter.svg';
import LineIcon from '@/assets/line.svg';
import PhoneIcon from '@/assets/phone.svg';
import moment from 'moment';
import AwsChannelIcon from '@/assets/aws-channel-icon.svg';

import NewWebOnlineVoiceIcon from '@/assets/web-online-voice-icon.svg';
import NewAppOnlineVoiceIcon from '@/assets/app-online-voice-icon.svg';
import NewFaceBookIcon from '@/assets/new-facebook-icon.svg';
import NewInstagramIcon from '@/assets/ins.svg';
import NewLineIcon from '@/assets/new-line-icon.svg';
import NewTwitterIcon from '@/assets/new-twitter-icon.svg';
import NewTelegramIcon from '@/assets/new-telegram-icon.svg';
import NewWeComIcon from '@/assets/new-wecom-icon.svg';
import NewWeChatMiniProgramIcon from '@/assets/new-wechat-mini-program-icon.svg';
import NewWechatOfficialAccountIcon from '@/assets/new-wechat-official-account-icon.svg';
import NewShopifyIcon from '@/assets/new-shopify-icon.svg';
import NewGooglePlayIcon from '@/assets/google-play-icon.svg';
import TikTokIcon from '@/assets/new-tiktok-icon.svg';
import DiscordIcon from '@/assets/new-discord-icon.svg';
import { pinyin } from 'pinyin-pro';

const { RangePicker } = DatePicker;
const { Option, OptGroup } = Select;

const BatchExportIcon = () => (
  <svg
    style={{ float: 'left', marginRight: '4px', marginTop: '1px' }}
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <path
      d="M12.6871 10.0422C12.4855 10.0454 12.323 10.211 12.3262 10.4125V11.3422C12.3262 11.7485 12.0043 12.0797 11.6074 12.0797H4.42461C4.02773 12.0797 3.70586 11.7485 3.70586 11.3422V4.70942C3.70586 4.30317 4.02773 3.97192 4.42461 3.97192H6.92461L7.85273 5.62349C7.92773 5.75474 8.06836 5.82192 8.20742 5.8063H11.6059C12.0027 5.8063 12.3246 6.13755 12.3246 6.5438V7.46567C12.3246 7.67036 12.4871 7.83599 12.6871 7.83599C12.8871 7.83599 13.048 7.67036 13.048 7.46567C13.048 7.4563 13.048 7.44692 13.0465 7.43755V6.5438C13.0465 5.72974 12.4027 5.0688 11.609 5.0688H8.37305L7.44648 3.42349C7.36367 3.27505 7.24492 3.23599 7.14023 3.23911L7.13867 3.23755H4.41992C3.62617 3.23755 2.98242 3.89849 2.98242 4.71255V11.3266C2.98242 12.1407 3.62617 12.8016 4.41992 12.8016H11.609C12.4027 12.8016 13.0465 12.1407 13.0465 11.3266V10.4407C13.048 10.4313 13.048 10.4219 13.048 10.4125C13.0496 10.3157 13.0121 10.2219 12.9434 10.1532C12.8762 10.0829 12.784 10.0438 12.6871 10.0422Z"
      fill="#3463FC"
    />
    <path
      d="M8.85371 7.3626C8.91152 7.3001 8.99121 7.26416 9.07715 7.26416C9.16152 7.26416 9.24277 7.3001 9.30059 7.3626L10.6318 8.77979L10.6396 8.7876H10.6412L10.649 8.79541C10.7693 8.92822 10.7693 9.13135 10.649 9.26416L9.31309 10.6892C9.25684 10.7517 9.17715 10.786 9.09277 10.7876C9.0084 10.7876 8.92871 10.7532 8.87246 10.6907L8.86621 10.6829C8.7459 10.5501 8.7459 10.347 8.86621 10.2142L9.6709 9.35479H6.01621C5.8459 9.35479 5.70684 9.20635 5.70684 9.0251C5.70684 8.84385 5.8459 8.69541 6.01621 8.69541H9.65684L8.85371 7.8376C8.73027 7.70479 8.73027 7.49697 8.85371 7.3626Z"
      fill="#3463FC"
    />
  </svg>
);
const ColumnOptionsIcon = () => (
  <svg
    style={{ float: 'left', marginRight: '3px', marginTop: '1px' }}
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M6.64666 13L9.03481 11.876V8.93017L12.7796 5.13228C12.9208 4.98998 13 4.79466 13 4.59014V3.75877C13 3.33934 12.6709 3 12.2648 3H3.73524C3.32913 3 3 3.33934 3 3.75877V4.60915C3 4.80215 3.07085 4.98767 3.19859 5.12825L6.64666 8.93017V13Z"
      stroke="#3463FC"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);
const CopyIcon = () => (
  <div className={styles.copyIcon}>
    <svg
      style={{ float: 'left', marginTop: '3px' }}
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.1823 13.9998H2.91824C2.41195 13.9998 2 13.5879 2 13.0816V6.98164L5.08236 3.89929H11.1823C11.6886 3.89929 12.1005 4.31124 12.1005 4.81752V13.0816C12.1005 13.5879 11.6886 13.9998 11.1823 13.9998ZM2.91824 7.36203V13.0816H11.1829L11.1823 4.81752H5.46274L2.91824 7.36203Z"
        fill="#3463FC"
      />
      <path
        d="M5.30562 7.6643H5.30033L2.45383 7.63121C2.26857 7.62913 2.10286 7.51579 2.03344 7.34408C1.9995 7.26011 1.99117 7.16797 2.00949 7.07926C2.02781 6.99056 2.07196 6.90926 2.13639 6.8456L4.98289 4.03216C5.11453 3.90187 5.31172 3.86368 5.48246 3.93487C5.56603 3.96981 5.63741 4.02867 5.68762 4.10406C5.73783 4.17945 5.76464 4.268 5.76466 4.35858V7.20509C5.76466 7.26583 5.7526 7.32596 5.72918 7.382C5.70577 7.43805 5.67146 7.48889 5.62825 7.53157C5.54242 7.61663 5.42646 7.66434 5.30562 7.6643ZM3.56376 6.7258L4.8465 6.74069V5.45784L3.56376 6.7258ZM13.541 10.9068C13.2874 10.9068 13.0818 10.7013 13.0818 10.4477V2.91824H7.38881C7.13532 2.91824 6.92971 2.7127 6.92971 2.45911C6.92971 2.20551 7.13532 2 7.38881 2H13.541C13.7944 2 14.0001 2.20553 14.0001 2.45911V10.4477C14.0001 10.7013 13.7944 10.9068 13.541 10.9068ZM9.71314 11.7963H4.29555C4.04206 11.7963 3.83644 11.5908 3.83644 11.3372C3.83644 11.0836 4.04206 10.8781 4.29555 10.8781H9.71313C9.96662 10.8781 10.1722 11.0836 10.1722 11.3372C10.1722 11.5908 9.96662 11.7963 9.71314 11.7963ZM9.71314 9.77622H4.29555C4.04206 9.77622 3.83644 9.5707 3.83644 9.3171C3.83644 9.06351 4.04206 8.85797 4.29555 8.85797H9.71313C9.96662 8.85797 10.1722 9.06351 10.1722 9.3171C10.1722 9.5707 9.96662 9.77622 9.71314 9.77622ZM9.71314 7.6643H6.86662C6.61313 7.6643 6.40751 7.45877 6.40751 7.20519C6.40751 6.9516 6.61313 6.74606 6.86662 6.74606H9.71314C9.96663 6.74606 10.1723 6.95161 10.1723 7.20519C10.1722 7.45877 9.96662 7.6643 9.71314 7.6643Z"
        fill="#3463FC"
      />
    </svg>
  </div>
);
const DataDetailsContent = () => {
  const dispatch = useDispatch();
  const [spinning, setSpinning] = useState(false);
  const [pageNum, setPageNum] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [connectList, setConnectList] = useState([]);
  // const [startDate, setStartDate] = useState('');
  // const [endDate, setEndDate] = useState('');
  const [startDate, setStartDate] = useState(
    moment()
      .startOf('month')
      .format('YYYY-MM-DD HH:mm:ss'),
  );
  const [endDate, setEndDate] = useState(
    moment()
      .endOf('month')
      .format('YYYY-MM-DD HH:mm:ss'),
  );
  const [selectTime, setSelectTime] = useState(false);
  const [multiFieldQuery, setMultiFieldQuery] = useState(''); // 联系id
  const [dataSource, setDataSource] = useState([]);
  const [total, setTotal] = useState(0);
  const [channelTypeId, setChannelTypeId] = useState('');
  const [channelTypeList, setChannelTypeList] = useState([]);
  let [connectLine, setConnectLine] = useState(null); // 联络线路
  let [voiceSupplierRealName, setVoiceSupplierRealName] = useState(null); // 联络线路区域--twilio/connect
  let [filterWorkOrderExtList, setFilterWorkOrderExtList] = useState([]); //  form表单所有可扩展列数据
  let [defaultValueArray, setDefaultValue] = useState([]); // form表单动态列默认值
  let [openMore, setOpenMore] = useState(false); //显示更多字段弹窗]
  let [temporarilyCheckList, setTemporarilyCheckList] = useState([]); //  临时变量，添加form表单数据
  let [filterFormList, setFilterFormList] = useState([]); //  添加后动态form数据
  let [formLoading, setFormLoading] = useState(false);
  let [flag, setFlag] = useState(false);
  let [defaultTableValueArray, setDefaultTableValueArray] = useState([]); // 表格配置动态列id集合
  let [openMoreTableColumn, setOpenMoreTableColumn] = useState(false);
  let [loadingTable, setLoadingTable] = useState(false);
  let [columns, setColumns] = useState([]);
  const [sortedInfo, setSortedInfo] = useState({});
  let [workOrderTableHeaderList, setWorkOrderTableHeaderList] = useState([]);
  let [exportLoading, setExportLoading] = useState(false);
  let [
    filterTableColumnWorkOrderExtList,
    setTableColumnFilterWorkOrderExtList,
  ] = useState([]); //  table所有可扩展列数据

  useEffect(() => {
    getChannelList();
    queryWorkOrderExtList();
    queryFilterCondition();
    queryWorkRecordByUserId();
    getAllConnectList();
  }, [flag]);
  useEffect(() => {
    if (!selectTime) {
      handleSearch();
    }
  }, [
    pageNum,
    pageSize,
    multiFieldQuery,
    selectTime,
    endDate,
    channelTypeId,
    connectLine,
  ]);

  // 查询联络明细
  const getAllConnectList = () => {
    setSpinning(true);
    dispatch({
      type: 'channel/queryContactLineList',
      callback: response => {
        setSpinning(false);
        let { code, data, msg } = response;
        if (200 === code) {
          let connectList;
          if (data) {
            connectList = data;
          } else {
            connectList = [];
          }
          setConnectList(connectList);
        } else {
          notification.error({
            message: msg,
          });
        }
      },
    });
  };

  // 查询工单属性定义扩展
  const queryWorkOrderExtList = () => {
    dispatch({
      type: 'workOrderCenter/queryWorkOrderExtList',
      payload: 1,
      callback: response => {
        if (response.code == 200) {
          setTableColumnFilterWorkOrderExtList(response.data); //  table扩展列
          // 处理form动态列和默认列渠道类型重复问题
          let res = response.data.filter(
            item =>
              item.workRecordExtDefCode !== 'channelTypeName' &&
              item.workRecordExtDefCode !== 'createTime',
          );
          setFilterWorkOrderExtList(res);
          setFlag(true);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 查询加载时默认展示的字段
  const queryFilterCondition = () => {
    let storageFormData = JSON.parse(localStorage.getItem('filterFormList'));
    if (storageFormData && storageFormData.length > 0) {
      const matchedData = filterWorkOrderExtList?.filter(item =>
        storageFormData?.some(
          id => id.workRecordExtDefCode == item.workRecordExtDefCode,
        ),
      );
      const defaultIds = matchedData?.map(item => item.workRecordExtDefId);
      setFilterFormList(storageFormData);
      setDefaultValue(defaultIds);
      setTemporarilyCheckList(matchedData);
    }
  };

  // 加载渠道类型
  const getChannelList = () => {
    dispatch({
      type: 'dataDetails/getChannelList',
      callback: response => {
        if (response.code == 200) {
          if (response.data) {
            let list = response.data.filter(item =>
              ['7', '8', '9', '10', '11'].includes(item.code),
            );
            setChannelTypeList(list);
          }
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 切换渠道类型
  const handleChangeSelect = value => {
    if (value !== undefined) {
      setChannelTypeId(value);
      setPageNum(1);
    } else {
      setChannelTypeId('');
      setPageNum(1);
    }
  };

  // 时间选择框事件
  const rangePickerChange = (value, dateString) => {
    setStartDate(dateString[0]);
    setEndDate(dateString[1]);
  };
  const onOpenChange = open => {
    setSelectTime(open);
  };

  // 批量导出
  const handleBatchExport = () => {
    setExportLoading(true);
    const formatWorkOrderExtVos = filterFormList?.map(item => ({
      values: item.workRecordExtDefValue,
      code: item.workRecordExtDefCode,
      isSystemDefault: item.isSystemDefault,
      propType: item.propertyTypeId, // 注意这里应该是 propertyTypeId 而不是 propType
    }));
    // 如果values为null或者不存在的话 不传这个字段
    const filterValues = formatWorkOrderExtVos.filter(
      item =>
        item.values !== null && item.values !== undefined && item.values !== '',
    );
    const params = {
      startDate: startDate, //  选择日期开始
      endDate: endDate, //  选择日期结束
      connectAlias: connectLine, // 联络线路
      voiceSupplierRealName: voiceSupplierRealName,
      queryChannelType: channelTypeId, // 渠道类型
      multiFieldQuery: multiFieldQuery, // 联系id，多条件输入
      pageNum: pageNum,
      pageSize: pageSize,
      workOrderExtVos: filterValues, //  动态form字段
    };
    dispatch({
      type: 'dataDetails/exportDataDetails',
      payload: params,
      callback: response => {
        if (response) {
          setExportLoading(false);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // let [defaultColumns, setDefaultColumns]
  let defaultColumns = [
    {
      title: getIntl().formatMessage({
        id: 'statistics.data.details.table.contact.id',
        defaultValue: '联系ID',
      }),
      width: 200,
      dataIndex: 'contactId',
      key: 'contactId',
      ellipsis: true,
    },
    {
      title: (
        <FormattedMessage
          id="email.channel.configuration.calling.lines"
          defaultMessage="联络线路"
        />
      ),
      dataIndex: 'connectAlias',
      key: 'connectAlias',
      width: 200,
      ellipsis: true,
    },
    {
      title: getIntl().formatMessage({
        id: 'statistics.data.details.table.incoming.call.time',
        defaultValue: '开始时间',
      }),
      dataIndex: 'startTime',
      key: 'startTime',
      width: 200,
      ellipsis: true,
    },
    {
      title: getIntl().formatMessage({
        id: 'statistics.data.details.table.incoming.call.time.start',
        defaultValue: '座席应答时间',
      }),
      dataIndex: 'callTime',
      key: 'callTime',
      width: 200,
      ellipsis: true,
    },
    {
      title: getIntl().formatMessage({
        id: 'statistics.data.details.table.end.time',
        defaultValue: '挂断时间',
      }),
      dataIndex: 'endTime',
      key: 'endTime',
      width: 200,
      ellipsis: true,
    },
    {
      title: getIntl().formatMessage({
        id: 'statistics.data.details.table.acw.time',
        defaultValue: 'ACW结束时间',
      }),
      dataIndex: 'acwTime',
      key: 'acwTime',
      width: 200,
      ellipsis: true,
    },
    {
      title: getIntl().formatMessage({
        id: 'statistics.data.details.table.total.call.duration',
        defaultValue: '通话总时长',
      }),
      dataIndex: 'totalTime',
      key: 'totalTime',
      width: 200,
      ellipsis: true,
    },
    {
      title: getIntl().formatMessage({
        id: 'statistics.data.details.table.interaction.time',
        defaultValue: '互动时间',
      }),
      dataIndex: 'interactionTime',
      key: 'interactionTime',
      width: 200,

      ellipsis: true,
    },
    {
      title: getIntl().formatMessage({
        id: 'statistics.data.details.table.queue.waiting.time',
        defaultValue: '队列等待时间',
      }),
      dataIndex: 'queueWaitTime',
      key: 'queueWaitTime',
      width: 200,
      ellipsis: true,
    },
    {
      title: getIntl().formatMessage({
        id: 'statistics.data.details.table.acw.duration',
        defaultValue: 'ACW时长',
      }),
      dataIndex: 'acwDuration',
      key: 'acwDuration',
      width: 200,
      ellipsis: true,
    },
    {
      title: getIntl().formatMessage({
        id: 'statistics.data.details.table.incoming.call.channel',
        defaultValue: '渠道',
      }),
      dataIndex: 'callChannel',
      key: 'callChannel',
      ellipsis: true,
      width: 200,
      render: (text, record) => {
        if (record.callChannel == '1') {
          return (
            <div className={styles.channelName}>
              <img src={TableEmailIcon} />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.email"
                  defaultMessage="邮件"
                />
              </span>
            </div>
          );
        } else if (record.callChannel == '7') {
          return (
            <div className={styles.channelName}>
              <img src={TablePhoneIcon} />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.phone"
                  defaultMessage="电话"
                />
              </span>
            </div>
          );
        } else if (record.callChannel == '2') {
          return (
            <div className={styles.channelName}>
              <img src={TableInfoIcon} />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.info"
                  defaultMessage="短信"
                />
              </span>
            </div>
          );
        } else if (record.callChannel == '3') {
          return (
            <div className={styles.channelName}>
              <img src={NewFaceBookIcon} />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.facebook"
                  defaultMessage="Facebook"
                />
              </span>
            </div>
          );
        } else if (record.callChannel == '4') {
          return (
            <div className={styles.channelName}>
              <img src={WhatsAppIcon} />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.whats.app"
                  defaultMessage="WhatsApp"
                />
              </span>
            </div>
          );
        } else if (record.callChannel == '8') {
          return (
            <div className={styles.channelName}>
              <img src={ChatIcon} />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.chat"
                  defaultMessage="Web在线聊天"
                />
              </span>
            </div>
          );
        } else if (record.callChannel == '9') {
          return (
            <div className={styles.channelName}>
              <img src={AppChatOutlinedIcon} />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.app.chat"
                  defaultMessage="App在线聊天"
                />
              </span>
            </div>
          );
        } else if (record.callChannel == '10') {
          return (
            <div className={styles.channelName}>
              <img src={WebVideoOutlinedIcon} />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.web.video"
                  defaultMessage="Web在线视频"
                />
              </span>
            </div>
          );
        } else if (record.callChannel == '11') {
          return (
            <div className={styles.channelName}>
              <img src={AppVideoOutlinedIcon} />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.app.video"
                  defaultMessage="App在线视频"
                />
              </span>
            </div>
          );
        } else if (record.callChannel == '12') {
          return (
            <div className={styles.channelName}>
              <img src={AwsChannelIcon} />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.amazon.message"
                  defaultMessage="亚马逊站内信"
                />
              </span>
            </div>
          );
        } else if (record.callChannel == '13') {
          return (
            <div className={styles.channelName}>
              <img src={NewInstagramIcon} />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.instagram"
                  defaultMessage="Instagram"
                />
              </span>
            </div>
          );
        } else if (record.callChannel == '14') {
          return (
            <div className={styles.channelName}>
              <img src={NewLineIcon} />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.line"
                  defaultMessage="Line"
                />
              </span>
            </div>
          );
        } else if (record.callChannel == '15') {
          return (
            <div className={styles.channelName}>
              <img src={NewWeComIcon} />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.weCom"
                  defaultMessage="微信客服"
                />
              </span>
            </div>
          );
        } else if (record.callChannel == '16') {
          return (
            <div className={styles.channelName}>
              <img src={NewWechatOfficialAccountIcon} />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.weChat.official.account"
                  defaultMessage="微信公众号"
                />
              </span>
            </div>
          );
        } else if (record.callChannel == '17') {
          return (
            <div className={styles.channelName}>
              <img src={NewWebOnlineVoiceIcon} />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.web.online.video"
                  defaultMessage="WEB在线语音"
                />
              </span>
            </div>
          );
        } else if (record.callChannel == '18') {
          return (
            <div className={styles.channelName}>
              <img src={NewAppOnlineVoiceIcon} />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.app.online.video"
                  defaultMessage="APP在线语音"
                />
              </span>
            </div>
          );
        } else if (record.callChannel == '19') {
          return (
            <div className={styles.channelName}>
              <img src={NewTwitterIcon} />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.twitter"
                  defaultMessage="Twitter"
                />
              </span>
            </div>
          );
        } else if (record.callChannel == '20') {
          return (
            <div className={styles.channelName}>
              <img src={NewTelegramIcon} />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.telegram"
                  defaultMessage="Telegram"
                />
              </span>
            </div>
          );
        } else if (record.callChannel == '21') {
          return (
            <div className={styles.channelName}>
              <img src={NewWeChatMiniProgramIcon} />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.weChat.mini.program"
                  defaultMessage="微信小程序"
                />
              </span>
            </div>
          );
        } else if (record.callChannel == '22') {
          return (
            <div className={styles.channelName}>
              <img src={NewShopifyIcon} />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.shopify"
                  defaultMessage="Shopify"
                />
              </span>
            </div>
          );
        } else if (record.callChannel == '23') {
          return (
            <div className={styles.channelName}>
              <img src={NewGooglePlayIcon} />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.google.play"
                  defaultMessage="Google Play"
                />
              </span>
            </div>
          );
        } else if (record.callChannel == '24') {
          return (
            <div className={styles.channelName}>
              <img src={TikTokIcon} />
              <span>TikTok Shop</span>
            </div>
          );
        } else if (record.callChannel == '25') {
          return (
            <div className={styles.channelName}>
              <img src={DiscordIcon} />
              <span>Discord</span>
            </div>
          );
        } else {
          return (
            <div className={styles.channelName}>
              <span>{record.callChannel}</span>
            </div>
          );
        }
      },
    },
    {
      title: getIntl().formatMessage({
        id: 'statistics.data.details.table.reception.seat',
        defaultValue: '接待座席',
      }),
      dataIndex: 'receptionAgent',
      key: 'receptionAgent',
      width: 200,
      ellipsis: true,
    },
    {
      title: getIntl().formatMessage({
        id: 'statistics.data.details.table.seating.group',
        defaultValue: '座席组',
      }),
      dataIndex: 'agentGroup',
      key: 'agentGroup',
      width: 200,
      ellipsis: true,
    },
    {
      title: getIntl().formatMessage({
        id: 'statistics.data.details.table.queue',
        defaultValue: '队列',
      }),
      dataIndex: 'queueName',
      key: 'queueName',
      width: 200,
      ellipsis: true,
    },
    {
      title: getIntl().formatMessage({
        id: 'statistics.data.details.table.queue.in',
        defaultValue: '呼入/呼出',
      }),
      dataIndex: 'incomingOutgoing',
      key: 'incomingOutgoing',
      width: 200,
      ellipsis: true,
    },
    {
      title: getIntl().formatMessage({
        id: 'statistics.data.details.table.phone.customer',
        defaultValue: '客户电话',
      }),
      dataIndex: 'customerPhone',
      key: 'customerPhone',
      width: 200,
      ellipsis: true,
    },
    {
      title: getIntl().formatMessage({
        id: 'statistics.data.details.table.system.phone',
        defaultValue: '系统电话',
      }),
      dataIndex: 'systemPhone',
      key: 'systemPhone',
      width: 200,
      ellipsis: true,
    },
    {
      title: getIntl().formatMessage({
        id: 'statistics.data.details.table.work.order.number',
        defaultValue: '对应工单编号',
      }),
      dataIndex: 'workOrderNumber',
      key: 'workOrderNumber',
      width: 200,
      ellipsis: true,
    },
    {
      title: getIntl().formatMessage({
        id: 'statistics.data.details.table.on.hold.time',
        defaultValue: '座席OnHold时间',
      }),
      dataIndex: 'onHoldTime',
      key: 'onHoldTime',
      width: 200,
      ellipsis: true,
    },
    {
      title: getIntl().formatMessage({
        id: 'statistics.data.details.table.on.hold.num',
        defaultValue: '座席OnHold次数',
      }),
      dataIndex: 'onHoldNumber',
      key: 'onHoldNumber',
      width: 200,
      ellipsis: true,
    },
    {
      title: getIntl().formatMessage({
        id: 'statistics.data.details.table.is.switch',
        defaultValue: '是否转接',
      }),
      dataIndex: 'isSwitch',
      key: 'isSwitch',
      width: 200,
      ellipsis: true,
      render: (text, record) => {
        // 0否，1是
        if (text == '1') {
          return (
            <span>
              <FormattedMessage
                id="work.order.management.table.robot.work.order.yes"
                defaultMessage="是"
              />
            </span>
          );
        } else {
          return (
            <span>
              <FormattedMessage
                id="work.order.management.table.robot.work.order.no"
                defaultMessage="否"
              />
            </span>
          );
        }
      },
    },
    {
      title: getIntl().formatMessage({
        id: 'statistics.data.details.table.hanging.type',
        defaultValue: '挂断类型',
      }),
      dataIndex: 'hangingType',
      key: 'hangingType',
      width: 200,
      ellipsis: true,
    },

    {
      title: getIntl().formatMessage({
        id: 'statistics.data.details.table.contact.id.id',
        defaultValue: '初始联络ID',
      }),
      dataIndex: 'initialContactId',
      key: 'initialContactId',
      width: 200,
      ellipsis: true,
    },
    {
      title: getIntl().formatMessage({
        id: 'statistics.data.details.table.contact.id.previous',
        defaultValue: '上一个联络ID',
      }),
      dataIndex: 'previousContactId',
      key: 'previousContactId',
      width: 200,
      ellipsis: true,
    },
    {
      title: getIntl().formatMessage({
        id: 'statistics.data.details.table.contact.id.next',
        defaultValue: '下一个联络ID',
      }),
      dataIndex: 'nextContactId',
      key: 'nextContactId',
      width: 200,
      ellipsis: true,
    },

    {
      title: getIntl().formatMessage({
        id: 'statistics.data.details.table.satisfaction.rating',
        defaultValue: '满意度评分',
      }),
      dataIndex: 'satisfactionRating',
      key: 'satisfactionRating',
      width: 200,
      ellipsis: true,
    },
  ];
  /**
   * 呼叫类型选项
   */
  const handleChangeSelectCallingLine = (value, options) => {
    console.log('------------value-----------', value);
    console.log('------------options-----------', options);
    if (value) {
      // setConnectLine(JSON.parse(value).connectId);
      setConnectLine(options.label);
      setVoiceSupplierRealName(options.voiceSupplierRealName);
    } else {
      setConnectLine(null);
      setVoiceSupplierRealName(null);
    }
  };

  /**======================动态form表单配置============================ */
  // 重置当前筛选器
  const resetFilter = () => {
    setDefaultValue([]);
    setFilterFormList([]);
    localStorage.removeItem('filterFormList');
  };
  // 勾选
  const onChangeCheckbox = checkedValues => {
    setDefaultValue(checkedValues);
    let idSet = new Set(checkedValues);
    const matchedObjects = filterWorkOrderExtList?.filter(item =>
      idSet.has(item.workRecordExtDefId),
    );
    // console.log(matchedObjects, 'matchedObjects');
    setTemporarilyCheckList(matchedObjects);
  };
  // 打开
  const handleOpenChange = () => {
    setOpenMore(true);
  };
  // 取消勾选
  const hide = () => {
    setOpenMore(false);
  };
  // 确定添加form动态列
  const addMoreField = () => {
    setOpenMore(false);
    let newData = temporarilyCheckList.map(item => ({
      ...item,
      workRecordExtDefValue: null,
    }));
    setFilterFormList(newData);
    localStorage.setItem('filterFormList', JSON.stringify(newData));
    // console.log(temporarilyCheckList, 'temporarilyCheckList');
  };

  // input框取值
  const handleAddInputChange = (e, item, index) => {
    let newfilterFormList = [...filterFormList];
    if (
      item.workRecordExtDefCode == newfilterFormList[index].workRecordExtDefCode
    ) {
      newfilterFormList[index].workRecordExtDefValue = e.target.value;
    }
    setFilterFormList(newfilterFormList);
  };

  // SELECT
  const handleChangeSelectChannelType = (value, item, index) => {
    let newfilterFormList = [...filterFormList];
    if (
      item.workRecordExtDefCode == newfilterFormList[index].workRecordExtDefCode
    ) {
      newfilterFormList[index].workRecordExtDefValue = value;
    }
    setFilterFormList(newfilterFormList);
  };

  // select 多选
  const handleChangeMultipleSelect = (value, item, index) => {
    let newfilterFormList = [...filterFormList];
    if (
      item.workRecordExtDefCode == newfilterFormList[index].workRecordExtDefCode
    ) {
      newfilterFormList[index].workRecordExtDefValue = value;
    }
    setFilterFormList(newfilterFormList);
  };
  // radio 单选
  const handleChangeRadio = (e, item, index) => {
    let newfilterFormList = [...filterFormList];
    if (
      item.workRecordExtDefCode == newfilterFormList[index].workRecordExtDefCode
    ) {
      newfilterFormList[index].workRecordExtDefValue = e.target.value;
    }
    setFilterFormList(newfilterFormList);
  };

  // checkbox多选
  const handleChangeCheckbox = (checkedValues, item, index) => {
    let newfilterFormList = [...filterFormList];
    if (
      item.workRecordExtDefCode == newfilterFormList[index].workRecordExtDefCode
    ) {
      newfilterFormList[index].workRecordExtDefValue = checkedValues;
    }
    setFilterFormList(newfilterFormList);
  };

  // 开关
  const handleChangeSwitch = (checked, item, index) => {
    let newfilterFormList = [...filterFormList];
    if (
      item.workRecordExtDefCode == newfilterFormList[index].workRecordExtDefCode
    ) {
      newfilterFormList[index].workRecordExtDefValue = checked;
    }
    setFilterFormList(newfilterFormList);
  };

  // 时间选择
  const handleSelectTimeChange = (dateString, item, index) => {
    let newfilterFormList = [...filterFormList];
    if (
      item.workRecordExtDefCode == newfilterFormList[index].workRecordExtDefCode
    ) {
      newfilterFormList[index].workRecordExtDefValue = dateString;
    }
    setFilterFormList(newfilterFormList);
  };

  // 时间范围
  const handleSelectRangeChange = (dateString, item, index) => {
    let newfilterFormList = [...filterFormList];
    if (
      item.workRecordExtDefCode == newfilterFormList[index].workRecordExtDefCode
    ) {
      newfilterFormList[index].workRecordExtDefValue = dateString.join(',');
    }
    setFilterFormList(newfilterFormList);
  };

  // 查询 handleSearch
  const handleSearch = () => {
    setSpinning(true);
    const formatWorkOrderExtVos = filterFormList?.map(item => ({
      values: item.workRecordExtDefValue,
      code: item.workRecordExtDefCode,
      isSystemDefault: item.isSystemDefault,
      propType: item.propertyTypeId, // 注意这里应该是 propertyTypeId 而不是 propType
    }));
    // 如果values为null或者不存在的话 不传这个字段
    const filterValues = formatWorkOrderExtVos.filter(
      item =>
        item.values !== null && item.values !== undefined && item.values !== '',
    );
    const params = {
      startDate: startDate, //  选择日期开始
      endDate: endDate, //  选择日期结束
      connectAlias: connectLine, // 联络线路
      voiceSupplierRealName: voiceSupplierRealName,
      queryChannelType: channelTypeId, // 渠道类型
      multiFieldQuery: multiFieldQuery, // 联系id，多条件输入
      pageNum: pageNum,
      pageSize: pageSize,
      workOrderExtVos: filterValues, //  动态form字段
    };
    // console.log('handleSearch', params);
    dispatch({
      type: 'dataDetails/queryStatDataDetails',
      payload: params,
      callback: response => {
        setSpinning(false);
        if (response.code == 200) {
          let data = response.data; //
          if (response.data) {
            setTotal(data.total);
            setDataSource(data.records);
          }
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  /**======================== 表格动态列 ============================ */
  // setDefaultTableValueArray
  const handleOpenChangeTableColumn = () => {
    setOpenMoreTableColumn(true);
  };
  const onChangeCheckboxTbale = checkedValues => {
    setDefaultTableValueArray(checkedValues);
  };

  // 取消
  const tableColumnHide = () => {
    setOpenMoreTableColumn(false);
  };
  // 确定
  const tableColumnAddMoreField = () => {
    dispatch({
      type: 'workOrderCenter/addUserContactDetailExt',
      payload: defaultTableValueArray,
      callback: response => {
        if (response.code == 200) {
          notification.success({
            message: getIntl().formatMessage({
              id: 'user.management.operation.success',
              defaultValue: '操作成功',
            }),
          });
          setOpenMoreTableColumn(false);
          setColumns(defaultColumns);
          queryWorkRecordByUserId();
        } else {
          setOpenMoreTableColumn(false);
          notification.error({
            message: getIntl().formatMessage({
              id: 'user.management.operation.failure',
              defaultValue: '操作失败',
            }),
          });
        }
      },
    });
  };
  // 查询添加后列回显
  const queryWorkRecordByUserId = () => {
    setLoadingTable(true);
    dispatch({
      type: 'workOrderCenter/queryContactDetailByUserId',
      callback: response => {
        if (response.code == 200) {
          let workOrderTableHeaderListNew = response.data;
          setWorkOrderTableHeaderList(workOrderTableHeaderListNew); //  实际返回的列
          let defaultTableValueArray = response.data?.map(
            item => item.workRecordExtDefId,
          ); // 回显扩展列选项
          setDefaultTableValueArray(defaultTableValueArray);
          addCol(workOrderTableHeaderListNew);
          setLoadingTable(false);
        } else {
          setLoadingTable(false);
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  useEffect(() => {
    addCol(workOrderTableHeaderList);
  }, [sortedInfo]);
  // 动态添加表格列
  const addCol = workOrderTableHeaderList => {
    let addColArr = [];
    workOrderTableHeaderList?.map((item, index) => {
      if (item.workRecordExtDefCode == 'wordRecordCode') {
        addColArr.push({
          title: item.workRecordExtDefName,
          dataIndex: item.workRecordExtDefCode,
          key: item.workRecordExtDefCode,
          width: 180,
          // fixed: 'left',
          sorter: (a, b) => a.wordRecordCode - b.wordRecordCode,
          sortOrder:
            sortedInfo.columnKey === 'wordRecordCode' ? sortedInfo.order : null,
          ellipsis: true,
          render: (text, record) => {
            return (
              <CopyToClipboard
                text={text}
                onCopy={(_, result) => {
                  if (result) {
                    message.success(
                      getIntl().formatMessage({
                        id: 'work.order.detail.copy.success',
                        defaultValue: '复制成功',
                      }),
                    );
                  } else {
                    message.error(
                      getIntl().formatMessage({
                        id: 'work.order.detail.copy.error',
                        defaultValue: '复制失败，请稍后再试',
                      }),
                    );
                  }
                }}
              >
                <div className={styles.copyContent}>
                  {text}
                  <CopyIcon />
                </div>
              </CopyToClipboard>
            );
          },
        });
      } else if (item.workRecordExtDefCode == 'workRecordId') {
        addColArr.push({
          title: item.workRecordExtDefName,
          dataIndex: item.workRecordExtDefCode,
          key: item.workRecordExtDefCode,
          width: 150,
          // fixed: 'left',
          sorter: (a, b) => a.workRecordId.length - b.workRecordId.length,
          sortOrder:
            sortedInfo.columnKey === 'workRecordId' ? sortedInfo.order : null,
          ellipsis: true,
        });
      } else if (item.workRecordExtDefCode == 'channelTypeName') {
        addColArr.push({
          title: item.workRecordExtDefName,
          dataIndex: item.workRecordExtDefCode,
          key: item.workRecordExtDefCode,
          width: 200,
          sorter: (a, b) => a.channelTypeId - b.channelTypeId,
          sortOrder:
            sortedInfo.columnKey === 'channelTypeName'
              ? sortedInfo.order
              : null,
          render: (text, record) => {
            if (record.channelTypeName) {
              if (record.channelTypeId == '1') {
                return (
                  <div className={styles.channelName}>
                    <img src={TableEmailIcon} />
                    <span>{record.channelTypeName}</span>
                  </div>
                );
              } else if (record.channelTypeId == '7') {
                return (
                  <div className={styles.channelName}>
                    <img src={TablePhoneIcon} />
                    <span>{record.channelTypeName}</span>
                  </div>
                );
              } else if (record.channelTypeId == '3') {
                return (
                  <div className={styles.channelName}>
                    <img src={FacebookIcon} />
                    <span>{record.channelTypeName}</span>
                  </div>
                );
              } else if (record.channelTypeId == '2') {
                return (
                  <div className={styles.channelName}>
                    <img src={TableInfoIcon} />
                    <span>{record.channelTypeName}</span>
                  </div>
                );
              } else if (record.channelTypeId == '4') {
                return (
                  <div className={styles.channelName}>
                    <img src={WhatsAppIcon} />
                    <span>{record.channelTypeName}</span>
                  </div>
                );
              } else if (record.channelTypeId == '8') {
                return (
                  <div className={styles.channelName}>
                    <img src={ChatIcon} />
                    <span>{record.channelTypeName}</span>
                  </div>
                );
              } else if (record.channelTypeId == '9') {
                return (
                  <div className={styles.channelName}>
                    <img src={AppChatOutlinedIcon} />
                    <span>{record.channelTypeName}</span>
                  </div>
                );
              } else if (record.channelTypeId == '10') {
                return (
                  <div className={styles.channelName}>
                    <img src={WebVideoOutlinedIcon} />
                    <span>{record.channelTypeName}</span>
                  </div>
                );
              } else if (record.channelTypeId == '11') {
                return (
                  <div className={styles.channelName}>
                    <img src={AppVideoOutlinedIcon} />
                    <span>{record.channelTypeName}</span>
                  </div>
                );
              } else if (record.channelTypeId == '12') {
                return (
                  <div className={styles.channelName}>
                    <img src={AwsChannelIcon} />
                    <span>{record.channelTypeName}</span>
                  </div>
                );
              } else if (record.channelTypeId == '13') {
                return (
                  <div className={styles.channelName}>
                    <img src={NewInstagramIcon} />
                    <span>{record.channelTypeName}</span>
                  </div>
                );
              } else if (record.channelTypeId == '14') {
                return (
                  <div className={styles.channelName}>
                    <img src={LineIcon} />
                    <span>{record.channelTypeName}</span>
                  </div>
                );
              } else if (record.channelTypeId == '15') {
                return (
                  <div className={styles.channelName}>
                    <img src={NewWeComIcon} />
                    <span>{record.channelTypeName}</span>
                  </div>
                );
              } else if (record.channelTypeId == '16') {
                return (
                  <div className={styles.channelName}>
                    <img src={NewWechatOfficialAccountIcon} />
                    <span>{record.channelTypeName}</span>
                  </div>
                );
              } else if (record.channelTypeId == '17') {
                return (
                  <div className={styles.channelName}>
                    <img src={NewWebOnlineVoiceIcon} />
                    <span>{record.channelTypeName}</span>
                  </div>
                );
              } else if (record.channelTypeId == '18') {
                return (
                  <div className={styles.channelName}>
                    <img src={NewAppOnlineVoiceIcon} />
                    <span>{record.channelTypeName}</span>
                  </div>
                );
              } else if (record.channelTypeId == '19') {
                return (
                  <div className={styles.channelName}>
                    <img src={TwitterIcon} />
                    <span>{record.channelTypeName}</span>
                  </div>
                );
              } else if (record.channelTypeId == '20') {
                return (
                  <div className={styles.channelName}>
                    <img src={NewTelegramIcon} />
                    <span>{record.channelTypeName}</span>
                  </div>
                );
              } else if (record.channelTypeId == '21') {
                return (
                  <div className={styles.channelName}>
                    <img src={NewWeChatMiniProgramIcon} />
                    <span>{record.channelTypeName}</span>
                  </div>
                );
              } else if (record.channelTypeId == '22') {
                return (
                  <div className={styles.channelName}>
                    <img src={NewShopifyIcon} />
                    <span>{record.channelTypeName}</span>
                  </div>
                );
              } else if (record.channelTypeId == '23') {
                return (
                  <div className={styles.channelName}>
                    <img src={NewGooglePlayIcon} />
                    <span>{record.channelTypeName}</span>
                  </div>
                );
              } else if (record.channelTypeId == '24') {
                return (
                  <div className={styles.channelName}>
                    <img src={TikTokIcon} />
                    <span>{record.channelTypeName}</span>
                  </div>
                );
              } else if (record.channelTypeId == '25') {
                return (
                  <div className={styles.channelName}>
                    <img src={DiscordIcon} />
                    <span>{record.channelTypeName}</span>
                  </div>
                );
              } else {
                return (
                  <div className={styles.channelName}>
                    <span>{record.channelTypeName}</span>
                  </div>
                );
              }
            } else {
              return null;
            }
          },
        });
      } else if (item.workRecordExtDefCode == 'priorityLevelName') {
        addColArr.push({
          title: item.workRecordExtDefName,
          dataIndex: item.workRecordExtDefCode,
          key: item.workRecordExtDefCode,
          width: 150,
          sorter: (a, b) => a.priorityLevelId - b.priorityLevelId,
          sortOrder:
            sortedInfo.columnKey === 'priorityLevelName'
              ? sortedInfo.order
              : null,
          ellipsis: true,
          render: (text, record) => {
            let priorityLevelId = record.priorityLevelId;
            if (priorityLevelId == '1001') {
              return (
                <div className={styles.priority1}>
                  {record.priorityLevelName}
                </div>
              );
            } else if (priorityLevelId == '1002') {
              return (
                <div className={styles.priority1}>
                  {record.priorityLevelName}
                </div>
              );
            } else if (priorityLevelId == '1003') {
              return (
                <div className={styles.priority3}>
                  {record.priorityLevelName}
                </div>
              );
            } else if (priorityLevelId == '1004') {
              return (
                <div className={styles.priority3}>
                  {record.priorityLevelName}
                </div>
              );
            } else if (priorityLevelId == '1005') {
              return (
                <div className={styles.priority5}>
                  {record.priorityLevelName}
                </div>
              );
            }
          },
        });
      } else if (item.workRecordExtDefCode == 'serviceObjectives') {
        addColArr.push({
          title: item.workRecordExtDefName,
          dataIndex: item.workRecordExtDefCode,
          key: item.workRecordExtDefCode,
          width: 270,
          ellipsis: true,
          render: (text, record) => {
            return (
              <div
                dangerouslySetInnerHTML={{ __html: record.serviceObjectives }}
              ></div>
            );
          },
        });
      } else if (item.workRecordExtDefCode == 'status') {
        addColArr.push({
          title: item.workRecordExtDefName,
          dataIndex: item.workRecordExtDefCode,
          key: item.workRecordExtDefCode,
          width: 200,
          ellipsis: true,
          render: (text, record) => {
            let status = record.status;
            if (status == 0) {
              return (
                <div className={styles.state5}>
                  <div className={styles.circle}></div>
                  <FormattedMessage
                    id="work.order.management.table.status.text1"
                    defaultMessage="待分配"
                  />
                </div>
              );
            } else if (status == 1) {
              return (
                <div className={styles.state3}>
                  <div className={styles.circle}></div>
                  <FormattedMessage
                    id="work.order.management.table.status.text2"
                    defaultMessage="待客服处理"
                  />
                </div>
              );
            } else if (status == 2) {
              return (
                <div className={styles.state3}>
                  <div className={styles.circle}></div>
                  <FormattedMessage
                    id="work.order.management.table.status.text3"
                    defaultMessage="待客户回复"
                  />
                </div>
              );
            } else if (status == 3) {
              return (
                <div className={styles.state1}>
                  <div className={styles.circle}></div>
                  <FormattedMessage
                    id="work.order.management.table.status.text4"
                    defaultMessage="已解决"
                  />
                </div>
              );
            } else if (status == 4) {
              return (
                <div className={styles.state2}>
                  <div className={styles.circle}></div>
                  <FormattedMessage
                    id="work.order.management.table.status.text5"
                    defaultMessage="已终止"
                  />
                </div>
              );
            } else if (status == 5) {
              return (
                <div className={styles.state4}>
                  <div className={styles.circle}></div>
                  <FormattedMessage
                    id="work.order.management.table.status.text6"
                    defaultMessage="已转单"
                  />
                </div>
              );
            }
          },
        });
      } else if (item.workRecordExtDefCode == 'createType') {
        addColArr.push({
          title: item.workRecordExtDefName,
          dataIndex: item.workRecordExtDefCode,
          key: item.workRecordExtDefCode,
          width: 150,
          render: (text, record) => {
            let createType = record.createType;
            if (createType == 1) {
              return (
                <div>
                  <FormattedMessage
                    id="work.order.detail.info.8"
                    defaultMessage="手动创建"
                  />
                </div>
              );
            } else if (createType == 0) {
              return (
                <div>
                  <FormattedMessage
                    id="work.order.detail.info.7"
                    defaultMessage="自动创建"
                  />
                </div>
              );
            }
          },
        });
      } else if (item.workRecordExtDefCode == 'reminderStatus') {
        // 催单
        addColArr.push({
          title: '催单状态',
          dataIndex: item.workRecordExtDefCode,
          key: item.workRecordExtDefCode,
          sorter: (a, b) => a.reminderStatus - b.reminderStatus,
          sortOrder:
            sortedInfo.columnKey === 'reminderStatus' ? sortedInfo.order : null,
          ellipsis: true,
          onCell: record => ({
            className:
              record.reminderStatus == 1 &&
              sortedInfo.columnKey === 'reminderStatus'
                ? 'reminderStatus'
                : ' ',
          }),
        });
      } else if (item.workRecordExtDefCode == 'customerName') {
        addColArr.push({
          title: item.workRecordExtDefName,
          dataIndex: item.workRecordExtDefCode,
          key: item.workRecordExtDefCode,
          width: 200,
          ellipsis: true,
          render: (text, record) => {
            return (
              <CopyToClipboard
                text={text}
                onCopy={(_, result) => {
                  if (result) {
                    message.success(
                      getIntl().formatMessage({
                        id: 'work.order.detail.copy.success',
                        defaultValue: '复制成功',
                      }),
                    );
                  } else {
                    message.error(
                      getIntl().formatMessage({
                        id: 'work.order.detail.copy.error',
                        defaultValue: '复制失败，请稍后再试',
                      }),
                    );
                  }
                }}
              >
                <div className={styles.copyContent}>
                  {text}
                  <CopyIcon />
                </div>
              </CopyToClipboard>
            );
          },
        });
      } else if (item.workRecordExtDefCode == 'customerTelephone') {
        addColArr.push({
          title: item.workRecordExtDefName,
          dataIndex: item.workRecordExtDefCode,
          key: item.workRecordExtDefCode,
          width: 200,
          ellipsis: true,
          render: (text, record) => {
            return (
              <CopyToClipboard
                text={text}
                onCopy={(_, result) => {
                  if (result) {
                    message.success(
                      getIntl().formatMessage({
                        id: 'work.order.detail.copy.success',
                        defaultValue: '复制成功',
                      }),
                    );
                  } else {
                    message.error(
                      getIntl().formatMessage({
                        id: 'work.order.detail.copy.error',
                        defaultValue: '复制失败，请稍后再试',
                      }),
                    );
                  }
                }}
              >
                <div className={styles.copyContent}>
                  {text}
                  <CopyIcon />
                </div>
              </CopyToClipboard>
            );
          },
        });
      } else if (item.workRecordExtDefCode == 'agentName') {
        addColArr.push({
          title: item.workRecordExtDefName,
          dataIndex: item.workRecordExtDefCode,
          key: item.workRecordExtDefCode,
          width: 150,
          ellipsis: true,
          sorter: (a, b) => {
            if (a.agentName) {
              if (b.agentName) {
                let aPinyinArray = pinyin(a.agentName, {
                  pattern: 'first',
                  toneType: 'none',
                  type: 'array',
                });
                let bPinyinArray = pinyin(b.agentName, {
                  pattern: 'first',
                  toneType: 'none',
                  type: 'array',
                });
                return aPinyinArray[0].localeCompare(bPinyinArray[0]);
              } else {
                let aPinyinArray = pinyin(a.agentName, {
                  pattern: 'first',
                  toneType: 'none',
                  type: 'array',
                });
                let bPinyinArray = ['z'];
                return aPinyinArray[0].localeCompare(bPinyinArray[0]);
              }
            } else {
              if (b.agentName) {
                let aPinyinArray = ['z'];
                let bPinyinArray = pinyin(b.agentName, {
                  pattern: 'first',
                  toneType: 'none',
                  type: 'array',
                });
                return aPinyinArray[0].localeCompare(bPinyinArray[0]);
              } else {
                let aPinyinArray = ['z'];
                let bPinyinArray = ['z'];
                return aPinyinArray[0].localeCompare(bPinyinArray[0]);
              }
            }
          },
          sortOrder:
            sortedInfo.columnKey === 'agentName' ? sortedInfo.order : null,
          render: (text, record) => {
            return (
              <CopyToClipboard
                text={text}
                onCopy={(_, result) => {
                  if (result) {
                    message.success(
                      getIntl().formatMessage({
                        id: 'work.order.detail.copy.success',
                        defaultValue: '复制成功',
                      }),
                    );
                  } else {
                    message.error(
                      getIntl().formatMessage({
                        id: 'work.order.detail.copy.error',
                        defaultValue: '复制失败，请稍后再试',
                      }),
                    );
                  }
                }}
              >
                <div className={styles.copyContent}>
                  {text}
                  <CopyIcon />
                </div>
              </CopyToClipboard>
            );
          },
        });
      } else if (item.workRecordExtDefCode == 'createTime') {
        addColArr.push({
          title: item.workRecordExtDefName,
          dataIndex: item.workRecordExtDefCode,
          key: item.workRecordExtDefCode,
          width: 150,
          ellipsis: true,
          defaultSortOrder: 'descend',
          sorter: (a, b) => {
            let timestamp1 = Date.parse(new Date(a.createTime));
            let timestamp2 = Date.parse(new Date(b.createTime));
            return timestamp1 - timestamp2;
          },
          sortOrder:
            sortedInfo.columnKey === 'createTime' ? sortedInfo.order : null,
        });
      } else if (item.workRecordExtDefCode == 'modifyTime') {
        addColArr.push({
          title: item.workRecordExtDefName,
          dataIndex: item.workRecordExtDefCode,
          key: item.workRecordExtDefCode,
          width: 150,
          ellipsis: true,
          defaultSortOrder: 'descend',
          sorter: (a, b) => {
            let timestamp1 = Date.parse(new Date(a.modifyTime));
            let timestamp2 = Date.parse(new Date(b.modifyTime));
            return timestamp1 - timestamp2;
          },
          sortOrder:
            sortedInfo.columnKey === 'modifyTime' ? sortedInfo.order : null,
        });
      } else if (item.workRecordExtDefCode == 'resolveTime') {
        addColArr.push({
          title: item.workRecordExtDefName,
          dataIndex: item.workRecordExtDefCode,
          key: item.workRecordExtDefCode,
          width: 150,
          ellipsis: true,
          // defaultSortOrder: 'descend',
          sorter: (a, b) => {
            let timestamp1 = Date.parse(new Date(a.resolveTime));
            let timestamp2 = Date.parse(new Date(b.resolveTime));
            return timestamp1 - timestamp2;
          },
          sortOrder:
            sortedInfo.columnKey === 'resolveTime' ? sortedInfo.order : null,
        });
      } else {
        addColArr.push({
          title: item.workRecordExtDefName,
          dataIndex: item.workRecordExtDefCode,
          key: item.workRecordExtDefCode,
          width: 150,
          ellipsis: true,
        });
      }
    });
    let newColumns = [...defaultColumns, ...addColArr];
    setColumns(newColumns);
    // 需要调查询接口
    // handleSearch();
    // setDefaultColumns(newColArr)
  };

  // 排序
  const onChangeStore = (pagination, filters, sorter) => {
    if (pagination.pageSize === pageSize && pagination.current === pageNum) {
      let sortedInfo1 = {
        order: sorter.order,
        columnKey: sorter.columnKey,
      };
      setSortedInfo(sortedInfo1);
    }
  };
  return (
    <Spin spinning={spinning}>
      <div className={styles.dataDetailsContent}>
        <div className={styles.headerContent}>
          <p className="blueBorder">
            <FormattedMessage
              id="statistics.data.details.title"
              defaultMessage="联络明细"
            />
          </p>
          {/* 默认查询 */}
          <div className={styles.defaultSelectContent}>
            <p className={styles.updataDateLabel}>
              <FormattedMessage
                id="statistics.data.details.select.date"
                defaultMessage="选择日期："
              />
            </p>
            <RangePicker
              defaultValue={[
                moment(startDate, 'YYYY-MM-DD HH:mm:ss'),
                moment(endDate, 'YYYY-MM-DD HH:mm:ss'),
              ]}
              onOpenChange={onOpenChange}
              onChange={rangePickerChange}
              showTime
            />
            <p className={styles.updataDateLabel}>
              <FormattedMessage
                id="statistics.data.details.channel.type"
                defaultMessage="渠道类型："
              />
            </p>
            {/* <Select
              popupClassName="selectFilterContent"
              allowClear={true}
              onChange={e => handleChangeSelect(e)}
              placeholder={getIntl().formatMessage({
                id: 'email.channel.configuration.channel.type.placeholder',
                defaultValue: '请选择渠道类型',
              })}
            >
              {channelTypeList?.map(items => {
                // 联络明细下拉列表只需要电话、在线聊天、在线视频五种
                // if (items.code == '1') {
                //   return (
                //     <Option value={items.code} key={items.code}>
                //       <img src={EmailIcon} />
                //       {items.name}
                //     </Option>
                //   );
                // } else if (items.code == '3') {
                //   return (
                //     <Option value={items.code} key={items.code}>
                //       <img src={FacebookIcon} />
                //       {items.name}
                //     </Option>
                //   );
                // } else if (items.code == '4') {
                //   return (
                //     <Option value={items.code} key={items.code}>
                //       <img src={WhatsAppIcon} />
                //       {items.name}
                //     </Option>
                //   );
                // } else if (items.code == '5') {
                //   return (
                //     <Option value={items.code} key={items.code}>
                //       <img src={TwitterIcon} />
                //       {items.name}
                //     </Option>
                //   );
                // } else if (items.code == '6') {
                //   return (
                //     <Option value={items.code} key={items.code}>
                //       <img src={LineIcon} /> {items.name}
                //     </Option>
                //   );
                // }
                if (items.code == '7') {
                  return (
                    <Option value={items.code} key={items.code}>
                      <img src={PhoneIcon} /> {items.name}
                    </Option>
                  );
                } else if (items.code == '8') {
                  return (
                    <Option value={items.code} key={items.code}>
                      <img src={ChatIcon} />
                      {items.name}
                    </Option>
                  );
                } else if (items.code == '9') {
                  return (
                    <Option value={items.code} key={items.code}>
                      <img src={AppChatOutlinedIcon} />
                      {items.name}
                    </Option>
                  );
                } else if (items.code == '10') {
                  return (
                    <Option value={items.code} key={items.code}>
                      <img src={WebVideoOutlinedIcon} />
                      {items.name}
                    </Option>
                  );
                } else if (items.code == '11') {
                  return (
                    <Option value={items.code} key={items.code}>
                      <img src={AppVideoOutlinedIcon} />
                      {items.name}
                    </Option>
                  );
                }
                // if (items.code == '12') {
                //   return (
                //     <Option value={items.code} key={items.code}>
                //       <img src={AwsChannelIcon} />
                //       {items.name}
                //     </Option>
                //   );
                // } else if (items.code == '13') {
                //   return (
                //     <Option value={items.code} key={items.code}>
                //       <img src={NewInstagramIcon} />
                //       {items.name}
                //     </Option>
                //   );
                // } else if (items.code == '14') {
                //   return (
                //     <Option value={items.code} key={items.code}>
                //       <img src={NewLineIcon} />
                //       {items.name}
                //     </Option>
                //   );
                // } else if (items.code == '15') {
                //   return (
                //     <Option value={items.code} key={items.code}>
                //       <img src={NewWeComIcon} />
                //       {items.name}
                //     </Option>
                //   );
                // } else if (items.code == '16') {
                //   return (
                //     <Option value={items.code} key={items.code}>
                //       <img src={NewWechatOfficialAccountIcon} />
                //       {items.name}
                //     </Option>
                //   );
                // } else if (items.code == '17') {
                //   return (
                //     <Option value={items.code} key={items.code}>
                //       <img src={NewWebOnlineVoiceIcon} />
                //       {items.name}
                //     </Option>
                //   );
                // } else if (items.code == '18') {
                //   return (
                //     <Option value={items.code} key={items.code}>
                //       <img src={NewAppOnlineVoiceIcon} />
                //       {items.name}
                //     </Option>
                //   );
                // } else if (items.code == '19') {
                //   return (
                //     <Option value={items.code} key={items.code}>
                //       <img src={NewTwitterIcon} />
                //       {items.name}
                //     </Option>
                //   );
                // } else if (items.code == '20') {
                //   return (
                //     <Option value={items.code} key={items.code}>
                //       <img src={NewTelegramIcon} />
                //       {items.name}
                //     </Option>
                //   );
                // } else if (items.code == '21') {
                //   return (
                //     <Option value={items.code} key={items.code}>
                //       <img src={NewWeChatMiniProgramIcon} />
                //       {items.name}
                //     </Option>
                //   );
                // } else if (items.code == '22') {
                //   return (
                //     <Option value={items.code} key={items.code}>
                //       <img src={NewShopifyIcon} />
                //       {items.name}
                //     </Option>
                //   );
                // } else if (items.code == '23') {
                //   return (
                //     <Option value={items.code} key={items.code}>
                //       <img src={NewGooglePlayIcon} />
                //       {items.name}
                //     </Option>
                //   );
                // } else {
                //   return (
                //     <Option value={items.code} key={items.code}>
                //       {items.name}
                //     </Option>
                //   );
                // }
              })}
            </Select> */}
            <ChannelTypeSelect
              onChange={e => handleChangeSelect(e)}
              channelTypeList={channelTypeList}
              popupClassName="selectFilterContent"
            />
            <p className={styles.updataDateLabel}>
              <FormattedMessage
                id="statistics.data.details.contact.id"
                defaultMessage="联系ID"
              />
            </p>
            <Input
              // value={multiFieldQuery}
              // style={{ marginRight: '100px' }}
              placeholder={getIntl().formatMessage({
                id: 'statistics.data.details.search.tips',
              })}
              prefix={<Search />}
              onPressEnter={e => {
                setMultiFieldQuery(e.target.value);
                setPageNum(1);
              }}
              onBlur={e => {
                setMultiFieldQuery(e.target.value);
                setPageNum(1);
              }}
            />
          </div>
          <div className={styles.defaultSelectContent}>
            <p className={styles.updataDateLabel}>
              <FormattedMessage id="email.channel.configuration.calling.lines.label" />
            </p>
            <Select
              style={{ width: '26%' }}
              placeholder={getIntl().formatMessage({
                id: 'email.channel.configuration.calling.lines.placeholder',
                defaultValue: '请选择联络线路',
              })}
              value={connectLine}
              // options={connectList}
              showSearch
              filterOption={(inputValue, option) =>
                option.label.toLowerCase().indexOf(inputValue.toLowerCase()) >=
                0
              }
              onChange={(value, options) =>
                handleChangeSelectCallingLine(value, options)
              }
              allowClear
            >
              {connectList?.map(group => (
                <OptGroup
                  key={group.voiceSupplierId}
                  label={group.voiceSupplierAlias}
                >
                  {group.contactLineContentList.map(option => (
                    <Option
                      key={option.connectLineId}
                      value={option.connectLineId}
                      label={option.alias}
                      voiceSupplierRealName={option.voiceSupplierRealName}
                    >
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                        }}
                      >
                        {/*<div*/}
                        {/*  style={{*/}
                        {/*    width: '12px',*/}
                        {/*    height: '12px',*/}
                        {/*    backgroundColor: 'red',*/}
                        {/*    marginRight: '4px',*/}
                        {/*  }}*/}
                        {/*></div>*/}
                        <span>{option.alias}</span>
                      </div>
                    </Option>
                  ))}
                </OptGroup>
              ))}
            </Select>
          </div>
          {/* 动态查询 */}
          <div>
            <Spin spinning={formLoading}>
              <div className={styles.selectItemContent}>
                <Form
                  name="basic"
                  autoComplete="off"
                  labelAlign="right"
                  // ref={this.formSearchFilterRef}
                >
                  {filterFormList?.map((item, index) => {
                    if (item.propertyTypeId == '1001') {
                      // 1001,单行输入框
                      return (
                        <div className={styles.selectItem}>
                          <Form.Item
                            label={item.workRecordExtDefName}
                            name={item.workRecordExtDefCode}
                          >
                            <Input
                              placeholder={getIntl().formatMessage({
                                id: 'work.order.management.tips',
                                defaultMessage: '请输入',
                              })}
                              // value={item.workRecordExtDefValue}
                              onChange={e =>
                                handleAddInputChange(e, item, index)
                              }
                              name={item.workRecordExtDefCode}
                            />
                          </Form.Item>
                        </div>
                      );
                    } else if (item.propertyTypeId == '1002') {
                      // 1002,多行输入框
                      return (
                        <div className={styles.selectItem}>
                          <Form.Item
                            label={item.workRecordExtDefName}
                            name={item.workRecordExtDefCode}
                          >
                            <Input
                              placeholder={getIntl().formatMessage({
                                id: 'work.order.management.tips',
                                defaultMessage: '请输入',
                              })}
                              onChange={e =>
                                handleAddInputChange(e, item, index)
                              }
                              name={item.workRecordExtDefCode}
                            />
                          </Form.Item>
                        </div>
                      );
                    } else if (item.propertyTypeId == '1003') {
                      if (item.workRecordExtDefCode == 'channelTypeName') {
                        // 1003,单选下拉框 渠道类型
                        return (
                          <div className={styles.selectItem}>
                            <Form.Item
                              label={item.workRecordExtDefName}
                              name={item.workRecordExtDefCode}
                            >
                              {/* <Select
                                popupClassName="selectFilterContent"
                                onChange={value =>
                                  handleChangeSelectChannelType(
                                    value,
                                    item,
                                    index,
                                  )
                                }
                              >
                                {item.workOrderExtOptionDefList?.map(items => {
                                  if (items.optionValue == '1') {
                                    return (
                                      <Option
                                        value={items.optionValue}
                                        key={items.optionValue}
                                      >
                                        <img src={EmailIcon} />
                                        {items.optionName}
                                      </Option>
                                    );
                                  } else if (items.optionValue == '3') {
                                    return (
                                      <Option
                                        value={items.optionValue}
                                        key={items.optionValue}
                                      >
                                        <img src={FacebookIcon} />
                                        {items.optionName}
                                      </Option>
                                    );
                                  } else if (items.optionValue == '4') {
                                    return (
                                      <Option
                                        value={items.optionValue}
                                        key={items.optionValue}
                                      >
                                        <img src={WhatsAppIcon} />
                                        {items.optionName}
                                      </Option>
                                    );
                                  } else if (items.optionValue == '5') {
                                    return (
                                      <Option
                                        value={items.optionValue}
                                        key={items.optionValue}
                                      >
                                        <img src={TwitterIcon} />{' '}
                                        {items.optionName}
                                      </Option>
                                    );
                                  } else if (items.optionValue == '6') {
                                    return (
                                      <Option
                                        value={items.optionValue}
                                        key={items.optionValue}
                                      >
                                        <img src={LineIcon} />{' '}
                                        {items.optionName}
                                      </Option>
                                    );
                                  } else if (items.optionValue == '7') {
                                    return (
                                      <Option
                                        value={items.optionValue}
                                        key={items.optionValue}
                                      >
                                        <img src={PhoneIcon} />{' '}
                                        {items.optionName}
                                      </Option>
                                    );
                                  } else if (items.optionValue == '8') {
                                    return (
                                      <Option
                                        value={items.optionValue}
                                        key={items.optionValue}
                                      >
                                        <img src={ChatIcon} />
                                        {items.optionName}
                                      </Option>
                                    );
                                  } else if (items.optionValue == '9') {
                                    return (
                                      <Option
                                        value={items.optionValue}
                                        key={items.optionValue}
                                      >
                                        <img src={AppChatOutlinedIcon} />
                                        {items.optionName}
                                      </Option>
                                    );
                                  } else if (items.optionValue == '10') {
                                    return (
                                      <Option
                                        value={items.optionValue}
                                        key={items.optionValue}
                                      >
                                        <img src={WebVideoOutlinedIcon} />
                                        {items.optionName}
                                      </Option>
                                    );
                                  } else if (items.optionValue == '11') {
                                    return (
                                      <Option
                                        value={items.optionValue}
                                        key={items.optionValue}
                                      >
                                        <img src={AppVideoOutlinedIcon} />
                                        {items.optionName}
                                      </Option>
                                    );
                                  } else if (items.optionValue == '12') {
                                    return (
                                      <Option
                                        value={items.optionValue}
                                        key={items.optionValue}
                                      >
                                        <img src={AwsChannelIcon} />
                                        {items.optionName}
                                      </Option>
                                    );
                                  } else if (items.optionValue == '13') {
                                    return (
                                      <Option
                                        value={items.optionValue}
                                        key={items.optionValue}
                                      >
                                        <img src={NewInstagramIcon} />
                                        {items.optionName}
                                      </Option>
                                    );
                                  } else if (items.optionValue == '14') {
                                    return (
                                      <Option
                                        value={items.optionValue}
                                        key={items.optionValue}
                                      >
                                        <img src={NewLineIcon} />
                                        {items.optionName}
                                      </Option>
                                    );
                                  } else if (items.optionValue == '15') {
                                    return (
                                      <Option
                                        value={items.optionValue}
                                        key={items.optionValue}
                                      >
                                        <img src={NewWeComIcon} />
                                        {items.optionName}
                                      </Option>
                                    );
                                  } else if (items.optionValue == '16') {
                                    return (
                                      <Option
                                        value={items.optionValue}
                                        key={items.optionValue}
                                      >
                                        <img
                                          src={NewWechatOfficialAccountIcon}
                                        />
                                        {items.optionName}
                                      </Option>
                                    );
                                  } else if (items.optionValue == '17') {
                                    return (
                                      <Option
                                        value={items.optionValue}
                                        key={items.optionValue}
                                      >
                                        <img src={NewWebOnlineVoiceIcon} />
                                        {items.optionName}
                                      </Option>
                                    );
                                  } else if (items.optionValue == '18') {
                                    return (
                                      <Option
                                        value={items.optionValue}
                                        key={items.optionValue}
                                      >
                                        <img src={NewAppOnlineVoiceIcon} />
                                        {items.optionName}
                                      </Option>
                                    );
                                  } else if (items.optionValue == '19') {
                                    return (
                                      <Option
                                        value={items.optionValue}
                                        key={items.optionValue}
                                      >
                                        <img src={NewTwitterIcon} />
                                        {items.optionName}
                                      </Option>
                                    );
                                  } else if (items.optionValue == '20') {
                                    return (
                                      <Option
                                        value={items.optionValue}
                                        key={items.optionValue}
                                      >
                                        <img src={NewTelegramIcon} />
                                        {items.optionName}
                                      </Option>
                                    );
                                  } else if (items.optionValue == '21') {
                                    return (
                                      <Option
                                        value={items.optionValue}
                                        key={items.optionValue}
                                      >
                                        <img src={NewWeChatMiniProgramIcon} />
                                        {items.optionName}
                                      </Option>
                                    );
                                  } else if (items.optionValue == '22') {
                                    return (
                                      <Option
                                        value={items.optionValue}
                                        key={items.optionValue}
                                      >
                                        <img src={NewShopifyIcon} />
                                        {items.optionName}
                                      </Option>
                                    );
                                  } else if (items.optionValue == '23') {
                                    return (
                                      <Option
                                        value={items.optionValue}
                                        key={items.optionValue}
                                      >
                                        <img src={NewGooglePlayIcon} />
                                        {items.optionName}
                                      </Option>
                                    );
                                  } else {
                                    return (
                                      <Option
                                        value={items.optionValue}
                                        key={items.optionValue}
                                      >
                                        {items.optionName}
                                      </Option>
                                    );
                                  }
                                })}
                              </Select> */}
                              <ChannelTypeSelect
                                channelTypeList={item.workOrderExtOptionDefList}
                                onChange={value =>
                                  handleChangeSelectChannelType(
                                    value,
                                    item,
                                    index,
                                  )
                                }
                                valueKey="optionValue"
                                labelKey="optionName"
                                popupClassName="selectFilterContent"
                              />
                            </Form.Item>
                          </div>
                        );
                      } else {
                        // 1003,单选下拉框
                        return (
                          <div className={styles.selectItem}>
                            <Form.Item
                              label={item.workRecordExtDefName}
                              name={item.workRecordExtDefCode}
                            >
                              <Select
                                showSearch
                                popupClassName="selectFilterContent"
                                fieldNames={{
                                  label: 'optionName',
                                  value: 'optionValue',
                                  key: 'optionValue',
                                }}
                                filterOption={(inputValue, option) =>
                                  option.optionName
                                    .toLowerCase()
                                    .indexOf(inputValue.toLowerCase()) >= 0
                                }
                                options={item.workOrderExtOptionDefList}
                                onChange={value =>
                                  handleChangeSelectChannelType(
                                    value,
                                    item,
                                    index,
                                  )
                                }
                              />
                            </Form.Item>
                          </div>
                        );
                      }
                    } else if (item.propertyTypeId == '1004') {
                      // 1004,多选下拉框
                      return (
                        <div className={styles.selectItem}>
                          <Form.Item
                            label={item.workRecordExtDefName}
                            name={item.workRecordExtDefCode}
                          >
                            <Select
                              showSearch
                              mode="multiple"
                              popupClassName="selectFilterContent"
                              filterOption={(inputValue, option) =>
                                option.optionName
                                  .toLowerCase()
                                  .indexOf(inputValue.toLowerCase()) >= 0
                              }
                              fieldNames={{
                                label: 'optionName',
                                value: 'optionValue',
                                key: 'optionValue',
                              }}
                              options={item.workOrderExtOptionDefList}
                              onChange={value =>
                                handleChangeMultipleSelect(value, item, index)
                              }
                            />
                          </Form.Item>
                        </div>
                      );
                    } else if (item.propertyTypeId == '1005') {
                      // 1005,单选框
                      return (
                        <div className={styles.selectItem}>
                          <Form.Item
                            label={item.workRecordExtDefName}
                            name={item.workRecordExtDefCode}
                          >
                            <Radio.Group
                              onChange={e => handleChangeRadio(e, item, index)}
                            >
                              {item.workOrderExtOptionDefList?.map(item => {
                                return (
                                  <Radio
                                    key={item.optionValue}
                                    value={item.optionValue}
                                  >
                                    {item.optionName}
                                  </Radio>
                                );
                              })}
                            </Radio.Group>
                          </Form.Item>
                        </div>
                      );
                    } else if (item.propertyTypeId == '1006') {
                      // 1006,多选框
                      return (
                        <div className={styles.selectItem}>
                          <Form.Item
                            label={item.workRecordExtDefName}
                            name={item.workRecordExtDefCode}
                          >
                            <Checkbox.Group
                              onChange={checkedValues =>
                                handleChangeCheckbox(checkedValues, item, index)
                              }
                            >
                              {item.workOrderExtOptionDefList?.map(item => {
                                return (
                                  <Checkbox
                                    key={item.optionValue}
                                    value={item.optionValue}
                                  >
                                    {item.optionName}
                                  </Checkbox>
                                );
                              })}
                            </Checkbox.Group>
                          </Form.Item>
                        </div>
                      );
                    } else if (item.propertyTypeId == '1007') {
                      // 1007,开关
                      return (
                        <div className={styles.selectItem}>
                          <Form.Item
                            label={item.workRecordExtDefName}
                            name={item.workRecordExtDefCode}
                          >
                            <Switch
                              defaultChecked={item.switch}
                              onChange={checked =>
                                handleChangeSwitch(checked, item, index)
                              }
                            />
                          </Form.Item>
                        </div>
                      );
                    } else if (item.propertyTypeId == '1008') {
                      // 1008,时间选择
                      return (
                        <div className={styles.selectItem}>
                          <Form.Item
                            label={item.workRecordExtDefName}
                            name={item.workRecordExtDefCode}
                          >
                            <DatePicker
                              format="YYYY-MM-DD HH:mm:ss"
                              onChange={(date, dateString) =>
                                handleSelectTimeChange(
                                  date,
                                  dateString,
                                  item,
                                  index,
                                )
                              }
                              showTime
                            />
                          </Form.Item>
                        </div>
                      );
                    } else if (item.propertyTypeId == '1009') {
                      // 1009,时间范围选择
                      return (
                        <div className={styles.selectItem}>
                          <Form.Item
                            label={item.workRecordExtDefName}
                            name={item.workRecordExtDefCode}
                          >
                            <RangePicker
                              onChange={(value, dateString) =>
                                handleSelectRangeChange(
                                  value,
                                  dateString,
                                  item,
                                  index,
                                )
                              }
                              showTime
                              format="YYYY-MM-DD HH:mm:ss"
                            />
                          </Form.Item>
                        </div>
                      );
                    }
                  })}
                </Form>
              </div>
            </Spin>
          </div>

          {/* 添加更多列字段 */}
          <div className={styles.headerBtn}>
            <div
              title={getIntl().formatMessage({
                id: 'work.order.reset.filter',
                defaultValue: '重置筛选器',
              })}
              onClick={() => resetFilter()}
              className={styles.resetting}
            ></div>
            <Popover
              content={() => {
                return (
                  <div>
                    <div className="moreFieldList">
                      <Checkbox.Group
                        value={defaultValueArray}
                        onChange={e => onChangeCheckbox(e)}
                      >
                        {filterWorkOrderExtList?.map(item => {
                          return (
                            <Checkbox
                              value={item.workRecordExtDefId}
                              key={item.workRecordExtDefId}
                            >
                              {item.workRecordExtDefName}
                            </Checkbox>
                          );
                        })}
                      </Checkbox.Group>
                    </div>
                    <div className="moreFieldBtn">
                      <Button onClick={hide}>
                        <FormattedMessage id="work.order.management.btn.cancel" />
                      </Button>
                      <Button type="primary" onClick={addMoreField}>
                        <FormattedMessage id="work.order.management.btn.sure" />
                      </Button>
                    </div>
                  </div>
                );
              }}
              overlayClassName="moreFieldsContentPopover"
              title={null}
              trigger="click"
              open={openMore}
              onOpenChange={() => handleOpenChange()}
            >
              <Button icon={<PlusOutlined />} className={styles.moreOption}>
                <FormattedMessage id="work.order.management.more.fields" />
              </Button>
            </Popover>
            <Button
              className={styles.searchOperation}
              onClick={() => handleSearch()}
              type="primary"
              icon={<SearchOutlined />}
            >
              <FormattedMessage id="work.order.management.search" />
            </Button>
          </div>
        </div>

        <div className={styles.tableContent}>
          <div className={styles.tableBtn}>
            <Popover
              content={() => {
                return (
                  <div>
                    <div className="moreFieldList">
                      <Checkbox.Group
                        value={defaultTableValueArray}
                        onChange={e => onChangeCheckboxTbale(e)}
                      >
                        {filterTableColumnWorkOrderExtList?.map(item => {
                          return (
                            <Checkbox
                              value={item.workRecordExtDefId}
                              key={item.workRecordExtDefId}
                            >
                              {item.workRecordExtDefName}
                            </Checkbox>
                          );
                        })}
                      </Checkbox.Group>
                    </div>
                    <div className="moreFieldBtn">
                      <Button onClick={() => tableColumnHide()}>
                        <FormattedMessage id="work.order.management.btn.cancel" />
                      </Button>
                      <Button
                        type="primary"
                        onClick={() => tableColumnAddMoreField()}
                      >
                        <FormattedMessage id="work.order.management.btn.sure" />
                      </Button>
                    </div>
                  </div>
                );
              }}
              overlayClassName="moreFieldsContentPopover"
              title={null}
              trigger="click"
              open={openMoreTableColumn}
              onOpenChange={() => handleOpenChangeTableColumn()}
            >
              <Button
                icon={<ColumnOptionsIcon />}
                className={styles.columnOption}
              >
                <FormattedMessage id="work.order.management.column" />
              </Button>
            </Popover>
            <Button
              loading={exportLoading}
              className={styles.batchExport}
              onClick={() => handleBatchExport()}
              icon={<BatchExportIcon />}
            >
              <FormattedMessage id="work.order.management.batch.export" />
            </Button>
          </div>
          <Table
            scroll={{ x: 4500 }}
            dataSource={dataSource}
            columns={columns}
            loading={loadingTable}
            onChange={onChangeStore}
            pagination={{
              total: total,
              pageSize: pageSize,
              current: pageNum,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showTotal: total => (
                <FormattedMessage
                  id="page.total.num"
                  defaultMessage={`共 ${total} 条`}
                  values={{ total }}
                />
              ),
              onChange: (pageNum, pageSize) => {
                setPageNum(pageNum);
                setPageSize(pageSize);
              },
            }}
          />
        </div>
      </div>
    </Spin>
  );
};

export default DataDetailsContent;
