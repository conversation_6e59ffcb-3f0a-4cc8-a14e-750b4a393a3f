import React, {
  Component,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  connect,
  useDispatch,
  useSelector,
  getIntl,
  FormattedMessage,
  getLocale,
  history,
} from 'umi';
import { ReactComponent as DeleteHome } from '@/assets/deleteHome.svg';

import {
  MoreOutlined,
  EditOutlined,
  MinusSquareOutlined,
  CheckOutlined,
  CloseOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import {
  Input,
  Button,
  Select,
  Steps,
  Form,
  Spin,
  TreeSelect,
  Row,
  Col,
  Modal,
  Radio,
  Checkbox,
} from 'antd';
import createOrganiza from '@/assets/createOrganiza.png';
import styles from './index.less';
import { notification, generatePassword } from '@/utils/utils';
import { queryTwilioList } from '../../../service/channel';

export default () => {
  const dispatch = useDispatch();
  const { connectList, deptList, user, authAccess } = useSelector(
    ({ layouts }) => ({
      connectList: layouts.connectList,
      deptList: layouts.deptList,
      user: layouts.user,
      authAccess: layouts.auth,
    }),
  );
  const formRefDept = useRef(null);
  let [isSaveDept, setIsSaveDept] = useState(0);
  let [treeData, setTreeData] = useState([]);
  let [openSelfChat, setOpenSelfChat] = useState(null);

  let [userNotInDeptArr, setUserNotInDeptArr] = useState([]);
  let [current, setCurrent] = useState(10);
  let [updateDeptId, setUpdateDeptId] = useState('');
  const [loading, setLoading] = useState(false);
  const [loadingBtn, setLoadingBtn] = useState(false);
  let [radioValue, setRadioValue] = useState(1);
  let [deptPrinciple, setDeptPrinciple] = useState(0);
  let [agentAccessChannelValue, setAgentAccessChannelValue] = useState([]);

  const [contactLinesList, setContactLinesList] = useState([]);
  const areaNumList = [
    {
      city: '中国大陆',
      code: '+86',
    },
    {
      city: '中国香港',
      code: '+852',
    },
    {
      city: '中国澳门',
      code: '+853',
    },
    {
      city: '中国台湾',
      code: '+886',
    },
    {
      city: '新加坡',
      code: '+65',
    },
    {
      city: '阿富汗',
      code: '+93',
    },
    {
      city: '阿尔巴尼亚',
      code: '+355',
    },
    {
      city: '阿尔格拉',
      code: '+213',
    },
    {
      city: '安道尔',
      code: '+376',
    },
    {
      city: '安哥拉',
      code: '+244',
    },
    {
      city: '安圭拉',
      code: '+1264',
    },
    {
      city: '阿森松岛',
      code: '+247',
    },
    {
      city: '安提瓜和巴布达',
      code: '+1268',
    },
    {
      city: '阿根廷',
      code: '+54',
    },
    {
      city: '亚美尼亚',
      code: '+374',
    },
    {
      city: '阿鲁巴',
      code: '+297',
    },
    {
      city: '澳大利亚',
      code: '+61',
    },
    {
      city: '奥地利',
      code: '+43',
    },
    {
      city: '阿塞拜疆',
      code: '+994',
    },
    {
      city: '巴哈马',
      code: '+1242',
    },
    {
      city: '巴林',
      code: '+973',
    },
    {
      city: '孟加拉国',
      code: '+880',
    },
    {
      city: '巴巴多斯',
      code: '+1246',
    },
    {
      city: '白俄罗斯',
      code: '+375',
    },
    {
      city: '比利时',
      code: '+32',
    },
    {
      city: '伯利兹',
      code: '+501',
    },
    {
      city: '贝宁',
      code: '+229',
    },
    {
      city: '百慕大',
      code: '+1441',
    },
    {
      city: '不丹',
      code: '+975',
    },
    {
      city: '玻利维亚',
      code: '+591',
    },
    {
      city: '波斯尼亚和黑塞哥维那',
      code: '+387',
    },
    {
      city: '博茨瓦纳',
      code: '+267',
    },
    {
      city: '巴西',
      code: '+55',
    },
    {
      city: '文莱',
      code: '+673',
    },
    {
      city: '保加利亚',
      code: '+359',
    },
    {
      city: '布基纳法索',
      code: '+226',
    },
    {
      city: '布隆迪',
      code: '+257',
    },
    {
      city: '柬埔寨',
      code: '+855',
    },
    {
      city: '喀麦隆',
      code: '+237',
    },
    {
      city: '加拿大',
      code: '+1',
    },
    {
      city: '佛得角',
      code: '+238',
    },
    {
      city: '开曼群岛',
      code: '+1345',
    },
    {
      city: '中非共和国',
      code: '+236',
    },
    {
      city: '乍得',
      code: '+235',
    },
    {
      city: '智利',
      code: '+56',
    },
    {
      city: '哥伦比亚',
      code: '+57',
    },
    {
      city: '科摩罗',
      code: '+269',
    },
    {
      city: '刚果共和国',
      code: '+242',
    },
    {
      city: '刚果民主共和国',
      code: '+243',
    },
    {
      city: '库克群岛',
      code: '+682',
    },
    {
      city: '哥斯达黎加',
      code: '+506',
    },
    {
      city: '科特迪沃',
      code: '+225',
    },
    {
      city: '克罗地亚',
      code: '+385',
    },
    {
      city: '古巴',
      code: '+53',
    },
    {
      city: '塞浦路斯',
      code: '+357',
    },
    {
      city: '+捷克共和国',
      code: '+420',
    },
    {
      city: '丹麦',
      code: '+45',
    },
    {
      city: '吉布提',
      code: '+253',
    },
    {
      city: '多米尼加',
      code: '+1767',
    },
    {
      city: '多米尼加共和国',
      code: '+1809',
    },
    {
      city: '厄瓜多尔',
      code: '+593',
    },
    {
      city: '埃及',
      code: '+20',
    },
    {
      city: '艾萨尔瓦多',
      code: '+503',
    },
    {
      city: '爱沙尼亚',
      code: '+372',
    },
    {
      city: '埃塞俄比亚',
      code: '+251',
    },
    {
      city: '法罗群岛',
      code: '+298',
    },
    {
      city: '斐济',
      code: '+679',
    },
    {
      city: '芬兰',
      code: '+358',
    },
    {
      city: '法国',
      code: '+33',
    },
    {
      city: '法属圭亚那',
      code: '+594',
    },
    {
      city: '法属波利尼西亚',
      code: '+689',
    },
    {
      city: '加蓬',
      code: '+241',
    },
    {
      city: '冈比亚',
      code: '+220',
    },
    {
      city: '格鲁吉亚',
      code: '+995',
    },
    {
      city: '德国',
      code: '+94',
    },
    {
      city: '加纳',
      code: '+233',
    },
    {
      city: '直布罗陀',
      code: '+350',
    },
    {
      city: '希腊',
      code: '+30',
    },
    {
      city: '格陵兰',
      code: '+299',
    },
    {
      city: '格林纳达',
      code: '+1473',
    },
    {
      city: '瓜德罗普',
      code: '+590',
    },
    {
      city: '关岛',
      code: '+1671',
    },
    {
      city: '危地马拉',
      code: '+502',
    },
    {
      city: '几内亚',
      code: '+240',
    },
    {
      city: '根西',
      code: '+44',
    },
    {
      city: '几内亚',
      code: '+224',
    },
    {
      city: '圭亚那',
      code: '+592',
    },
    {
      city: '海地',
      code: '+509',
    },
    {
      city: '洪都拉斯',
      code: '+504',
    },
    {
      city: '缅甸',
      code: '+95',
    },
    {
      city: '匈牙利',
      code: '+36',
    },
    {
      city: '冰岛',
      code: '+354',
    },
    {
      city: '印度',
      code: '+91',
    },
    {
      city: '印度尼西亚',
      code: '+62',
    },
    {
      city: '伊朗',
      code: '+98',
    },
    {
      city: '伊拉克',
      code: '+964',
    },
    {
      city: '爱尔兰',
      code: '+353',
    },
    {
      city: '马恩岛',
      code: '+44',
    },
    {
      city: '以色列',
      code: '+972',
    },
    {
      city: '意大利',
      code: '+93',
    },
    {
      city: '牙买加',
      code: '+1876',
    },
    {
      city: '日本',
      code: '+81',
    },
    {
      city: '泽西岛',
      code: '+44',
    },
    {
      city: '约旦',
      code: '+962',
    },
    {
      city: '哈萨克斯坦',
      code: '+7',
    },
    {
      city: '肯尼亚',
      code: '+254',
    },
    {
      city: '科索沃',
      code: '+383',
    },
    {
      city: '科威特',
      code: '+965',
    },
    {
      city: '吉尔吉斯斯坦',
      code: '+996',
    },
    {
      city: '老挝',
      code: '+856',
    },
    {
      city: '拉脱维亚',
      code: '+371',
    },
    {
      city: '黎巴嫩',
      code: '+961',
    },
    {
      city: '莱索托',
      code: '+266',
    },
    {
      city: '利比里亚',
      code: '+231',
    },
    {
      city: '利比亚',
      code: '+218',
    },
    {
      city: '列支敦士登',
      code: '+423',
    },
    {
      city: '立陶宛',
      code: '+370',
    },
    {
      city: '卢森堡',
      code: '+352',
    },
    {
      city: '马其顿',
      code: '+389',
    },
    {
      city: '马达加斯加',
      code: '+261',
    },
    {
      city: '马拉维',
      code: '+265',
    },
    {
      city: '马来西亚',
      code: '+60',
    },
    {
      city: '马尔代夫',
      code: '+960',
    },
    {
      city: '马里',
      code: '+223',
    },
    {
      city: '马耳他',
      code: '+356',
    },
    {
      city: '马提尼克',
      code: '+596',
    },
    {
      city: '毛里塔尼亚',
      code: '+222',
    },
    {
      city: '毛里求斯',
      code: '+230',
    },
    {
      city: '马约特',
      code: '+262',
    },
    {
      city: '墨西哥',
      code: '+52',
    },
    {
      city: '摩尔多瓦',
      code: '+373',
    },
    {
      city: '摩纳哥',
      code: '+377',
    },
    {
      city: '蒙古',
      code: '+976',
    },
    {
      city: '黑山',
      code: '+382',
    },
    {
      city: '蒙特塞拉特',
      code: '+1664',
    },
    {
      city: '摩洛哥',
      code: '+212',
    },
    {
      city: '莫桑比克',
      code: '+258',
    },
    {
      city: '纳米比亚',
      code: '+264',
    },
    {
      city: '尼泊尔',
      code: '+977',
    },
    {
      city: '荷兰',
      code: '+31',
    },
    {
      city: '荷属安的列斯',
      code: '+599',
    },
    {
      city: '新喀里多尼亚',
      code: '+687',
    },
    {
      city: '新西兰',
      code: '+64',
    },
    {
      city: '尼加拉瓜',
      code: '+505',
    },
    {
      city: '尼日尔',
      code: '+227',
    },
    {
      city: '尼日利亚',
      code: '+234',
    },
    {
      city: '挪威',
      code: '+47',
    },
    {
      city: '阿曼',
      code: '+968',
    },
    {
      city: '巴基斯坦',
      code: '+92',
    },
    {
      city: '巴勒斯坦',
      code: '+970',
    },
    {
      city: '巴拿马',
      code: '+507',
    },
    {
      city: '巴布亚新几内亚',
      code: '+675',
    },
    {
      city: '巴拉圭',
      code: '+595',
    },
    {
      city: '秘鲁',
      code: '+51',
    },
    {
      city: '菲律宾',
      code: '+63',
    },
    {
      city: '波兰',
      code: '+48',
    },
    {
      city: '葡萄牙',
      code: '+351',
    },
    {
      city: '波多黎各',
      code: '+1',
    },
    {
      city: '库塔',
      code: '+974',
    },
    {
      city: '留尼汪',
      code: '+262',
    },
    {
      city: '罗马尼亚',
      code: '+40',
    },
    {
      city: '俄罗斯',
      code: '+7',
    },
    {
      city: '卢旺达',
      code: '+250',
    },
    {
      city: '萨摩亚东部',
      code: '+684',
    },
    {
      city: '萨摩亚西部',
      code: '+685',
    },
    {
      city: '圣马力诺',
      code: '+378',
    },
    {
      city: '圣多美和普林西比',
      code: '+239',
    },
    {
      city: '沙特阿拉伯',
      code: '+966',
    },
    {
      city: '塞内加尔',
      code: '+221',
    },
    {
      city: '塞尔维亚',
      code: '+381',
    },
    {
      city: '塞舌尔',
      code: '+248',
    },
    {
      city: '塞拉利昂',
      code: '+232',
    },
    {
      city: '斯洛伐克',
      code: '+421',
    },
    {
      city: '斯洛文尼亚',
      code: '+386',
    },
    {
      city: '南非',
      code: '+27',
    },
    {
      city: '韩国',
      code: '+82',
    },
    {
      city: '西班牙',
      code: '+34',
    },
    {
      city: '斯里兰卡',
      code: '+94',
    },
    {
      city: '圣基茨和尼维斯',
      code: '+1869',
    },
    {
      city: '圣卢西亚',
      code: '+1758',
    },
    {
      city: '圣文森特',
      code: '+1784',
    },
    {
      city: '苏丹',
      code: '+249',
    },
    {
      city: '苏里南',
      code: '+597',
    },
    {
      city: '斯威士兰',
      code: '+268',
    },
    {
      city: '瑞典',
      code: '+46',
    },
    {
      city: '瑞士',
      code: '+41',
    },
    {
      city: '叙利亚',
      code: '+963',
    },
    {
      city: '塔吉克斯坦',
      code: '+992',
    },
    {
      city: '坦桑尼亚',
      code: '+255',
    },
    {
      city: '泰国',
      code: '+66',
    },
    {
      city: '东帝汶',
      code: '+670',
    },
    {
      city: '多哥',
      code: '+228',
    },
    {
      city: '汤加',
      code: '+676',
    },
    {
      city: '特立尼达和多巴哥',
      code: '+1868',
    },
    {
      city: '突尼斯',
      code: '+216',
    },
    {
      city: '土耳其',
      code: '+90',
    },
    {
      city: '土库曼斯坦',
      code: '+993',
    },
    {
      city: '特克斯和凯科斯群岛',
      code: '+1649',
    },
    {
      city: '乌干达',
      code: '+256',
    },
    {
      city: '乌克兰',
      code: '+380',
    },
    {
      city: '阿拉伯联合酋长国',
      code: '+971',
    },
    {
      city: '英国',
      code: '+44',
    },
    {
      city: '美国',
      code: '+1',
    },
    {
      city: '乌拉圭',
      code: '+598',
    },
    {
      city: '乌兹别克斯坦',
      code: '+998',
    },
    {
      city: '瓦努阿图',
      code: '+678',
    },
    {
      city: '委内瑞拉',
      code: '+58',
    },
    {
      city: '越南',
      code: '+84',
    },
    {
      city: '维尔京群岛',
      code: '+1340',
    },
    {
      city: '也门',
      code: '+967',
    },
    {
      city: '赞比亚',
      code: '+260',
    },
    {
      city: '津巴布韦',
      code: '+263',
    },
  ]; //区号列表
  // {
  //   id: Date.now(),
  //   connectId: '',
  //   userCreateType: 1,
  //   connectUserId: '',
  //   connectUserName: '',
  //   newConnectUserList: [],
  // },
  // 构造connect list  构造成 {value: id, label: value}
  let newConnectList = useMemo(
    () =>
      connectList.map(connect => ({
        value: JSON.parse(connect.value).connectId,
        label: connect.label,
        identityManagementType: JSON.parse(connect.value)
          .identityManagementType,
      })),
    [connectList],
  );
  // 联络线路区域值
  const [lineArea, setLineArea] = useState('');
  // 联络线路区域下拉列表
  const [lineAreaList, setLineAreaList] = useState([]);
  //connect下拉列表
  const [connectDataList, setConnectDataList] = useState([]);
  //twilio下拉列表
  const [twilioList, setTwilioList] = useState([]);

  useEffect(() => {
    let a = null;
    if (user.openSelfChat) {
      console.log(user.openSelfChat);
      a = user.openSelfChat;
    } else {
      console.log(JSON.parse(sessionStorage.getItem('user')).openSelfChat);
      a = JSON.parse(sessionStorage.getItem('user')).openSelfChat;
    }
    setOpenSelfChat(a);
    setIsSaveDept(history.location.query.isEdit === 'false' ? false : true);
    setUpdateDeptId(history.location.query.id);
    formRefDept.current.setFieldValue('deptId', history.location.query.pId);
    if (history.location.query.isEdit === 'true') {
      getUpdateHandle();
      queryTwilioList();
      queryConnectList();
    }
    userNotInDept();
    queryDeptList();
    queryVoiceSupplierList();
  }, []);

  /**
   * 查询部门信息
   */
  const queryDeptList = () => {
    setLoading(true);
    dispatch({
      type: 'userManagement/queryDeptList',
      payload: null,
      callback: response => {
        let { code, data, msg } = response;
        if (200 === code) {
          setTreeData(data);
        }
        setLoading(false);
      },
    });
  };
  /**
   * 查询负责人
   */
  const userNotInDept = () => {
    setLoading(true);
    dispatch({
      type: 'userManagement/userNotInDept',
      callback: response => {
        let { code, data, msg } = response;
        setLoading(false);
        if (200 === code) {
          const a = data?.map(connect => ({
            value: connect.userId,
            label: connect.userName,
          }));
          setUserNotInDeptArr(a);
        }
      },
    });
  };
  const onChangeCheck = useCallback(
    e => {
      setDeptPrinciple(e.target.value);
    },
    [deptPrinciple],
  );
  // 团队回显
  const getUpdateHandle = () => {
    let openSelfChat1 = JSON.parse(sessionStorage.getItem('user')).openSelfChat;
    setLoading(true);
    let useId = history.location.query.id;
    dispatch({
      type: 'userManagement/queryUserById',
      payload: useId,
      callback: response => {
        let { code, data, msg } = response;
        setLoading(false);
        if (200 === code) {
          // console.log(data)
          // 回显数据
          let userType = data.roleList?.map(role => role.roleId)?.[0];
          let deptPrinciple = 0;
          if (userType === '1001') {
            userType = 1;
          } else if (userType === '1002') {
            userType = 2;
          } else if (userType === '1003') {
            userType = 3;
          } else if (userType === '1004') {
            userType = 2;
            deptPrinciple = 1;
          } else if (userType === '1005') {
            userType = 3;
            deptPrinciple = 1;
          }
          // 展示 取消 connect list
          setRadioValue(userType);
          setDeptPrinciple(deptPrinciple);

          let voiceSupplierRealName = data?.voiceSupplierRealName;
          setLineArea(voiceSupplierRealName);
          if (
            userType === 3 &&
            data.agentAccessChannel?.split(',')?.includes('3')
          ) {
            if (voiceSupplierRealName === 'Connect') {
              let contactlinesList = data?.connectList?.map((item, index) => {
                return {
                  id: Date.now(),
                  connectId: item.connectId,
                  voiceSupplierId: item.voiceSupplierId,
                  userCreateType: item.connectUserId ? 2 : 1,
                  connectUserId: item.connectUserId,
                  connectUserName: item.connectUserName,
                  newConnectUserList: [],
                  identityManagementType: item.identityManagementType,
                };
              });
              getConnectUserInfoUpdate(contactlinesList, useId);
            } else {
              let contactlinesList = data?.twilioList?.map((item, index) => {
                return {
                  id: Date.now(),
                  connectId: item.voiceTwilioLineId,
                  connectUserId: '',
                  connectUserName: '',
                  userCreateType: 1,
                  voiceSupplierId: item.voiceSupplierId,
                  voiceTwilioLineAlias: item.voiceTwilioLineAlias,
                  voiceSupplierRealName: item.voiceSupplierRealName,
                };
              });
              setContactLinesList(contactlinesList);
            }
          }

          // 回显数据
          formRefDept.current.setFieldsValue({
            ...data,
            userType,
            deptPrinciple,
            lineArea: voiceSupplierRealName,
            agentAccessChannel: data.agentAccessChannel
              ? data.agentAccessChannel.split(',')
              : [],
            receiveTicketType: data.receiveTicketType
              ? data.receiveTicketType.split(',')
              : [],
            connectList: data.connectList?.map(connect => connect.connectId),
          });
          setAgentAccessChannelValue(data.agentAccessChannel?.split(','));
        }
      },
    });
  };
  /**
   * 返回connectid对应的绑定用户列表
   */
  const getConnectUserInfoUpdate = useCallback(
    (contactlinesList, useId) => {
      setLoading(true);
      console.log(contactlinesList);
      contactlinesList?.forEach(item => {
        dispatch({
          type: 'userManagement/getConnectUserInfo',
          payload: { connectId: item.connectId, userId: useId },
          callback: response => {
            let { code, data, msg } = response;
            if (200 === code) {
              const a = data?.map(connect => ({
                value: connect.connectUserId,
                label: connect.userName,
              }));
              item.newConnectUserList = a;
            }
            setLoading(false);
          },
        });
      });
      console.log(contactlinesList);
      setContactLinesList(contactlinesList);
    },
    [contactLinesList],
  );
  const onChangeUserType = useCallback(
    e => {
      setRadioValue(e.target.value);
      if (e.target.value === 3) {
        setContactLinesList([
          {
            id: Date.now(),
            connectId: [],
            userCreateType: 1,
            connectUserId: '',
            connectUserName: '',
            newConnectUserList: [],
          },
        ]);
      } else {
        setContactLinesList([]);
      }
    },
    [radioValue],
  );
  /**
   * 添加联络线路
   */
  const addContactLines = () => {
    setTimeout(() => {
      if (lineArea === 'Connect') {
        setContactLinesList(prevData => {
          console.log(prevData, '奇怪了');
          const data = prevData ? [...prevData] : [];
          data.push({
            id: Date.now(),
            connectId: [],
            userCreateType: 1,
            connectUserId: [],
            connectUserName: '',
            newConnectUserList: [],
          });
          return data;
        });
      } else {
        setContactLinesList(prevData => {
          const data = prevData ? [...prevData] : [];
          data.push({
            id: Date.now(),
            //twilio联络线路指代 voiceTwilioLineId字段
            connectId: [],
            connectUserId: '',
            connectUserName: '',
            userCreateType: 1,
            voiceSupplierId: '',
            voiceTwilioLineAlias: '',
            voiceSupplierRealName: '',
          });
          return data;
        });
      }
    }, 0);
  };
  /**
   * 删除联络线路
   */
  const deleteContactLines = index => {
    setTimeout(() => {
      console.log('删=====');
      setContactLinesList(prevData => {
        const newData = [...prevData]; // 进行浅拷贝
        newData.splice(index, 1); // 删除指定位置的元素
        return newData; // 返回新的数组
      });
    }, 0);
  };
  const onChangeCreateType = useCallback(
    (e, index) => {
      const data = [...contactLinesList];
      data[index].userCreateType = e.target.value;
      if (e.target.value === 1) {
        data[index].connectUserId = '';
        data[index].connectUserName = '';
      } else {
        data[index].connectUserId = [];
      }
      setContactLinesList(data);
    },
    [contactLinesList],
  );
  const onChangeConnectUser = useCallback(
    (e, option, index) => {
      console.log(e, option);
      const data = [...contactLinesList];
      data[index].connectUserId = option.value;
      data[index].connectUserName = option.label;
      setContactLinesList(data);
    },
    [contactLinesList],
  );
  /**
   * 根据渠道id查询用户列表
   */
  const getConnectUserInfo = (e, index, op) => {
    console.log(op);
    setLoading(true);
    dispatch({
      type: 'userManagement/getConnectUserInfo',
      payload: { connectId: e },
      callback: response => {
        let { code, data, msg } = response;
        if (200 === code) {
          // console.log(data)

          const a = data?.map(connect => ({
            value: connect.connectUserId,
            label: connect.userName,
          }));
          const data1 = [...contactLinesList];
          if (op.identityManagementType !== 'SAML') {
            data1[index].userCreateType = null;
            data1[index].connectUserId = '';
          } else {
            data1[index].userCreateType = 1;
            data1[index].connectUserId = '';
            data1[index].connectUserName = '';
          }
          data1[index].connectId = e;
          data1[index].newConnectUserList = a;
          data1[index].voiceSupplierId = op.voiceSupplierId;
          data1[index].identityManagementType = op.identityManagementType;
          setContactLinesList(data1);
        }
        setLoading(false);
      },
    });
  };
  /**
   * 添加/更新 用户信息
   * @type {(function(*=): void)|*}
   */
  const onFinish = useCallback(
    values => {
      let payload = {
        ...values,
        agentAccessChannel: values.agentAccessChannel?.join(','),
        receiveTicketType: values.receiveTicketType?.join(','),
        voiceSupplierRealName: lineArea,
      };

      //判空，判重
      let flag = true;
      let connectIdList = [];
      //判断分配规则和接入渠道不能为空
      if (radioValue == 3) {
        if (
          !values.agentAccessChannel ||
          (values.agentAccessChannel && values.agentAccessChannel.length < 1)
        ) {
          notification.error({
            message: getIntl().formatMessage({
              id: 'user.management.agentAccessChannel.no',
            }),
          });
          return;
        }
        if (
          values.agentAccessChannel?.includes('1') &&
          (!values.receiveTicketType ||
            (values.receiveTicketType && values.receiveTicketType.length < 1))
        ) {
          notification.error({
            message: getIntl().formatMessage({
              id: 'user.management.receiveTicketType.no',
            }),
          });
          return;
        }
      }
      if (
        radioValue === 3 &&
        values.agentAccessChannel.includes('3') &&
        contactLinesList?.length < 1
      ) {
        notification.error({
          message: getIntl().formatMessage({
            id: 'user.management.contact.selsect.please',
          }),
        });
        return;
      } else if (
        radioValue === 3 &&
        values.agentAccessChannel.includes('3') &&
        contactLinesList?.length > 0
      ) {
        contactLinesList?.forEach(item => {
          if (
            !item.connectId ||
            item.connectId?.length == 0
            // && openSelfChat !== 1
          ) {
            notification.error({
              message: getIntl().formatMessage({
                id: 'user.management.contact.selsect.please',
              }),
            });
            flag = false;
          } else if (
            item.userCreateType === 2 &&
            (item.connectUserId?.length == 0 || !item.connectUserId)
          ) {
            notification.error({
              message: getIntl().formatMessage({
                id: 'user.management.contact.selsect.user.please',
              }),
            });
            flag = false;
          } else if (
            connectIdList.length > 0 &&
            connectIdList.includes(item.connectId)
          ) {
            notification.error({
              message: getIntl().formatMessage({
                id: 'user.management.contact.selsect.connectLines.please',
              }),
            });
            flag = false;
          }
          connectIdList.push(item.connectId);
        });
      }
      //传入contactLinesList
      if (flag == true) {
        payload.connectUserInfoVos = contactLinesList?.map(item => {
          if (item.connectId?.length == 0 || item.connectUserId?.length == 0) {
            return {
              ...item,
              connectId: item.connectId?.length == 0 ? '' : item.connectId,
              connectUserId:
                item.connectUserId?.length == 0 ? '' : item.connectUserId,
            };
          } else {
            return item;
          }
        });
      } else {
        return;
      }
      // 按钮加载状态
      setLoadingBtn(true);
      if (!isSaveDept) {
        dispatch({
          type: 'userManagement/createUser',
          payload: payload,
          callback: response => {
            let { code, msg } = response;
            if (200 === code) {
              handleCancelAdd();
              notification.success({
                message: getIntl().formatMessage({
                  id: 'user.management.create.success',
                }),
              });
            } else {
              notification.error({
                message: msg,
              });
            }
            // 取消加载
            setLoadingBtn(false);
          },
        });
      } else {
        payload.userId = updateDeptId;
        dispatch({
          type: 'userManagement/updateUser',
          payload: payload,
          callback: response => {
            let { code, msg } = response;
            if (200 === code) {
              notification.success({
                message: getIntl().formatMessage({
                  id: 'user.management.update.success',
                }),
              });
              handleCancelAdd();
            } else {
              notification.error({
                message: msg,
              });
            }
            // 取消加载
            setLoadingBtn(false);
          },
        });
      }
    },
    [isSaveDept, updateDeptId, contactLinesList, radioValue],
  );
  const handleCancelAdd = () => {
    history.goBack();
  };
  /**
   * 处理form中所有字段变化的事件
   */
  const handleFormValuesChange = (changedValues, allValues) => {
    if (changedValues.agentAccessChannel) {
      setAgentAccessChannelValue(changedValues.agentAccessChannel);
    }
  };
  // 获取联络线路供应商
  const queryVoiceSupplierList = () => {
    setLoading(true);
    dispatch({
      type: 'channel/queryVoiceSupplierList',
      callback: response => {
        let { code, msg, data } = response;
        if (200 === code) {
          if (data) {
            setLineAreaList(response.data);
            queryConnectList();
          }
        } else {
          notification.error({
            message: msg,
          });
        }
        // 取消加载
        setLoading(false);
      },
    });
  };
  // 获取connect联络线路下拉列表
  const queryConnectList = () => {
    setLoading(true);
    dispatch({
      type: 'channel/queryConnectList',
      callback: response => {
        let { code, msg, data } = response;
        if (200 === code) {
          if (data) {
            setConnectDataList(data);
          }
        } else {
          notification.error({
            message: msg,
          });
        }
        // 取消加载
        setLoading(false);
      },
    });
  };
  // 获取Twilio联络线路下拉列表
  const queryTwilioList = () => {
    setLoading(true);
    dispatch({
      type: 'channel/queryTwilioList',
      callback: response => {
        let { code, msg, data } = response;
        if (200 === code) {
          if (data) {
            setTwilioList(data);
          }
        } else {
          notification.error({
            message: msg,
          });
        }
        // 取消加载
        setLoading(false);
      },
    });
  };
  // 切换线路区域
  const onChangeLineArea = e => {
    setLineArea(e.target.value);
    if (e.target.value === 'Connect') {
      setContactLinesList([
        {
          id: Date.now(),
          connectId: [],
          userCreateType: 1,
          voiceSupplierId: '',
          connectUserId: '',
          connectUserName: '',
          newConnectUserList: [],
        },
      ]);
      queryConnectList();
    } else {
      queryTwilioList();
      setContactLinesList([
        {
          id: Date.now(),
          connectId: [],
          connectUserId: '',
          connectUserName: '',
          userCreateType: 1,
          voiceSupplierId: '',
          voiceTwilioLineAlias: '',
          voiceSupplierRealName: '',
        },
      ]);
    }
  };
  // 选择twilio联络线路
  const getTwilioInfo = (value, index, option) => {
    const newData = [...contactLinesList];
    newData[index].connectId = value;
    newData[index].voiceSupplierId = option.voiceSupplierId;
    newData[index].voiceTwilioLineAlias = option.voiceTwilioLineAlias;
    newData[index].voiceSupplierRealName = option.voiceSupplierRealName;

    setContactLinesList(newData);
  };
  return (
    <div className={styles.createOrganization}>
      <Spin spinning={loading}>
        <p className="blueBorder">
          {!isSaveDept ? (
            <FormattedMessage
              id="user.management.create.user"
              defaultMessage="创建用户"
            />
          ) : (
            <FormattedMessage
              id="user.management.update.user"
              defaultMessage="修改用户"
            />
          )}
        </p>
        <Form
          name="basic"
          initialValues={{
            remember: true,
            deptPrinciple: deptPrinciple,
          }}
          labelCol={{
            span: getLocale() === 'zh-CN' ? 4 : 5,
          }}
          wrapperCol={{
            span: getLocale() === 'zh-CN' ? 20 : 19,
          }}
          ref={formRefDept}
          autoComplete="off"
          onFinish={onFinish}
          onValuesChange={handleFormValuesChange}
        >
          <Steps
            progressDot
            current={current}
            direction="vertical"
            items={[
              {
                title: (
                  <FormattedMessage
                    id="create.user.step.title.1"
                    defaultMessage="用户基本信息"
                  />
                ),
                description: (
                  <div>
                    <Row gutter={22}>
                      <Col span={11}>
                        <Form.Item
                          label={getIntl().formatMessage({
                            id: 'create.user.step.title.1.label.2',
                          })}
                          name="userName"
                          rules={[
                            {
                              required: true,
                              message: (
                                <FormattedMessage id="register.last.name.input" />
                              ),
                            },
                          ]}
                        >
                          <Input
                            placeholder={getIntl().formatMessage({
                              id: 'create.user.step.title.1.label.2.p',
                            })}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={11}>
                        <Form.Item
                          label={getIntl().formatMessage({
                            id: 'create.user.step.title.1.label.1',
                          })}
                          name="lastName"
                          rules={[
                            {
                              required: true,
                              message: (
                                <FormattedMessage id="register.user.name.input" />
                              ),
                            },
                          ]}
                        >
                          <Input
                            placeholder={getIntl().formatMessage({
                              id: 'create.user.step.title.1.label.1.p',
                            })}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                    <Row gutter={22}>
                      <Col span={11}>
                        <Form.Item
                          label={getIntl().formatMessage({
                            id:
                              'customerInformation.add.basicInformation.contactNumber',
                            defaultValue: '联系电话',
                          })}
                          required
                        >
                          <Input.Group compact>
                            <Form.Item name={'telephonePrefixId'} noStyle>
                              <Select
                                placeholder={getIntl().formatMessage({
                                  id:
                                    'customerInformation.add.basicInformation.Address.placeholder',
                                  defaultValue: '请选择地域',
                                })}
                                showSearch
                                allowClear={true}
                                dropdownMatchSelectWidth={false}
                                popupClassName="select-drop-down-platform"
                                style={{ width: '18%', marginRight: '2%' }}
                              >
                                {areaNumList?.map((item, index) => (
                                  <Option value={item.code} key={index}>
                                    {item.code}
                                  </Option>
                                ))}
                              </Select>
                            </Form.Item>
                            <Form.Item
                              name="phonenumber"
                              noStyle
                              rules={[
                                {
                                  required: true,
                                  message: (
                                    <FormattedMessage id="register.user.phone.input" />
                                  ),
                                },
                                {
                                  max: 30,
                                  pattern: '^(\\+\\d{1,3})?[1-9]\\d{9,14}$',
                                  message: (
                                    <FormattedMessage id="register.user.phone.length.input" />
                                  ),
                                },
                              ]}
                            >
                              <Input
                                style={{ width: '80%' }}
                                placeholder={getIntl().formatMessage({
                                  id: 'create.user.step.title.1.label.3.p',
                                })}
                              />
                            </Form.Item>
                          </Input.Group>
                        </Form.Item>
                      </Col>
                      <Col span={11}>
                        <Form.Item
                          label={getIntl().formatMessage({
                            id: 'register.email',
                          })}
                          name="email"
                          rules={[
                            {
                              required: true,
                              message: (
                                <FormattedMessage id="login.user.email.input" />
                              ),
                            },
                            {
                              min: 1,
                              max: 40,
                              type: 'email',
                              message: (
                                <FormattedMessage id="login.user.email.pattern" />
                              ),
                            },
                          ]}
                        >
                          <Input
                            disabled={isSaveDept ? true : false}
                            placeholder={getIntl().formatMessage({
                              id: 'login.user.email.input',
                            })}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                    {/* 初始密码 */}
                    {!isSaveDept ? (
                      <Row gutter={22}>
                        <Col span={11}>
                          <div className={styles.colAuto}>
                            <Form.Item
                              label={getIntl().formatMessage({
                                id: 'register.user.password',
                              })}
                              name="password"
                              rules={[
                                {
                                  required: true,
                                },
                              ]}
                            >
                              <Input
                                placeholder={getIntl().formatMessage({
                                  id:
                                    'register.user.name.input.initialPassword',
                                })}
                              />
                            </Form.Item>
                            <Button
                              style={{ marginTop: 5 }}
                              type="text"
                              onClick={() => {
                                formRefDept.current.setFieldValue(
                                  'password',
                                  generatePassword(8),
                                );
                              }}
                            >
                              {getIntl().formatMessage({
                                id: 'create.management.connect.auto.generation',
                              })}
                            </Button>
                          </div>
                        </Col>
                        <Col span={11}></Col>
                      </Row>
                    ) : (
                      ''
                    )}
                  </div>
                ),
              },
              {
                title: (
                  <FormattedMessage
                    id="create.user.step.title.2"
                    defaultMessage="用户团队信息"
                  />
                ),
                description: (
                  <div>
                    <Row style={{ width: '50%' }}>
                      <Col span={22}>
                        {/* 部门 */}
                        <Form.Item
                          label={getIntl().formatMessage({
                            id: 'create.user.step.title.2.label.1',
                          })}
                          name="deptId"
                          rules={[
                            {
                              required: true,
                              message: (
                                <FormattedMessage id="create.user.step.title.2.label.1.p" />
                              ),
                            },
                          ]}
                        >
                          <TreeSelect
                            showSearch
                            style={{
                              width: '100%',
                            }}
                            // value={value}
                            dropdownStyle={{
                              maxHeight: 400,
                              overflow: 'auto',
                            }}
                            placeholder={getIntl().formatMessage({
                              id: 'create.user.step.title.2.label.1.p',
                            })}
                            allowClear
                            treeDefaultExpandAll
                            // onChange={onChange}
                            treeData={treeData}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                    <Row style={{ width: '50%' }}>
                      <Col span={22}>
                        {/* 角色 */}
                        <Form.Item
                          label={getIntl().formatMessage({
                            id: 'user.management.role.select',
                          })}
                          name="userType"
                          rules={[
                            {
                              required: true,
                              message: (
                                <FormattedMessage id="create.user.step.title.2.label.2.p" />
                              ),
                            },
                          ]}
                        >
                          <Radio.Group
                            disabled={isSaveDept ? true : false}
                            onChange={onChangeUserType}
                            // defaultValue={radioValue}
                          >
                            <Radio value={1}>
                              <FormattedMessage id="user.management.radio.user.1" />
                            </Radio>
                            <Radio value={2}>
                              <FormattedMessage id="user.management.radio.user.2" />
                            </Radio>
                            <Radio value={3}>
                              <FormattedMessage id="user.management.radio.user.3" />
                            </Radio>
                          </Radio.Group>
                        </Form.Item>
                      </Col>
                    </Row>
                    <Row style={{ width: '50%' }}>
                      <Col span={22}>
                        {/* 设为组织负责人 */}
                        {radioValue !== 1 && (
                          <Form.Item
                            label={getIntl().formatMessage({
                              id: 'team.userName.set',
                            })}
                            name="deptPrinciple"
                          >
                            <Radio.Group
                              onChange={onChangeCheck}
                              value={deptPrinciple}
                            >
                              <Radio value={1}>
                                <FormattedMessage id="user.management.operation.btn.yes" />
                              </Radio>
                              <Radio value={0}>
                                <FormattedMessage id="user.management.operation.btn.no" />
                              </Radio>
                            </Radio.Group>
                          </Form.Item>
                        )}
                      </Col>
                    </Row>
                    <Row style={{ width: '50%' }}>
                      <Col span={22}>
                        {/* 工号 */}
                        {radioValue !== 1 && (
                          <Form.Item
                            label={getIntl().formatMessage({
                              id: 'register.user.workNumber',
                            })}
                            name="workNumber"
                          >
                            <Input
                              placeholder={getIntl().formatMessage({
                                id: 'register.user.name.input.workNumber',
                              })}
                            />
                          </Form.Item>
                        )}
                      </Col>
                    </Row>
                  </div>
                ),
              },
              radioValue == 3 && {
                title: (
                  <FormattedMessage
                    id="create.user.step.title.3"
                    defaultMessage="座席接入渠道"
                  />
                ),
                description: (
                  <div>
                    <Form.Item name="agentAccessChannel">
                      <Checkbox.Group>
                        <Row style={{ width: '100%', marginLeft: 30 }}>
                          <Col span={3}>
                            <Checkbox
                              value="3"
                              style={{
                                fontSize: 12,
                              }}
                            >
                              <FormattedMessage id="create.user.step.title.2.label.3.1" />
                            </Checkbox>
                          </Col>
                          <Col span={3}>
                            <Checkbox
                              value="1"
                              style={{
                                fontSize: 12,
                              }}
                            >
                              <FormattedMessage id="create.user.step.title.2.label.3.2" />
                            </Checkbox>
                          </Col>
                          <Col span={3}>
                            <Checkbox
                              value="2"
                              style={{
                                fontSize: 12,
                              }}
                            >
                              <FormattedMessage id="channel.configuration.email.title" />
                            </Checkbox>
                          </Col>
                        </Row>
                      </Checkbox.Group>
                    </Form.Item>
                  </div>
                ),
              },
              radioValue == 3 &&
                agentAccessChannelValue?.includes('1') && {
                  title: (
                    <FormattedMessage
                      id="create.user.step.title.4"
                      defaultMessage="聊天领取工单方式"
                    />
                  ),
                  description: (
                    <div>
                      <Form.Item name="receiveTicketType">
                        <Checkbox.Group>
                          <Row style={{ width: '100%', marginLeft: 30 }}>
                            <Col span={6}>
                              <Checkbox
                                value="1"
                                style={{
                                  fontSize: 12,
                                }}
                              >
                                <FormattedMessage id="create.user.step.title.2.label.4.1" />
                              </Checkbox>
                            </Col>
                            <Col span={6}>
                              <Checkbox
                                value="2"
                                style={{
                                  fontSize: 12,
                                }}
                              >
                                <FormattedMessage id="create.user.step.title.2.label.4.2" />
                              </Checkbox>
                            </Col>
                          </Row>
                        </Checkbox.Group>
                      </Form.Item>
                      <Row
                        style={{ width: '100%', marginLeft: 30, fontSize: 12 }}
                      >
                        <FormattedMessage id="create.user.step.title.2.label.4.tips" />
                      </Row>
                    </div>
                  ),
                },
              radioValue === 3 &&
                agentAccessChannelValue?.includes('3') && {
                  title: (
                    <FormattedMessage
                      id="create.user.step.title.5"
                      defaultMessage="添加电话联络线路"
                    />
                  ),
                  description: (
                    <div>
                      <Row gutter={24}>
                        <Col span={12}>
                          <Form.Item
                            label={getIntl().formatMessage({
                              id: 'create.user.step.title.6',
                              defaultValue: '选择线路区域',
                            })}
                            rules={[
                              {
                                required: true,
                                message: (
                                  <FormattedMessage
                                    id="create.user.step.title.6.placeholder"
                                    defaultValue="请选择线路区域"
                                  />
                                ),
                              },
                            ]}
                            name="lineArea"
                          >
                            <Radio.Group
                              onChange={onChangeLineArea}
                              value={lineArea}
                            >
                              {lineAreaList?.map(item => {
                                return (
                                  <Radio value={item.voiceSupplierRealName}>
                                    {item.voiceSupplierAlias}
                                  </Radio>
                                );
                              })}
                            </Radio.Group>
                          </Form.Item>
                        </Col>
                      </Row>
                      <Row style={{ width: '80%' }}>
                        {lineArea &&
                        contactLinesList &&
                        contactLinesList.length > 0 ? (
                          <Col span={24}>
                            <Row
                              style={{
                                // display:
                                //   radioValue === 3 && openSelfChat !== 1
                                //     ? 'block'
                                //     : 'none',
                                marginTop: 10,
                              }}
                            >
                              <Col span={24}>
                                {contactLinesList?.map((item, index) => {
                                  if (lineArea === 'Connect') {
                                    return (
                                      <div className={styles.contactLinesList}>
                                        <p
                                          style={{
                                            fontWeight: 'bold',
                                            color: '#333',
                                          }}
                                        >
                                          <span>
                                            {getIntl().formatMessage({
                                              id: 'user.management.connect',
                                            })}
                                            {index + 1}
                                          </span>

                                          <span
                                            style={{
                                              float: 'right',
                                              display:
                                                index !== 0 ? 'block' : 'none',
                                            }}
                                          >
                                            <Button
                                              type="primary"
                                              danger
                                              onClick={() =>
                                                deleteContactLines(index)
                                              }
                                              icon={<DeleteHome />}
                                            >
                                              <span style={{ marginLeft: 6 }}>
                                                <FormattedMessage id="user.option.delete" />
                                              </span>
                                            </Button>
                                          </span>
                                        </p>
                                        <div
                                          style={{
                                            marginTop: 30,
                                            marginBottom: 10,
                                            clear: 'both',
                                            display: 'flex',
                                            alignItems: 'baseline',
                                            color: '#333',
                                          }}
                                        >
                                          {getIntl().formatMessage({
                                            id:
                                              'user.management.contact.selsect',
                                          })}

                                          <Select
                                            allowClear={true}
                                            options={connectDataList}
                                            value={item.connectId}
                                            onChange={(e, option) =>
                                              getConnectUserInfo(
                                                e,
                                                index,
                                                option,
                                              )
                                            }
                                            placeholder={getIntl().formatMessage(
                                              {
                                                id:
                                                  'user.management.contact.selsect.please',
                                              },
                                            )}
                                            showSearch
                                            filterOption={(
                                              inputValue,
                                              option,
                                            ) =>
                                              option.label
                                                .toLowerCase()
                                                .indexOf(
                                                  inputValue.toLowerCase(),
                                                ) >= 0
                                            }
                                            fieldNames={{
                                              label: 'connectAlias',
                                              value: 'connectId',
                                              key: 'connectId',
                                            }}
                                          />
                                        </div>
                                        {item.identityManagementType ===
                                        'SAML' ? (
                                          <div>
                                            <div
                                              style={{
                                                marginLeft:
                                                  getLocale() === 'zh-CN'
                                                    ? 84
                                                    : 140,
                                                marginTop: 10,
                                                marginBottom: 10,
                                              }}
                                            >
                                              <Radio.Group
                                                onChange={e =>
                                                  onChangeCreateType(e, index)
                                                }
                                                value={item.userCreateType}
                                              >
                                                <Radio
                                                  value={1}
                                                  // disabled={isSaveDept}
                                                >
                                                  <FormattedMessage id="user.management.connect.add.user" />
                                                </Radio>
                                                <Radio value={2}>
                                                  <FormattedMessage id="user.management.connect.add.user.bind" />
                                                </Radio>
                                                {/* <Tooltip
                                    placement="top"
                                    title={getIntl().formatMessage({
                                      id:
                                        'user.management.contact.Tooltip.radio3',
                                    })}
                                  >
                                    <Radio value={3}>
                                      <FormattedMessage id="user.management.connect.add.user.bind.no" />
                                    </Radio>
                                  </Tooltip> */}
                                              </Radio.Group>
                                            </div>
                                            {item.userCreateType == 2 ? (
                                              <div
                                                style={{
                                                  marginBottom: 10,
                                                  display: 'flex',
                                                  alignItems: 'baseline',
                                                }}
                                              >
                                                {getIntl().formatMessage({
                                                  id:
                                                    'user.management.contact.selsect.user',
                                                })}

                                                <Select
                                                  allowClear={true}
                                                  options={
                                                    item.newConnectUserList
                                                  }
                                                  // defaultValue={defaultConnectList}
                                                  onChange={(e, option) =>
                                                    onChangeConnectUser(
                                                      e,
                                                      option,
                                                      index,
                                                    )
                                                  }
                                                  value={item.connectUserId}
                                                  placeholder={getIntl().formatMessage(
                                                    {
                                                      id:
                                                        'user.management.contact.selsect.user.please',
                                                    },
                                                  )}
                                                  showSearch
                                                  filterOption={(
                                                    inputValue,
                                                    option,
                                                  ) =>
                                                    option.label
                                                      .toLowerCase()
                                                      .indexOf(
                                                        inputValue.toLowerCase(),
                                                      ) >= 0
                                                  }
                                                />
                                              </div>
                                            ) : (
                                              ''
                                            )}
                                          </div>
                                        ) : (
                                          ''
                                        )}
                                      </div>
                                    );
                                  } else {
                                    return (
                                      <div className={styles.contactLinesList}>
                                        <p
                                          style={{
                                            fontWeight: 'bold',
                                            color: '#333',
                                          }}
                                        >
                                          <span>
                                            {getIntl().formatMessage({
                                              id: 'user.management.connect',
                                            })}
                                            {index + 1}
                                          </span>

                                          <span
                                            style={{
                                              float: 'right',
                                              display:
                                                index !== 0 ? 'block' : 'none',
                                            }}
                                          >
                                            <Button
                                              type="primary"
                                              danger
                                              onClick={() =>
                                                deleteContactLines(index)
                                              }
                                              icon={<DeleteHome />}
                                            >
                                              <span style={{ marginLeft: 6 }}>
                                                <FormattedMessage id="user.option.delete" />
                                              </span>
                                            </Button>
                                          </span>
                                        </p>
                                        <div
                                          style={{
                                            marginTop: 30,
                                            marginBottom: 10,
                                            clear: 'both',
                                            display: 'flex',
                                            alignItems: 'baseline',
                                            color: '#333',
                                          }}
                                        >
                                          {getIntl().formatMessage({
                                            id:
                                              'user.management.contact.selsect',
                                          })}

                                          <Select
                                            allowClear={true}
                                            options={twilioList}
                                            value={item.connectId}
                                            onChange={(value, option) =>
                                              getTwilioInfo(
                                                value,
                                                index,
                                                option,
                                              )
                                            }
                                            placeholder={getIntl().formatMessage(
                                              {
                                                id:
                                                  'user.management.contact.selsect.please',
                                              },
                                            )}
                                            showSearch
                                            filterOption={(
                                              inputValue,
                                              option,
                                            ) =>
                                              option.label
                                                .toLowerCase()
                                                .indexOf(
                                                  inputValue.toLowerCase(),
                                                ) >= 0
                                            }
                                            fieldNames={{
                                              label: 'voiceTwilioLineAlias',
                                              value: 'voiceTwilioLineId',
                                              key: 'voiceTwilioLineId',
                                            }}
                                          />
                                        </div>
                                      </div>
                                    );
                                  }
                                })}
                              </Col>
                            </Row>
                          </Col>
                        ) : null}
                        {lineArea && (
                          <Button
                            onClick={() => addContactLines()}
                            style={{
                              fontSize: 12,
                            }}
                          >
                            <div>
                              <PlusOutlined />
                              <span style={{ marginLeft: 10 }}>
                                {getIntl().formatMessage({
                                  id: 'create.management.connect.add',
                                })}
                              </span>
                            </div>
                          </Button>
                        )}
                      </Row>
                    </div>
                  ),
                },
              // 为实现ui设计需要留下
              {
                title: null,
                description: null,
              },
            ]}
          />

          <Form.Item
            wrapperCol={{
              offset: 10,
              span: 14,
            }}
            style={{ marginTop: '100px' }}
          >
            <Button
              onClick={handleCancelAdd}
              style={{
                marginRight: 20,
              }}
            >
              <FormattedMessage id="user.management.btn.cancel" />
            </Button>
            <Button loading={loadingBtn} type="primary" htmlType="submit">
              {getIntl().formatMessage({
                id: 'channel.save',
              })}
            </Button>
          </Form.Item>
        </Form>
      </Spin>
    </div>
  );
};
