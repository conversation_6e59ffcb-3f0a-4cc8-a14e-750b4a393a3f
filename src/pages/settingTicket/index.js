import React, { useEffect, useState } from 'react';
import styles from './index.less';
import {
  getIntl,
  FormattedMessage,
  history,
  useDispatch,
  useSelector,
} from 'umi';
import {
  AISettingIcon,
  CloseTicketSettingIcon,
  MergeTicketSettingIcon,
  QuickReplySettingIcon,
  AgentQuickReplySettingIcon,
  InfoSettingIcon,
  RouteRuleSettingIcon,
  TicketWarningSettingIcon,
  PermissionSettingsIcon,
  CustomerAttrIcon,
  TicketAttrIcon,
  TicketTypeIcon,
  CompanyInfoIcon,
  PermissionsIcon,
  TicketSlaIcon,
  TicketWorkerTimeIcon,
  AgentStatusIcon,
  HoldMusicIcon,
} from './icon';

const SettingTicket = () => {
  const [activeTab, setActiveTab] = useState('1');
  const dispatch = useDispatch();
  const { currentSettingTab } = useSelector(({ layouts }) => ({
    currentSettingTab: layouts.currentSettingTab,
  }));
  useEffect(() => {
    setActiveTab(currentSettingTab);
  }, []);
  const menuList = [
    {
      key: '1',
      title: getIntl().formatMessage({ id: 'ticket.setting.menu.1' }),
      content: getIntl().formatMessage({
        id: 'ticket.setting.menu.1.subtitle',
      }),
    },
    {
      key: '2',
      title: getIntl().formatMessage({ id: 'ticket.setting.menu.2' }),
      content: getIntl().formatMessage({
        id: 'ticket.setting.menu.2.subtitle',
      }),
    },
    {
      key: '3',
      title: getIntl().formatMessage({ id: 'ticket.setting.menu.3' }),
      content: getIntl().formatMessage({
        id: 'ticket.setting.menu.3.subtitle',
      }),
    },
    {
      key: '5',
      title: getIntl().formatMessage({ id: 'ticket.setting.menu.5' }),
      content: getIntl().formatMessage({
        id: 'ticket.setting.menu.5.subtitle',
      }),
    },
  ];
  const saveCurrentActiveTab = e => {
    setActiveTab(e);
    dispatch({
      type: 'layouts/setCurrentSettingTab',
      payload: e,
    });
  };
  return (
    <div className={styles.settingTicketContainer}>
      <p className="blueBorder">
        <FormattedMessage id="ticket.setting.title" defaultMessage="系统设置" />
      </p>
      <div className={styles.settingTicketContent}>
        {/* 左侧导航 */}
        <div className={styles.settingTicketContentLeft}>
          <NavMenu
            activeTab={activeTab}
            onChange={e => saveCurrentActiveTab(e)}
            list={menuList}
          />
        </div>
        <div className={styles.settingTicketContentLine}></div>
        {/* 右侧内容 */}
        <div className={styles.settingTicketContentRight}>
          {activeTab === '1' && <TicketOrCustomerSettings />}
          {activeTab === '2' && <AgentOperationSettings />}
          {activeTab === '3' && <AutomationSettings />}
          {activeTab === '5' && <CompanyInfo />}
          {/* 其他标签内容 */}
        </div>
      </div>
    </div>
  );
};

const NavMenu = ({ activeTab, onChange, list }) => {
  return list.map(item => {
    return (
      <div
        className={`${styles.navMenu} ${
          activeTab === item.key ? styles.navMenuActive : ''
        }`}
        onClick={() => onChange(item.key)}
      >
        <div className={styles.navMenuTitle}>{item.title}</div>
        <div className={styles.navMenuContent}>{item.content}</div>
      </div>
    );
  });
};

const AutomationSettings = () => {
  const [activeTab, setActiveTab] = useState('');
  const menuList = [
    {
      key: '1',
      title: getIntl().formatMessage({
        id: 'automation.config.response.title.1',
      }),
      content: [
        {
          key: '1.1',
          name: getIntl().formatMessage({
            id: 'automation.config.ticket.aigc',
          }),
          disabled: true,
          path: '/settingTicket/aigcAssist',
          icon: AISettingIcon(),
        },
        {
          key: '1.2',
          name: getIntl().formatMessage({
            id: 'automation.config.smart.ticket',
          }),
          path: '/intelligentFormFilling',
          icon: AISettingIcon(),
        },
        {
          key: '1.3',
          name: getIntl().formatMessage({
            id: 'automation.config.auto.close',
          }),
          path: '/autoCloseSetting',
          icon: CloseTicketSettingIcon(),
        },
        {
          key: '1.4',
          name: getIntl().formatMessage({
            id: 'automation.config.merge.tickets',
          }),
          path: '/settingTicket/ticketMerge',
          icon: MergeTicketSettingIcon(),
        },
      ],
    },
    {
      key: '2',
      title: getIntl().formatMessage({
        id: 'automation.config.response.title.2',
      }),
      content: [
        {
          key: '2.1',
          name: getIntl().formatMessage({
            id: 'automation.config.seat.reply.classification',
          }),
          disabled: true,
          path: '/settingTicket/aigcAssist',
          icon: QuickReplySettingIcon(),
        },
        {
          key: '2.2',
          name: getIntl().formatMessage({
            id: 'automation.config.seat.reply',
          }),
          disabled: true,
          path: '/settingTicket/aigcAssist',
          icon: AgentQuickReplySettingIcon(),
        },
        {
          key: '2.3',
          name: getIntl().formatMessage({
            id: 'automation.config.inactive.reminder',
          }),
          path: '/inactiveMessageReminder',
          icon: InfoSettingIcon(),
        },
      ],
    },
    {
      key: '3',
      title: getIntl().formatMessage({
        id: 'automation.config.routing.title',
      }),
      content: [
        {
          key: '3.1',
          name: getIntl().formatMessage({
            id: 'automation.config.smart.routing',
          }),
          path: '/allocationRule',
          icon: RouteRuleSettingIcon(),
        },
      ],
    },
    {
      key: '4',
      title: getIntl().formatMessage({
        id: 'automation.config.alert.title',
      }),
      content: [
        {
          key: '4.1',
          name: getIntl().formatMessage({
            id: 'automation.config.ticket.alert',
          }),
          disabled: true,
          path: '/settingTicket/aigcAssist',
          icon: TicketWarningSettingIcon(),
        },
      ],
    },
    // 公司基本信息
    // {
    //   key: '5',
    //   title: getIntl().formatMessage({
    //     id: 'ticket.setting.menu.5',
    //   }),
    //   content: [
    //     {
    //       key: '5.1',
    //       name: getIntl().formatMessage({
    //         id: 'ticket.setting.menu.5.1',
    //       }),
    //       path: '/companyInfo',
    //       icon: InfoSettingIcon(),
    //     },
    //   ],
    // },
  ];
  const handleRoute = (path, key) => {
    setActiveTab(key);
    history.push(path);
  };
  return (
    <div>
      <p
        style={{
          fontSize: '24px',
          fontWeight: '700',
          color: '#333',
          marginBottom: '20px',
        }}
      >
        <FormattedMessage
          id="ticket.setting.menu.3"
          defaultMessage="自动化配置"
        />
      </p>
      <div>
        {menuList.map(item => {
          return (
            <div>
              <p
                style={{
                  fontWeight: 700,
                  fontSize: 14,
                }}
              >
                {item.title}
              </p>
              <div className={styles.automationSettingsMenu}>
                {item.content.map(content => {
                  return (
                    <div
                      className={`${styles.automationSettingsMenuItem} ${
                        activeTab === content.key
                          ? styles.automationSettingsMenuItemActive
                          : ''
                      }`}
                      style={{
                        cursor: content.disabled ? 'not-allowed' : 'pointer',
                      }}
                      onClick={() =>
                        content.disabled
                          ? {}
                          : handleRoute(content.path, content.key)
                      }
                    >
                      {content.icon}
                      {content.name}
                    </div>
                  );
                })}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
const TicketOrCustomerSettings = () => {
  const [activeTab, setActiveTab] = useState('');
  const menuList = [
    {
      key: '1',
      content: [
        {
          key: '1.1',
          name: getIntl().formatMessage({
            id: 'ticketOrCustomerSettings.config.title.1',
          }),
          path: '/settingTicket/ticketType',
          icon: TicketTypeIcon(),
        },
        {
          key: '1.2',
          name: getIntl().formatMessage({
            id: 'ticketOrCustomerSettings.config.title.2',
          }),
          path: '/settingTicket/ticketAttr',
          icon: TicketAttrIcon(),
        },
        {
          key: '1.3',
          name: getIntl().formatMessage({
            id: 'ticketOrCustomerSettings.config.title.3',
          }),
          path: '/settingTicket/configureCustomerExtensionInformation',
          icon: CustomerAttrIcon(),
        },
        {
          key: '1.4',
          name: getIntl().formatMessage({
            id: 'ticketOrCustomerSettings.config.title.4',
          }),
          path: '/permissionSettings',
          icon: PermissionsIcon(),
        },
      ],
    },
  ];
  const handleRoute = (path, key) => {
    setActiveTab(key);
    history.push(path);
  };
  return (
    <div>
      <p
        style={{
          fontSize: '24px',
          fontWeight: '700',
          color: '#333',
          marginBottom: '20px',
        }}
      >
        <FormattedMessage
          id="ticket.setting.menu.1"
          defaultMessage="工单与客户管理"
        />
      </p>
      <div>
        {menuList.map(item => {
          return (
            <div>
              <div className={styles.automationSettingsMenu}>
                {item.content.map(content => {
                  return (
                    <div
                      className={`${styles.automationSettingsMenuItem} ${
                        activeTab === content.key
                          ? styles.automationSettingsMenuItemActive
                          : ''
                      }`}
                      style={{
                        cursor: content.disabled ? 'not-allowed' : 'pointer',
                      }}
                      onClick={() =>
                        content.disabled
                          ? {}
                          : handleRoute(content.path, content.key)
                      }
                    >
                      {content.icon}
                      {content.name}
                    </div>
                  );
                })}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
const AgentOperationSettings = () => {
  const [activeTab, setActiveTab] = useState('');
  const menuList = [
    {
      key: '1',
      content: [
        {
          key: '1.1',
          name: getIntl().formatMessage({
            id: 'agentOperationSettings.config.title.1',
          }),
          path: '/settingTicket/ticketSLA',
          icon: TicketSlaIcon(),
        },
        {
          key: '1.2',
          name: getIntl().formatMessage({
            id: 'agentOperationSettings.config.title.2',
          }),
          path: '/settingTicket/agentWorkerTime',
          icon: TicketWorkerTimeIcon(),
        },
        {
          key: '1.3',
          name: getIntl().formatMessage({
            id: 'agentOperationSettings.config.title.3',
          }),
          path: '/settingTicket/manageAgentStatus',
          icon: AgentStatusIcon(),
        },
        {
          key: '1.4',
          name: getIntl().formatMessage({
            id: 'agentOperationSettings.config.title.4',
          }),
          path: '/settingTicket/manualWaiting',
          icon: HoldMusicIcon(),
        },
      ],
    },
  ];
  const handleRoute = (path, key) => {
    setActiveTab(key);
    history.push(path);
  };
  return (
    <div>
      <p
        style={{
          fontSize: '24px',
          fontWeight: '700',
          color: '#333',
          marginBottom: '20px',
        }}
      >
        <FormattedMessage id="ticket.setting.menu.2" />
      </p>
      <div>
        {menuList.map(item => {
          return (
            <div>
              <div className={styles.automationSettingsMenu}>
                {item.content.map(content => {
                  return (
                    <div
                      className={`${styles.automationSettingsMenuItem} ${
                        activeTab === content.key
                          ? styles.automationSettingsMenuItemActive
                          : ''
                      }`}
                      style={{
                        cursor: content.disabled ? 'not-allowed' : 'pointer',
                      }}
                      onClick={() =>
                        content.disabled
                          ? {}
                          : handleRoute(content.path, content.key)
                      }
                    >
                      {content.icon}
                      {content.name}
                    </div>
                  );
                })}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
const CompanyInfo = () => {
  const [activeTab, setActiveTab] = useState('');
  const menuList = [
    {
      key: '1',
      name: getIntl().formatMessage({
        id: 'ticket.setting.menu.5.1',
      }),
      path: '/companyInfo',
      icon: CompanyInfoIcon(),
    },
  ];
  const handleRoute = (path, key) => {
    setActiveTab(key);
    history.push(path);
  };
  return (
    <div>
      <p
        style={{
          fontSize: '24px',
          fontWeight: '700',
          color: '#333',
          marginBottom: '20px',
        }}
      >
        <FormattedMessage
          id="ticket.setting.menu.5"
          defaultMessage="公司基本信息"
        />
      </p>
      <div>
        <div className={styles.automationSettingsMenu}>
          {menuList.map(content => {
            return (
              <div
                className={`${styles.automationSettingsMenuItem} ${
                  activeTab === content.key
                    ? styles.automationSettingsMenuItemActive
                    : ''
                }`}
                style={{
                  cursor: content.disabled ? 'not-allowed' : 'pointer',
                }}
                onClick={() =>
                  content.disabled ? {} : handleRoute(content.path, content.key)
                }
              >
                {/*<MenuIcon />*/}
                {content.icon}
                {content.name}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
export default SettingTicket;
