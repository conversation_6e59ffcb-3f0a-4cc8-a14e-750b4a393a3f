import Banner from '../components/banner';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import TocCardBg from '@/components/TocCardBg';
import { FormattedMessage } from 'umi';
import styles from '../index.less';
import MotionFade from '@/components/MotionFade';
import { motion } from 'framer-motion';
import { isCNDomain } from '@/utils/utils';

const Assistant = () => {
  return (
    <div>
      <Header />
      <Banner
        titleFirstId="product.assistant.banner.title.first"
        titleSecondId="product.assistant.banner.title.second"
        subtitleId="product.assistant.banner.subtitle"
        image={require('@/assets/product/zuoxifuzhu.png')}
        videoSrc={
          isCNDomain()
            ? `https://connectnowai.com/video/AI-Agent-Assistance.mp4`
            : `http://connectnowai.com/video/4_Al%20Copilot.mp4`
        }
        bgColor="#ECF1FF"
      />
      <div className={styles.productSubpageContent}>
        <MotionFade
          as={motion.div}
          type="fadeUp"
          className={styles.productSubpageContentTitle}
        >
          <FormattedMessage id="product.assistant.content.title" />
        </MotionFade>
        <MotionFade
          as={motion.div}
          type="fadeUp"
          delay={0.1}
          className={styles.productSubpageContentDesc}
        >
          <FormattedMessage id="product.assistant.content.desc" />
        </MotionFade>
      </div>
      <TocCardBg
        titleId="product.assistant.card1.title"
        descId="product.assistant.card1.desc"
        children={
          <MotionFade
            as={motion.img}
            type="fadeRight"
            delay={0.2}
            style={{ width: '100%' }}
            src={require('@/assets/product/zuoxifuzhu1.png')}
            alt=""
          />
        }
      />
      <TocCardBg
        titleId="product.assistant.card2.title"
        descId="product.assistant.card2.desc"
        children={
          <MotionFade
            as={motion.img}
            type="fadeLeft"
            delay={0.2}
            style={{ width: '100%' }}
            src={require('@/assets/product/zuoxifuzhu2.png')}
            alt=""
          />
        }
        bgColor="#ECF1FF"
      />
      <TocCardBg
        titleId="product.assistant.card3.title"
        descId="product.assistant.card3.desc"
        children={
          <MotionFade
            as={motion.img}
            type="fadeRight"
            delay={0.2}
            style={{ width: '100%' }}
            src={require('@/assets/product/zuoxifuzhu3.png')}
            alt=""
          />
        }
      />
      <Footer />
    </div>
  );
};

export default Assistant;
