import Banner from '../components/banner';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import TocCardBg from '@/components/TocCardBg';
import styles from '../index.less';
import currentStyle from './index.less';
import { FormattedMessage } from 'umi';
import { useEffect } from 'react';
import MotionFade from '@/components/MotionFade';
import { motion } from 'framer-motion';
import { isCNDomain } from '@/utils/utils';
const Channel = () => {
  useEffect(() => {}, []);
  const renderCardChildren2 = () => {
    return (
      <div className={currentStyle.channel2}>
        <MotionFade
          as={motion.img}
          type="fadeLeft"
          src={require('@/assets/product/channel2.png')}
          scale={0.7}
          translateX="-20%"
          alt=""
        />
        <MotionFade
          as={motion.img}
          type="fadeRight"
          src={require('@/assets/product/channel21.png')}
          alt=""
        />
        <MotionFade
          as={motion.img}
          type="fadeRight"
          src={require('@/assets/product/channel22.png')}
          alt=""
        />
        <MotionFade
          as={motion.img}
          type="fadeRight"
          src={require('@/assets/product/channel23.png')}
          alt=""
        />
      </div>
    );
  };
  return (
    <div className={styles.channelPage}>
      <Header />
      <Banner
        titleFirstId="product.channel.title.first"
        titleSecondId="product.channel.title.second"
        subtitleId="product.channel.subtitle"
        image={require('@/assets/product/channel.png')}
        bgColor="#f9eefd"
        videoSrc={
          isCNDomain()
            ? `https://connectnowai.com/video/Omnichannel.mp4`
            : `https://www.connectnowai.com/video/1_Engage%20with%20Customers%20Instantly%20On%20a%20Single....mp4`
        }
      />
      <div className={styles.productSubpageContent}>
        <MotionFade
          as={motion.div}
          type="fadeUp"
          className={styles.productSubpageContentTitle}
        >
          <FormattedMessage id="product.channel.content.title" />
        </MotionFade>
        <MotionFade
          as={motion.div}
          type="fadeUp"
          delay={0.1}
          className={styles.productSubpageContentDesc}
        >
          <FormattedMessage id="product.channel.content.desc" />
        </MotionFade>
      </div>
      <TocCardBg
        title={<FormattedMessage id="product.channel.card1.title" />}
        desc={<FormattedMessage id="product.channel.card1.desc" />}
        children={
          <MotionFade
            as={motion.img}
            type="fadeRight"
            src={require('@/assets/product/channel1.png')}
            style={{ width: '100%' }}
            alt="channel1"
          />
        }
      />
      <TocCardBg
        title={<FormattedMessage id="product.channel.card2.title" />}
        desc={<FormattedMessage id="product.channel.card2.desc" />}
        children={renderCardChildren2()}
        bgColor="#f9eefd"
      />
      <TocCardBg
        title={<FormattedMessage id="product.channel.card3.title" />}
        desc={<FormattedMessage id="product.channel.card3.desc" />}
        children={
          <MotionFade
            as={motion.img}
            type="fadeRight"
            src={require('@/assets/product/channel3.png')}
            style={{ width: '100%' }}
            alt="channel3"
          />
        }
      />
      <Footer />
    </div>
  );
};

export default Channel;
