import React, {
  useState,
  useMemo,
  useCallback,
  useEffect,
  useRef,
  Children,
} from 'react';
import styles from './index.less';
import {
  Input,
  Select,
  Form,
  Spin,
  notification,
  Button,
  Row,
  Col,
  Radio,
  Checkbox,
  Popconfirm,
  Tag,
} from 'antd';
const { Option, OptGroup } = Select;
import { useDispatch, getIntl, FormattedMessage, history } from 'umi';
import { ChannelTypeSelect } from '@/components/channelSelect'; // 导入通用组件

const AllocationRuleAdd = () => {
  const dispatch = useDispatch();
  const formChannelType = useRef(null);
  const formRule = useRef(null);
  const [channelTypeList, setChannelTypeList] = useState([]); // 渠道类型
  const [channelType, setChannelType] = useState(''); // 来源渠道
  const [workRecordTypeList, setWorkRecordTypeList] = useState([]); //工单类型
  let [channelOptions, setChannelOptions] = useState([]);
  let [channelId, setChannelId] = useState([]); // 渠道类型下来源渠道
  const [callTeamList, setCallTeamList] = useState([]); // 团队
  const [callAgentList, setAgentList] = useState([]); // 座席
  let [ruleList, setRuleList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [standardTagList, setStandardTasList] = useState([]); // 客户标签
  let [res, setRes] = useState({});
  let [routingChannel, setRoutingChannelList] = useState([]); // 来源渠道
  let [languageList, setLanguageList] = useState([]); //  客户语言
  let [gradeList, setGradeList] = useState([]); //  客户等级
  let [customerCountry, setCustomerCountry] = useState([]); //  客户国家
  // 个性化配置新增规则
  let [uniquesList, setUniquesList] = useState([
    {
      conditionType: '',
      conditionValues: [],
    },
  ]);
  let [determineTypeValue, setDetermineTypeValue] = useState(1); //  判断类型
  // 判断规则
  let [routingRule, setRoutingRule] = useState([
    {
      judgmentRuleType: 1, // 1:任意条件；2:个性化配置；3:默认规则
      distributionRuleType: 1, // 1:分配给特定团队；2:分配给特定座席
      distributionIds: [], // 分配的目标id，团队就写团队id，座席就写座席
      stickiness: 1, // 粘性分配 0否 1是
      uniquesList: uniquesList,
    },
  ]);
  const [currentThreeChannel, setCurrentThreeChannel] = useState(0);
  const [threeChannelList, setThreeChannelList] = useState([
    {
      label: getIntl().formatMessage({
        id: 'allocation.applicable.channel.types.phone',
        defaultMessage: '电话',
      }),
      value: 0,
    },
    {
      label: getIntl().formatMessage({
        id: 'allocation.applicable.channel.types.chat',
        defaultMessage: '聊天',
      }),
      value: 1,
    },
    {
      label: getIntl().formatMessage({
        id: 'allocation.applicable.channel.types.email',
        defaultMessage: '邮件',
      }),
      value: 2,
    },
  ]);
  useEffect(() => {
    const fetchData = async () => {
      await queryAllStandardTag();
      await queryRuleList();
      await queryChannelTypeList();
      await queryWorkRecordType();
      // await getChannels();
      await queryTeamList();
      await querySeatsUser();
      await getLanguageList();
      await gradeListQuery();
      await queryCountryDef();
    };
    fetchData();
  }, []);
  useEffect(() => {
    if (history.location.state) {
      let { routingId, type } = history.location.state;
      const fetch = async () => {
        await queryAllStandardTag();
        if (routingId) {
          await handleEdit(routingId);
        }
      };
      fetch();
    } else {
      history.push('/allocationRule');
    }
  }, [history.location.state?.routingId]);

  useEffect(() => {
    if (formChannelType.current && res) {
      formChannelType.current?.setFieldsValue({
        channelType: res.channelType + '',
        channelId: channelId,
      });
      getChannels(res.channelType);
    }

    if (formRule.current && res.routingRule) {
      const initialData = res.routingRule.reduce((acc, rule, index) => {
        acc[`routingRule[${index}].distributionIds`] = rule.distributionIds;
        return acc;
      }, {});
      formRule.current.setFieldsValue(initialData);
    }
  }, [res]);

  // 处理字符串id变成数组id
  const handleidFomartArray = rules => {
    return (
      rules &&
      rules.map(rule => ({
        routingRuleId: rule.routingRuleId,
        judgmentRuleType: rule.judgmentRuleType,
        distributionRuleType: rule.distributionRuleType,
        stickiness: rule.stickiness,
        distributionIdsNameList: rule.distributionIdsNameList,
        distributionIds: rule.distributionIds
          ? rule.distributionIds.split(',').map(id => id.trim())
          : [],
        uniquesList: rule.uniquesList
          ? rule.uniquesList.map(item => ({
            ...item,
            conditionValues: formatConditionValues(item),
          }))
          : [],
      }))
    );
  };
  // 处理回显时电话和邮件默认展示字符串
  const formatConditionValues = item => {
    if (
      item.conditionType === 'customer_email' ||
      item.conditionType === 'customer_phone'
    ) {
      if (item.conditionValues && item.conditionValues.length > 0) {
        return item.conditionValues;
      }
      return '';
    } else {
      if (
        typeof item.conditionValues === 'string' &&
        item.conditionValues.length > 0
      ) {
        return item.conditionValues.split(',').map(val => val.trim());
      }
      return [];
    }
  };
  // 修改回显
  const handleEdit = routingId => {
    setLoading(true);
    dispatch({
      type: 'allocation/editRuleInfo',
      payload: { routingId: routingId },
      callback: response => {
        if (response.code == 200) {
          let { channelType, routingChannel } = response.data;
          if (history.location.state?.defaultRouting === 1) {
            setRoutingRule(prevRoutingRule => {
              const newRoutingRule = handleidFomartArray(
                response.data.routingRule,
              );
              return newRoutingRule;
            });
          }
          setCurrentThreeChannel(response.data.routingType);
          setRoutingChannelList(routingChannel); // 来源渠道
          setChannelType(channelType + '');
          let channelIds = routingChannel.map(item => item.channelId);
          setDetermineTypeValue(response.data.judgmentRuleType);
          setChannelId(channelIds);
          setRoutingRule(prevRoutingRule => {
            const newRoutingRule = handleidFomartArray(
              response.data.routingRule,
            );
            return newRoutingRule;
          });
          setRes(response.data);
          setLoading(false);
        }
      },
    });
  };
  const getLanguageList = () => {
    // dispatch({
    //   type: 'personalCenter/listLanguage',
    //   callback: response => {
    //     let { code, data, msg } = response;
    //     if (code === 200) {
    //       setLanguageList(data);
    //     } else {
    //       notification.error({
    //         message: msg,
    //       });
    //     }
    //   },
    // });
    // 翻译的语言列表
    const googleLanguage = JSON.parse(localStorage.getItem('languageLocal'));
    setLanguageList(googleLanguage);
  };
  // 查询客户等级接口
  const gradeListQuery = () => {
    dispatch({
      type: 'customerDataGroupManagement/gradeList',
      callback: response => {
        if (response.code == 200) {
          setGradeList(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询客户国家
  const queryCountryDef = () => {
    dispatch({
      type: 'allocation/queryCustomerCountryList',
      callback: response => {
        if (response.code == 200) {
          setCustomerCountry(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询客户标签
  const queryAllStandardTag = () => {
    dispatch({
      type: 'allocation/queryAllStandardTag',
      callback: response => {
        if (response.code == 200) {
          if (response.data) {
            console.log(response.data, 'setStandardTasList');
            setStandardTasList(response.data);
          }
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 查询路由规则
  const queryRuleList = () => {
    dispatch({
      type: 'workOrderCenter/queryRuleList',
      callback: response => {
        if (response.code == 200) {
          if (response.data) {
            setRuleList(response.data);
          }
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 查询座席
  const querySeatsUser = () => {
    dispatch({
      type: 'workOrderCenter/querySeatsUser',
      callback: response => {
        if (response.code == 200) {
          if (response.data) {
            setAgentList(response.data);
          }
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询团队列表
  const queryTeamList = () => {
    dispatch({
      type: 'workOrderCenter/queryCallDeptList',
      callback: response => {
        if (response.code == 200) {
          setCallTeamList(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 来源渠道
  const getChannels = value => {
    const params = {
      pageSize: 1000,
      pageNum: 1,
      channelTypeId: value,
    };
    if (params.channelTypeId == 0) {
      delete params.channelTypeId;
    }
    dispatch({
      type: 'channel/getChannel',
      payload: params,
      callback: res => {
        if (res.code === 200) {
          setChannelOptions(res.data.rows);
        }
      },
    });
  };

  // 查询工单类型列表
  const queryWorkRecordType = () => {
    dispatch({
      type: 'workOrderCenter/queryWorkRecordType',
      callback: response => {
        if (response.code == 200) {
          let workRecordTypeList = response.data;
          setWorkRecordTypeList(workRecordTypeList);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 查询渠道类型列表
  const queryChannelTypeList = async () => {
    await dispatch({
      type: 'newChannelConfiguration/queryNewChannelTypeList',
      callback: response => {
        if (response.code == 200) {
          const newData = [
            {
              code: '0',
              name: getIntl().formatMessage({
                id: 'marketing.channel.type.all.small',
                defaultMessage: '所有渠道',
              }),
            },
            ...response.data,
          ];
          setChannelTypeList(newData);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 取消
  const handleBack = () => {
    history.push('/allocationRule');
  };

  // 座席团队回显
  const renderTags = (distributionIds, dataList) => {
    if (!distributionIds) return null;
    const matchedDepts = [];
    distributionIds.forEach(distributionId => {
      const matchedDept =
        dataList && dataList.find(dept => dept.deptId === distributionId);
      if (matchedDept) {
        matchedDepts.push({
          deptId: matchedDept.deptId,
          deptName: matchedDept.deptName,
        });
      }
    });
    if (matchedDepts.length > 0) {
      return matchedDepts.map(matchedDept => (
        <Tag className={styles.tagsSty} key={matchedDept.deptId}>
          <span>{matchedDept.deptName}</span>
        </Tag>
      ));
    }
    return null;
  };
  // 坐席回显
  const renderAgentTags = (distributionIds, dataList) => {
    if (!distributionIds) return null;
    const matchedDepts = [];
    distributionIds.forEach(distributionId => {
      const matchedDept =
        dataList && dataList.find(dept => dept.userId === distributionId);
      if (matchedDept) {
        matchedDepts.push({
          userId: matchedDept.userId,
          userName: matchedDept.userName,
        });
      }
    });
    if (matchedDepts.length > 0) {
      return matchedDepts.map(matchedDept => (
        <Tag className={styles.tagsSty} key={matchedDept.userId}>
          <span>{matchedDept.userName}</span>
        </Tag>
      ));
    }
    return null;
  };
  // 工单类型回显
  const renderTagsWorkType = (conditionValues, workRecordTypeList) => {
    if (!conditionValues) return null;
    const matchedDepts = [];
    conditionValues.forEach(conditionValue => {
      const matchedDept =
        workRecordTypeList &&
        workRecordTypeList.find(
          item => item.workRecordTypeId == conditionValue,
        );
      if (matchedDept) {
        matchedDepts.push({
          workRecordTypeId: matchedDept.workRecordTypeId,
          workRecordTypeName: matchedDept.workRecordTypeName,
        });
      }
    });
    if (matchedDepts.length > 0) {
      return matchedDepts.map(matchedDept => (
        <Tag className={styles.tagsSty} key={matchedDept.workRecordTypeId}>
          <span>{matchedDept.workRecordTypeName}</span>
        </Tag>
      ));
    }
    return null;
  };
  // 客户语言回显
  const renderTagsCustomerLanguage = (conditionValues, languageList) => {
    if (!conditionValues) return null;
    const matchedDepts = [];
    conditionValues.forEach(conditionValue => {
      const matchedDept =
        languageList && languageList.find(item => item.label == conditionValue);
      if (matchedDept) {
        matchedDepts.push({
          languageCode: matchedDept.value,
          languageName: matchedDept.label,
        });
      }
    });
    if (matchedDepts.length > 0) {
      return matchedDepts.map(matchedDept => (
        <Tag className={styles.tagsSty} key={matchedDept.languageCode}>
          <span>{matchedDept.languageName}</span>
        </Tag>
      ));
    }
    return null;
  };

  // 客户等级
  const renderTagsCustomerlevel = (conditionValues, gradeList) => {
    if (!conditionValues) return null;
    const matchedDepts = [];
    conditionValues.forEach(conditionValue => {
      const matchedDept =
        gradeList && gradeList.find(item => item.gradeId == conditionValue);
      if (matchedDept) {
        matchedDepts.push({
          gradeId: matchedDept.gradeId,
          gradeName: matchedDept.gradeName,
        });
      }
    });
    if (matchedDepts.length > 0) {
      return matchedDepts.map(matchedDept => (
        <Tag className={styles.tagsSty} key={matchedDept.gradeId}>
          <span>{matchedDept.gradeName}</span>
        </Tag>
      ));
    }
    return null;
  };

  // 客户国家
  const renderTagsCustomerCountry = (conditionValues, customerCountry) => {
    if (!conditionValues) return null;
    const matchedDepts = [];
    conditionValues.forEach(conditionValue => {
      const matchedDept =
        customerCountry &&
        customerCountry.find(item => item.countryCode == conditionValue);
      if (matchedDept) {
        matchedDepts.push({
          countryCode: matchedDept.countryCode,
          countryName: matchedDept.countryName,
        });
      }
    });
    if (matchedDepts.length > 0) {
      return matchedDepts.map(matchedDept => (
        <Tag className={styles.tagsSty} key={matchedDept.countryCode}>
          <span>{matchedDept.countryName}</span>
        </Tag>
      ));
    }
    return null;
  };
  // 客户标签回显
  const renderTagsCustomerList = (conditionValues, standardTagList) => {
    if (!conditionValues) return null;
    if (!standardTagList) return null;
    const tagList = standardTagList.flatMap(item => item.tagList);
    const matchedDepts = [];
    conditionValues.forEach(conditionValue => {
      const matchedDept =
        tagList && tagList.find(item => item.tagId == conditionValue);
      if (matchedDept) {
        matchedDepts.push({
          tagId: matchedDept.tagId,
          tagContent: matchedDept.tagContent,
          tagColorCode: matchedDept.tagColorCode || 'colorType1',
        });
      }
    });
    if (matchedDepts.length > 0) {
      let colorTypeMap = new Map([
        ['colorType1', 'colorType1'],
        ['colorType2', 'colorType2'],
        ['colorType3', 'colorType3'],
        ['colorType4', 'colorType4'],
        ['colorType5', 'colorType5'],
        ['colorType6', 'colorType6'],
        ['colorType7', 'colorType7'],
        ['colorType8', 'colorType8'],
        ['colorType9', 'colorType9'],
        ['colorType10', 'colorType10'],
        ['colorType11', 'colorType11'],
        // 可以根据需要添加更多映射关系
      ]);
      return (
        <div className={styles.customer}>
          {matchedDepts.map(matchedDept => (
            <Tag
              key={matchedDept.tagId}
              className={`${styles.customer} ${styles[
                colorTypeMap.get(matchedDept.tagColorCode) || 'colorType1'
                ]
                }`}
            >
              <span>{matchedDept.tagContent}</span>
            </Tag>
          ))}
        </div>
      );
    }
    return null;
  };
  // 渠道来源回显
  const renderTagsChnnelList = (conditionValues, channelOptions) => {
    if (!conditionValues) return null;
    const matchedDepts = [];
    conditionValues.forEach(conditionValue => {
      const matchedDept =
        channelOptions &&
        channelOptions.find(item => item.channelId == conditionValue);
      if (matchedDept) {
        matchedDepts.push({
          channelId: matchedDept.channelId,
          name: matchedDept.name,
          tagColorCode: matchedDept.tagColorCode || 'colorType1',
        });
      }
    });
    if (matchedDepts.length > 0) {
      return matchedDepts.map(matchedDept => (
        <div key={matchedDept.channelId}>
          <Tag className={styles.tagsSty}>
            <span>{matchedDept.name}</span>
          </Tag>
        </div>
      ));
    }
    return null;
  };

  return (
    <Spin spinning={loading}>
      {+history.location.state?.defaultRouting === 1 ? (
        routingRule &&
        routingRule.length > 0 &&
        routingRule.map((ruleItem, ruleIndex) => {
          return (
            <div className={styles.defaultStyle}>
              <div className={styles.header}>
                <p className="blueBorder">
                  <FormattedMessage
                    id="allocation.intelligent.rules"
                    defaultMessage="智能分配规则"
                  />
                </p>
              </div>
              <div className={styles.selectWrapper}>
                <p className={styles.defaultTitle}>
                  <FormattedMessage
                    id="allocation.default.rules"
                    defaultMessage="默认规则"
                  />
                </p>
                <p className={styles.defaultTooip}>
                  <FormattedMessage
                    id="allocation.default.tooltip.rules"
                    defaultMessage="当系统没有适配到任何其他规则，都会路由到默认规则，请设置默认情况下工单分配的对象"
                  />
                </p>
                <div style={{ marginBottom: '10px' }}>
                  <Radio.Group value={+ruleItem.distributionRuleType} disabled>
                    <Radio value={1}>
                      <FormattedMessage
                        id="allocation.assign.to.specific.team"
                        defaultMessage="分配给特定团队"
                      />
                    </Radio>
                    <Radio value={2}>
                      <FormattedMessage
                        id="allocation.assign.to.specific.agent"
                        defaultMessage="分配给特定座席"
                      />
                    </Radio>
                  </Radio.Group>
                </div>
                <Form>
                  {+ruleItem.distributionRuleType === 2 ? (
                    <Row>
                      <Col span={16}>
                        <Form.Item
                          label={
                            <FormattedMessage
                              id="allocation.assign.to.specific.agent"
                              defaultMessage="分配给特定座席"
                            />
                          }
                          name={`routingRule[${ruleIndex}].distributionIds`}
                        >
                          {renderAgentTags(
                            ruleItem.distributionIds,
                            callAgentList,
                          )}
                        </Form.Item>
                      </Col>
                    </Row>
                  ) : (
                    <Row>
                      <Col span={16}>
                        <Form.Item
                          name={`routingRule[${ruleIndex}].distributionIds`}
                          label={
                            <FormattedMessage
                              id="allocation.assign.to.specific.team"
                              defaultMessage="分配给特定团队"
                            />
                          }
                        >
                          {renderTags(ruleItem.distributionIds, callTeamList)}
                        </Form.Item>
                      </Col>
                    </Row>
                  )}
                </Form>
                <div>
                  <Checkbox checked={ruleItem.stickiness} disabled>
                    <span style={{ fontSize: '14px' }}>
                      <FormattedMessage
                        id="allocation.sticky.distribution"
                        defaultMessage="粘性分配"
                      />
                    </span>
                  </Checkbox>
                  <p
                    style={{
                      color: '#999',
                      fontSize: '12px',
                      marginTop: '8px',
                    }}
                  >
                    <FormattedMessage
                      id="allocation.when.the.system.assigns.tickets.it.will.try.to.route.them.to.the.agent.that.served.the.current.customer.last.time"
                      defaultMessage="系统进行工单分配时，会尽量路由到上次服务过当前客户的座席"
                    />
                  </p>
                </div>
              </div>
              <div className={styles.defaultFooterBtn}>
                {
                  <>
                    <Button onClick={handleBack} className={styles.backHover}>
                      <FormattedMessage
                        id="work.record.return"
                        defaultMessage="返回"
                      />
                    </Button>
                  </>
                }
              </div>
            </div>
          );
        })
      ) : (
        <div className={styles.wrapperContent}>
          <p className="blueBorder">
            <FormattedMessage id="channel.type" defaultMessage="渠道类型" />
          </p>
          {/* 选择当前适用渠道 */}
          <div className={styles.selectWrapper}>
            <p>
              <FormattedMessage
                id="allocationRule.channel.type"
                defaultMessage=" 请选择渠道类型"
              />
            </p>
            {/* 1.电话 2.聊天 3.邮件 */}
            <div className={styles.channelTypeWrapper}>
              {threeChannelList.map(item => (
                <div
                  key={item.value}
                  className={`${styles.channelTypeItem} ${currentThreeChannel === item.value ? styles.active : ''
                    }`}
                >
                  <Radio
                    value={item.value}
                    checked={currentThreeChannel === item.value}
                    disabled
                  >
                    {item.label}
                  </Radio>
                </div>
              ))}
            </div>
            <Form name="basic" ref={formChannelType}>
              <Row>
                <Col>
                  <Form.Item
                    label={
                      <FormattedMessage
                        id="channel.type"
                        defaultMessage="渠道类型"
                      />
                    }
                    name="channelType"
                  >
                    {/* <Select
                      style={{ width: '300px' }}
                      disabled={history.location.state?.type === 'detail'}
                      value={channelType}
                      popupClassName="selectFilterContent"
                      allowClear
                      placeholder={getIntl().formatMessage({
                        id:
                          'email.channel.configuration.channel.type.placeholder',
                        defaultValue: '请选择渠道类型',
                      })}
                    >
                      {channelTypeList &&
                        channelTypeList.map(items => {
                          if (items.code == '0') {
                            return (
                              <Option value={items.code} key={items.code}>
                                <img src={AllchannelIcon} />
                                {items.name}
                              </Option>
                            );
                          } else if (items.code == '1') {
                            return (
                              <Option value={items.code} key={items.code}>
                                <img src={EmailIcon} />
                                {items.name}
                              </Option>
                            );
                          } else if (items.code == '3') {
                            return (
                              <Option value={items.code} key={items.code}>
                                <img src={FacebookIcon} />
                                {items.name}
                              </Option>
                            );
                          } else if (items.code == '4') {
                            return (
                              <Option value={items.code} key={items.code}>
                                <img src={WhatsAppIcon} />
                                {items.name}
                              </Option>
                            );
                          } else if (items.code == '5') {
                            return (
                              <Option value={items.code} key={items.code}>
                                <img src={TwitterIcon} />
                                {items.name}
                              </Option>
                            );
                          } else if (items.code == '6') {
                            return (
                              <Option value={items.code} key={items.code}>
                                <img src={LineIcon} /> {items.name}
                              </Option>
                            );
                          } else if (items.code == '7') {
                            return (
                              <Option value={items.code} key={items.code}>
                                <img src={PhoneIcon} /> {items.name}
                              </Option>
                            );
                          } else if (items.code == '8') {
                            return (
                              <Option value={items.code} key={items.code}>
                                <img src={ChatIcon} />
                                {items.name}
                              </Option>
                            );
                          } else if (items.code == '9') {
                            return (
                              <Option value={items.code} key={items.code}>
                                <img src={AppChatOutlinedIcon} />
                                {items.name}
                              </Option>
                            );
                          } else if (items.code == '10') {
                            return (
                              <Option value={items.code} key={items.code}>
                                <img src={WebVideoOutlinedIcon} />
                                {items.name}
                              </Option>
                            );
                          } else if (items.code == '11') {
                            return (
                              <Option value={items.code} key={items.code}>
                                <img src={AppVideoOutlinedIcon} />
                                {items.name}
                              </Option>
                            );
                          } else if (items.code == '12') {
                            return (
                              <Option value={items.code} key={items.code}>
                                <img src={AwsChannelIcon} />
                                {items.name}
                              </Option>
                            );
                          } else if (items.code == '13') {
                            return (
                              <Option value={items.code} key={items.code}>
                                <img src={NewInstagramIcon} />
                                {items.name}
                              </Option>
                            );
                          } else if (items.code == '14') {
                            return (
                              <Option value={items.code} key={items.code}>
                                <img src={NewLineIcon} />
                                {items.name}
                              </Option>
                            );
                          } else if (items.code == '15') {
                            return (
                              <Option value={items.code} key={items.code}>
                                <img src={NewWeComIcon} />
                                {items.name}
                              </Option>
                            );
                          } else if (items.code == '16') {
                            return (
                              <Option value={items.code} key={items.code}>
                                <img src={NewWechatOfficialAccountIcon} />
                                {items.name}
                              </Option>
                            );
                          } else if (items.code == '17') {
                            return (
                              <Option value={items.code} key={items.code}>
                                <img src={NewWebOnlineVoiceIcon} />
                                {items.name}
                              </Option>
                            );
                          } else if (items.code == '18') {
                            return (
                              <Option value={items.code} key={items.code}>
                                <img src={NewAppOnlineVoiceIcon} />
                                {items.name}
                              </Option>
                            );
                          } else if (items.code == '19') {
                            return (
                              <Option value={items.code} key={items.code}>
                                <img src={NewTwitterIcon} />
                                {items.name}
                              </Option>
                            );
                          } else if (items.code == '20') {
                            return (
                              <Option value={items.code} key={items.code}>
                                <img src={NewTelegramIcon} />
                                {items.name}
                              </Option>
                            );
                          } else if (items.code == '21') {
                            return (
                              <Option value={items.code} key={items.code}>
                                <img src={NewWeChatMiniProgramIcon} />
                                {items.name}
                              </Option>
                            );
                          } else if (items.code == '22') {
                            return (
                              <Option value={items.code} key={items.code}>
                                <img src={NewShopifyIcon} />
                                {items.name}
                              </Option>
                            );
                          } else if (items.code == '23') {
                            return (
                              <Option value={items.code} key={items.code}>
                                <img src={NewGooglePlayIcon} />
                                {items.name}
                              </Option>
                            );
                          } else {
                            return (
                              <Option value={items.code} key={items.code}>
                                {items.name}
                              </Option>
                            );
                          }
                        })}
                    </Select> */}
                    <ChannelTypeSelect
                      channelTypeList={channelTypeList}
                      value={channelType}
                      popupClassName="selectFilterContent"
                      defaultStyle={{ width: '300px' }}
                      disabled={history.location.state?.type === 'detail'}
                    />
                  </Form.Item>
                </Col>
                <Col style={{ marginLeft: '20px' }}>
                  <Form.Item
                    label={
                      <FormattedMessage
                        id="contact.customers.title.other.information.source.channel"
                        defaultMessage="来源渠道"
                      />
                    }
                    name="channelId"
                  >
                    {routingChannel?.map((tag, index) => {
                      return (
                        <Tag className={styles.detailTag} key={tag.channelId}>
                          <span>{tag.routingChannelName}</span>
                        </Tag>
                      );
                    })}
                  </Form.Item>
                </Col>
              </Row>
            </Form>
            <p>
              <FormattedMessage
                id="allocationRule.channel.rules.type"
                defaultMessage=" 判断类型"
              />
            </p>
            <Row>
              <Col>
                <Radio.Group disabled={true} value={+determineTypeValue}>
                  <Radio value={1}>
                    <FormattedMessage
                      id="allocationRule.any.condition"
                      defaultMessage="任意条件"
                    />
                  </Radio>
                  <Radio value={2}>
                    <FormattedMessage
                      id="allocationRule.personalized.configuration"
                      defaultMessage="个性化配置"
                    />
                  </Radio>
                </Radio.Group>
              </Col>
            </Row>
          </div>

          {/* 判断规则 */}
          <Form ref={formRule} name="basicTwo">
            {routingRule &&
              routingRule.length > 0 &&
              routingRule.map((ruleItem, ruleIndex) => {
                return (
                  <div key={`rule-${ruleIndex}`}>
                    <div className={styles.rulesBox}>
                      {/* 选择个性化配置 */}
                      {+ruleItem.judgmentRuleType === 2 ? (
                        <div>
                          {ruleItem.uniquesList.map((item, index) => {
                            return (
                              <div
                                className={styles.personalityBox}
                                key={index}
                              >
                                <div className={styles.personalitySty}>
                                  <span
                                    style={{
                                      marginRight: 12,
                                      color: '#333',
                                      fontSize: '12px',
                                    }}
                                  >
                                    <FormattedMessage
                                      id="allocation.personal.when"
                                      defaultMessage="当"
                                    />
                                  </span>
                                  <Select
                                    disabled={true}
                                    optionLabelProp="label"
                                    optionFilterProp="children"
                                    allowClear
                                    style={{
                                      // width: '100%',
                                      width: 300,
                                    }}
                                    // placeholder={getIntl().formatMessage({
                                    //   id: 'allocation.please.select',
                                    // })}
                                    // options={ruleList}
                                    value={item.conditionType}
                                  // fieldNames={{
                                  //   label: 'name',
                                  //   value: 'code',
                                  //   key: 'code',
                                  // }}
                                  >
                                    {ruleList?.map(group => (
                                      <OptGroup
                                        key={group.groupCode}
                                        label={group.groupName}
                                      >
                                        {group.routingRuleList.map(option => (
                                          <Option
                                            key={option.code}
                                            value={option.code}
                                            label={option.name}
                                          >
                                            <div
                                              style={{
                                                display: 'flex',
                                                alignItems: 'center',
                                              }}
                                            >
                                              <div
                                                style={{
                                                  width: '12px',
                                                  height: '12px',
                                                  backgroundColor:
                                                    option.tagColor,
                                                  marginRight: '4px',
                                                }}
                                              ></div>
                                              <span>{option.name}</span>
                                            </div>
                                          </Option>
                                        ))}
                                      </OptGroup>
                                    ))}
                                  </Select>
                                  <span
                                    style={{
                                      marginRight: 12,
                                      marginLeft: 12,
                                      color: '#333',
                                      fontSize: '12px',
                                    }}
                                  >
                                    <FormattedMessage
                                      id="allocation.personal.include"
                                      defaultMessage="包含"
                                    />
                                  </span>
                                  {item.conditionType === 'ticket_type' ? (
                                    renderTagsWorkType(
                                      item.conditionValues,
                                      workRecordTypeList,
                                    )
                                  ) : item.conditionType === 'channel_name' ? (
                                    renderTagsChnnelList(
                                      item.conditionValues,
                                      channelOptions,
                                    )
                                  ) : item.conditionType ===
                                    'customer_label' ? (
                                    <div
                                      className={styles.customerTagContainer}
                                    >
                                      {renderTagsCustomerList(
                                        item.conditionValues,
                                        standardTagList,
                                      )}
                                    </div>
                                  ) : item.conditionType ==
                                    'customer_preferred_language' ? (
                                    <div
                                      className={styles.customerTagContainer}
                                    >
                                      {renderTagsCustomerLanguage(
                                        item.conditionValues,
                                        languageList,
                                      )}
                                    </div>
                                  ) : item.conditionType == 'customer_level' ? (
                                    <div
                                      className={styles.customerTagContainer}
                                    >
                                      {renderTagsCustomerlevel(
                                        item.conditionValues,
                                        gradeList,
                                      )}
                                    </div>
                                  ) : item.conditionType ==
                                    'customer_country' ? (
                                    <div
                                      className={styles.customerTagContainer}
                                    >
                                      {renderTagsCustomerCountry(
                                        item.conditionValues,
                                        customerCountry,
                                      )}
                                    </div>
                                  ) : item.conditionType == 'customer_email' ? (
                                    <span
                                      className={styles.tagsSty}
                                      style={{
                                        color: '#3463fc',
                                        border:
                                          '1px solid rgba(52, 99, 252, 0.5)',
                                        padding: '0px 7px',
                                      }}
                                    >
                                      {item.conditionValues}
                                    </span>
                                  ) : item.conditionType == 'customer_phone' ? (
                                    <span
                                      className={styles.tagsSty}
                                      style={{
                                        color: '#3463fc',
                                        border:
                                          '1px solid rgba(52, 99, 252, 0.5)',
                                        padding: '0px 7px',
                                      }}
                                    >
                                      {item.conditionValues}
                                    </span>
                                  ) : (
                                    ''
                                  )}
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      ) : (
                        ''
                      )}
                      {/* 分配给团队和座席 */}
                      <Row>
                        <Col span={12}>
                          <p
                            style={{
                              color: '#333',
                              fontSize: '14px',
                              fontWeight: 700,
                            }}
                          >
                            <FormattedMessage
                              id="allocation.rules"
                              defaultMessage="分配规则"
                            />
                          </p>
                          <p style={{ color: '#333', fontSize: '14px' }}>
                            <FormattedMessage
                              id="allocation.execution.rule"
                              defaultMessage="规则名称"
                            />
                            : {ruleItem.ruleName}
                          </p>
                          <Form.Item>
                            <Radio.Group
                              disabled={true}
                              value={+ruleItem.distributionRuleType}
                            >
                              <Radio value={1}>
                                <FormattedMessage
                                  id="allocation.assign.to.specific.team"
                                  defaultMessage="分配给特定团队"
                                />
                              </Radio>
                              <Radio value={2}>
                                <FormattedMessage
                                  id="allocation.assign.to.specific.agent"
                                  defaultMessage="分配给特定座席"
                                />
                              </Radio>
                            </Radio.Group>
                          </Form.Item>
                        </Col>
                      </Row>
                      {+ruleItem.distributionRuleType === 2 ? (
                        <Row>
                          <Col span={16}>
                            <Form.Item
                              label={
                                <FormattedMessage
                                  id="allocation.assign.to.specific.agent"
                                  defaultMessage="分配给特定座席"
                                />
                              }
                              name={`routingRule[${ruleIndex}].distributionIds`}
                            >
                              {renderAgentTags(
                                ruleItem.distributionIds,
                                callAgentList,
                              )}
                            </Form.Item>
                          </Col>
                        </Row>
                      ) : (
                        <Row>
                          <Col span={16}>
                            <Form.Item
                              name={`routingRule[${ruleIndex}].distributionIds`}
                              label={
                                <FormattedMessage
                                  id="allocation.assign.to.specific.team"
                                  defaultMessage="分配给特定团队"
                                />
                              }
                            >
                              {renderTags(
                                ruleItem.distributionIds,
                                callTeamList,
                              )}
                            </Form.Item>
                          </Col>
                        </Row>
                      )}
                      {/* 粘性分配 */}
                      <Row>
                        <Col>
                          <Form.Item>
                            <Checkbox
                              disabled={true}
                              checked={+ruleItem.stickiness}
                            >
                              <span style={{ fontSize: '14px' }}>
                                <FormattedMessage
                                  id="allocation.sticky.distribution"
                                  defaultMessage="粘性分配"
                                />
                              </span>
                            </Checkbox>
                            <p
                              style={{
                                color: '#999',
                                fontSize: '12px',
                                marginTop: '8px',
                              }}
                            >
                              <FormattedMessage
                                id="allocation.when.the.system.assigns.tickets.it.will.try.to.route.them.to.the.agent.that.served.the.current.customer.last.time"
                                defaultMessage="系统进行工单分配时，会尽量路由到上次服务过当前客户的座席"
                              />
                            </p>
                          </Form.Item>
                        </Col>
                      </Row>
                    </div>
                    <div></div>
                  </div>
                );
              })}
          </Form>
          <div className={styles.footerBtn}>
            <Button onClick={handleBack} className={styles.footerBtnHover}>
              <FormattedMessage id="work.record.return" defaultMessage="返回" />
            </Button>
          </div>
        </div>
      )}
    </Spin>
  );
};

export default AllocationRuleAdd;
