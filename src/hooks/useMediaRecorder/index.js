// 导入 React 核心库和必要的 Hooks
import React, { useEffect, useRef, useState, useCallback } from 'react';
import { getIntl } from 'umi';
import { notification } from 'antd';

// 定义连续音频录制 Hook
const useMediaRecorder = onChunkReady => {
  // 录制状态：是否正在录音
  const [recording, setRecording] = useState(false);
  // 录音片段数组：存储每个时间间隔录制的音频片段
  const [recordings, setRecordings] = useState([]);
  // 合并后的音频 URL：用于播放完整的录音
  const [mergedUrl, setMergedUrl] = useState(null);

  // MediaRecorder 实例的引用：用于控制录音
  const recorderRef = useRef(null);
  // 媒体流引用：用于管理麦克风音频流
  const streamRef = useRef(null);
  // 定时器引用：用于定时请求录音数据
  const intervalRef = useRef(null);
  // 录音数据临时存储：解决状态更新异步问题
  const recordingChunksRef = useRef([]);
  // 停止录音标志：避免重复处理最后的录音片段
  const isStoppingRef = useRef(false);
  // 音频上下文引用
  const audioContextRef = useRef(null);

  // 指定录音的 MIME 类型：WebM 格式，使用 Opus 编解码器
  const mimeType = 'audio/webm;codecs=opus';

  // 获取通话录制的音频约束配置
  const getCallRecordingConstraints = useCallback((stereo = false) => {
    return {
      audio: {
        sampleRate: { ideal: 16000, min: 16000 }, // 修改为16kHz采样率
        channelCount: { ideal: stereo ? 2 : 1 }, // 支持双声道或单声道
        echoCancellation: false, // 关闭回声消除，保留通话双方声音
        noiseSuppression: false, // 关闭噪声抑制，避免影响对方声音
        autoGainControl: false, // 关闭自动增益控制
        googEchoCancellation: false, // Chrome特定：关闭回声消除
        googNoiseSuppression: false, // Chrome特定：关闭噪声抑制
        googAutoGainControl: false, // Chrome特定：关闭自动增益
        googHighpassFilter: false, // Chrome特定：关闭高通滤波器
        mozEchoCancellation: false, // Firefox特定：关闭回声消除
        mozNoiseSuppression: false, // Firefox特定：关闭噪声抑制
        mozAutoGainControl: false, // Firefox特定：关闭自动增益
        // 立体声相关设置
        ...(stereo && {
          googAudioMirroring: false,
          latency: 0.01,
        }),
      },
      video: false,
    };
  }, []);

  // 创建专门用于通话录制的MediaRecorder配置
  const getCallRecorderOptions = useCallback(() => {
    return {
      mimeType: 'audio/webm;codecs=opus',
      audioBitsPerSecond: 64000, // 调整比特率适配16kHz采样率
    };
  }, []);

  // 创建纯原生的双声道录音流
  const createNativeStereoStream = useCallback(
    async (localStream, remoteStream) => {
      try {
        console.log('===native===开始创建原生双声道流');
        // 创建音频上下文
        const audioContext = new (window.AudioContext ||
          window.webkitAudioContext)({
          sampleRate: 16000,
          latencyHint: 'interactive',
        });
        audioContextRef.current = audioContext;
        // 创建音频源
        let leftSource = null;
        let rightSource = null;
        // 创建目标流
        const destination = audioContext.createMediaStreamDestination();
        // 创建立体声合并器
        const merger = audioContext.createChannelMerger(2);
        // 创建增益控制器
        const leftGain = audioContext.createGain();
        const rightGain = audioContext.createGain();
        // 设置音量（可以后续通过API调整）
        leftGain.gain.value = 1.0;
        rightGain.gain.value = 1.0;
        if (!localStream && !remoteStream) {
          // 获取用户媒体设备列表
          const devices = await navigator.mediaDevices.enumerateDevices();
          const audioInputs = devices.filter(
            device => device.kind === 'audioinput',
          );

          console.log(
            '===native===可用音频输入设备:',
            audioInputs,
            devices,
            audioInputs.length,
          );

          if (audioInputs.length >= 2) {
            // 如果有多个音频输入设备，使用不同设备作为左右声道
            console.log('===native===使用双设备模式');

            try {
              // 左声道：使用第一个设备
              const leftStream = await navigator.mediaDevices.getUserMedia({
                audio: {
                  deviceId: audioInputs[0].deviceId,
                  sampleRate: 16000,
                  channelCount: 1,
                  echoCancellation: false,
                  noiseSuppression: false,
                  autoGainControl: false,
                },
              });

              // 右声道：使用第二个设备（如果有的话）
              const rightStream = await navigator.mediaDevices.getUserMedia({
                audio: {
                  deviceId: audioInputs[1]
                    ? audioInputs[1].deviceId
                    : audioInputs[0].deviceId,
                  sampleRate: 16000,
                  channelCount: 1,
                  echoCancellation: false,
                  noiseSuppression: false,
                  autoGainControl: false,
                },
              });

              leftSource = audioContext.createMediaStreamSource(leftStream);
              rightSource = audioContext.createMediaStreamSource(rightStream);

              console.log('===native===双设备音频源创建成功');
            } catch (error) {
              console.log(
                '===native===双设备模式失败，降级为单设备双声道:',
                error,
              );
              // 降级处理
            }
          }
          //否则走下面
          if (!leftSource || !rightSource) {
            // 单设备模式：获取立体声音频流并分离左右声道
            console.log('===native===使用单设备立体声模式');

            const stereoStream = await navigator.mediaDevices.getUserMedia({
              audio: {
                sampleRate: 16000,
                channelCount: 2, // 强制立体声
                echoCancellation: false,
                noiseSuppression: false,
                autoGainControl: false,
              },
            });

            // 创建音频源
            const stereoSource = audioContext.createMediaStreamSource(
              stereoStream,
            );

            // 创建声道分离器
            const splitter = audioContext.createChannelSplitter(2);

            // 连接源到分离器
            stereoSource.connect(splitter);

            // 分别处理左右声道
            leftSource = splitter;
            rightSource = splitter;

            console.log('===native===立体声源和分离器创建成功');
          }
        } else {
          leftSource = audioContext.createMediaStreamSource(localStream);
          rightSource = audioContext.createMediaStreamSource(remoteStream);
        }
        // 连接音频路径：左声道
        leftSource.connect(leftGain);
        leftGain.connect(merger, 0, 0); // 输入声道0连接到输出左声道

        // 连接音频路径：右声道
        rightSource.connect(rightGain);
        rightGain.connect(merger, 0, 1); // 输入声道0连接到输出右声道

        // 连接到最终输出
        merger.connect(destination);

        console.log(
          '===native===原生双声道流创建完成',
          merger,
          rightSource,
          leftSource,
          destination,
        );

        return {
          stream: destination.stream,
          audioContext,
          leftGain,
          rightGain,
          stereoMode: true,
          controls: {
            setLeftVolume: volume => {
              leftGain.gain.value = volume;
            },
            setRightVolume: volume => {
              rightGain.gain.value = volume;
            },
            muteLeft: () => {
              leftGain.gain.value = 0;
            },
            muteRight: () => {
              rightGain.gain.value = 0;
            },
            unmuteLeft: () => {
              leftGain.gain.value = 1.0;
            },
            unmuteRight: () => {
              rightGain.gain.value = 1.0;
            },
          },
        };
      } catch (error) {
        console.error('===native===创建原生双声道流失败:', error);
        throw error;
      }
    },
    [],
  );

  // 解析音频信息的函数
  const parseAudioInfo = useCallback(async blob => {
    try {
      // 获取 ArrayBuffer
      const arrayBuffer = await blob.arrayBuffer();

      // 创建音频上下文来分析音频，使用16kHz采样率
      const audioCtx = new (window.AudioContext || window.webkitAudioContext)({
        sampleRate: 16000,
      });

      // 解码音频数据
      const audioBuffer = await audioCtx.decodeAudioData(arrayBuffer.slice());

      // 获取音频信息
      const audioInfo = {
        // 基本信息
        size: blob.size,
        type: blob.type,

        // 音频参数 - 使用我们设定的采样率而不是解码后的采样率
        sampleRate: 16000, // 直接使用我们设定的采样率
        numberOfChannels: audioBuffer.numberOfChannels, // 声道数
        length: audioBuffer.length, // 采样点数量
        duration: audioBuffer.duration, // 时长（秒）

        // 计算的信息
        bitDepth: 16, // WebM/Opus 通常是 32-bit float，但实际编码可能不同
        bitRate: Math.round((blob.size * 8) / audioBuffer.duration), // 比特率

        // ArrayBuffer 数据
        arrayBuffer: arrayBuffer,

        // 原始音频数据（浮点数组）
        rawAudioData: {
          left:
            audioBuffer.numberOfChannels > 0
              ? audioBuffer.getChannelData(0)
              : null,
          right:
            audioBuffer.numberOfChannels > 1
              ? audioBuffer.getChannelData(1)
              : null,
        },
      };

      // 关闭音频上下文
      audioCtx.close();
      return audioInfo;
    } catch (error) {
      console.error('解析音频信息失败:', error);

      // 如果解析失败，返回基本信息，使用我们设定的采样率
      return {
        size: blob.size,
        type: blob.type,
        sampleRate: 16000, // 确保默认值也是16000
        numberOfChannels: 1, // 默认单声道
        arrayBuffer: await blob.arrayBuffer(),
        error: error.message,
      };
    }
  }, []);

  // 内部重新启动录音的函数
  const restartRecording = useCallback(async () => {
    if (streamRef.current && recorderRef.current) {
      console.log('=======Recording=======重新启动录音');

      // 停止当前录音器
      if (recorderRef.current.state === 'recording') {
        recorderRef.current.stop();
      }

      // 创建新的 MediaRecorder 实例
      const newRecorder = new MediaRecorder(
        streamRef.current,
        getCallRecorderOptions(),
      );

      // 将新的录音器实例保存到引用中
      recorderRef.current = newRecorder;

      // 重新绑定事件监听器
      newRecorder.ondataavailable = async e => {
        console.log(
          'ondataavailable=======Recording=======',
          e,
          isStoppingRef.current,
        );
        if (e.data.size > 0) {
          recordingChunksRef.current.push(e.data);
          setRecordings(prev => [...prev, e.data]);

          if (onChunkReady && typeof onChunkReady === 'function') {
            console.log(
              'onChunkReady=======Recording=======',
              e,
              isStoppingRef.current,
            );
            try {
              const audioInfo = await parseAudioInfo(e.data);
              onChunkReady(e.data, audioInfo, isStoppingRef.current);
            } catch (error) {
              console.error('处理录音片段失败:', error);
            }
          }
        }
      };

      newRecorder.onstop = () => {
        console.log('=======Recording=======录音片段已停止');
      };

      // 开始新的录音
      newRecorder.start();
    }
  }, [parseAudioInfo, onChunkReady]);

  // 单声道开始录音的异步函数
  const startRecording = useCallback(async () => {
    try {
      // 清空之前的录音数据
      setRecordings([]);
      recordingChunksRef.current = [];
      // 重置停止标志
      isStoppingRef.current = false;

      // 请求用户授权并获取麦克风音频流
      const stream = await navigator.mediaDevices.getUserMedia(
        getCallRecordingConstraints(),
      );

      // 将音频流保存到引用中，便于后续操作
      streamRef.current = stream;
      console.log('stream=======Recording=======', stream);

      // 获取音频轨道信息
      const audioTrack = stream.getAudioTracks()[0];
      const settings = audioTrack.getSettings();
      console.log('音频轨道设置:', settings);

      // 创建 MediaRecorder 实例，指定音频流和 MIME 类型
      const recorder = new MediaRecorder(stream, getCallRecorderOptions());

      // 将录音器实例保存到引用中
      recorderRef.current = recorder;
      console.log('recorder=======Recording=======', recorder);

      // 监听数据可用事件：当有录音数据时触发
      recorder.ondataavailable = async e => {
        console.log(
          'ondataavailable=======Recording=======',
          e,
          isStoppingRef.current,
        );
        // 检查数据是否有效（大小大于0）
        if (e.data.size > 0) {
          // 同时更新状态和ref，确保数据同步
          recordingChunksRef.current.push(e.data);
          setRecordings(prev => [...prev, e.data]);

          // 如果有回调函数，处理单个录音片段并立即上传
          if (onChunkReady && typeof onChunkReady === 'function') {
            console.log(
              'onChunkReady=======Recording=======',
              e,
              isStoppingRef.current,
            );
            try {
              // 解析单个录音片段的音频信息
              const audioInfo = await parseAudioInfo(e.data);
              // 调用回调函数，传递录音片段信息
              onChunkReady(e.data, audioInfo, isStoppingRef.current);
            } catch (error) {
              console.error('处理录音片段失败:', error);
            }
          }
        }
      };

      // 监听录音停止事件
      recorder.onstop = () => {
        console.log('=======Recording=======录音片段已停止，准备重新开始');
        // 如果不是最终停止，则重新启动录音
        if (!isStoppingRef.current) {
          setTimeout(() => {
            restartRecording();
          }, 100); // 短暂延迟后重新启动
        }
      };

      // 开始录音
      recorder.start();

      // 设置定时器：每60秒暂停当前录音并重新开始
      intervalRef.current = setInterval(() => {
        // 检查录音器是否仍在录制状态
        if (recorderRef.current && recorderRef.current.state === 'recording') {
          console.log(
            '=======Recording=======定时器触发，准备暂停并重新开始录音',
          );
          // 直接停止录音器，MediaRecorder 会自动触发 ondataavailable
          recorderRef.current.stop();
        }
      }, 60000); // 60000ms = 60秒

      // 更新录制状态为 true
      setRecording(true);
    } catch (err) {
      // 捕获错误（通常是用户拒绝麦克风权限）
      console.log('startRecording=======', err);
      // 提示用户授权麦克风
      notification.error({
        message: getIntl().formatMessage({
          id: 'im.twilio.components.device.not.supported.message',
        }),
      });
    }
  }, [parseAudioInfo, onChunkReady, restartRecording]);

  // 开始双声道录音的异步函数（使用原生实现）
  const startStereoRecording = useCallback(
    async (localStream, remoteStream) => {
      try {
        console.log(
          '===stereo===开始原生双声道录音',
          localStream,
          remoteStream,
        );

        // 清空之前的录音数据
        setRecordings([]);
        recordingChunksRef.current = [];
        isStoppingRef.current = false;

        // 使用原生双声道实现（忽略传入的Twilio参数）
        const audioStreamData = await createNativeStereoStream(
          localStream,
          remoteStream,
        );
        console.log('===stereo===使用原生双声道流', audioStreamData);

        // 保存音频流和上下文
        streamRef.current = audioStreamData.stream;

        // 创建主录音器 MediaRecorder 实例
        const recorder = new MediaRecorder(audioStreamData.stream, {
          ...getCallRecorderOptions(),
          // 双声道适配比特率
          audioBitsPerSecond: 128000,
        });

        recorderRef.current = recorder;
        console.log(
          '===stereo===MediaRecorder创建成功, 原生双声道模式',
          recorder,
        );

        // 监听主录音器数据可用事件
        recorder.ondataavailable = async e => {
          console.log('===stereo===ondataavailable', e.data.size, 'bytes');
          if (e.data.size > 0) {
            recordingChunksRef.current.push(e.data);
            setRecordings(prev => [...prev, e.data]);

            if (onChunkReady && typeof onChunkReady === 'function') {
              try {
                const audioInfo = await parseAudioInfo(e.data);
                // 标记录音信息
                audioInfo.isStereo = true;
                audioInfo.leftChannel = '左声道输入';
                audioInfo.rightChannel = '右声道输入';
                audioInfo.nativeMode = true; // 标记为原生模式

                onChunkReady(e.data, audioInfo, isStoppingRef.current);
              } catch (error) {
                console.error('===stereo===处理录音片段失败:', error);
              }
            }
          }
        };

        // 监听主录音器停止事件
        recorder.onstop = () => {
          console.log('===stereo===录音片段已停止');

          // 清理音频上下文
          if (audioStreamData.audioContext) {
            audioStreamData.audioContext.close();
          }

          // 如果不是最终停止，则重新启动录音
          if (!isStoppingRef.current) {
            setTimeout(async () => {
              try {
                // 重新启动原生双声道录音
                await startStereoRecording();
              } catch (error) {
                console.error('===stereo===重新启动双声道录音失败:', error);
              }
            }, 100);
          }
        };

        // 开始所有录音器
        recorder.start();

        // 设置定时器：每60秒停止并重新开始
        intervalRef.current = setInterval(() => {
          if (
            recorderRef.current &&
            recorderRef.current.state === 'recording'
          ) {
            console.log('===stereo===定时器触发，准备重新开始双声道录音');
            recorderRef.current.stop();
          }
        }, 60000);

        setRecording(true);
        console.log('===stereo===原生双声道录音启动完成');

        // 返回控制接口，允许外部调整音量等
        return audioStreamData.controls;
      } catch (error) {
        console.error('===stereo===启动双声道录音失败:', error);

        // 降级到单声道录音
        try {
          console.log('===stereo===降级到单声道录音');
          await startRecording();
          notification.warning({
            message: '双声道录音失败，已降级为单声道录音',
          });
        } catch (fallbackError) {
          console.error('===stereo===单声道录音也失败:', fallbackError);
          notification.error({
            message: '录音功能启动失败: ' + error.message,
          });
        }
      }
    },
    [
      createNativeStereoStream,
      getCallRecorderOptions,
      parseAudioInfo,
      onChunkReady,
      startRecording,
    ],
  );

  // 停止录音的函数
  const stopRecording = useCallback(() => {
    // 设置停止标志，防止重新启动录音
    isStoppingRef.current = true;
    return new Promise(async resolve => {
      console.log('=======Recording=======开始停止录音流程');

      // 清除定时器，停止定时请求录音数据
      clearInterval(intervalRef.current);

      // 检查录音器是否存在且正在录制
      if (recorderRef.current && recorderRef.current.state === 'recording') {
        // 监听录音停止事件，在停止后处理数据
        recorderRef.current.onstop = async () => {
          console.log('=======Recording=======最终录音停止，开始合并数据');

          // 检查音频流是否存在
          if (streamRef.current) {
            // 停止所有音频轨道，释放麦克风资源
            streamRef.current.getTracks().forEach(t => t.stop());
            streamRef.current = null;
          }

          // 清理音频上下文
          if (audioContextRef.current) {
            audioContextRef.current.close();
            audioContextRef.current = null;
          }

          // 使用ref中的数据，避免状态更新延迟问题
          const mergedBlob = mergeRecordingChunks(recordingChunksRef.current);

          if (mergedBlob) {
            // 解析音频信息
            const audioInfo = await parseAudioInfo(mergedBlob);
            console.log('=======Recording=======最终音频信息:', audioInfo);

            // 返回包含音频信息的对象
            resolve({
              blob: mergedBlob,
              audioInfo: audioInfo,
            });
          } else {
            resolve(null);
          }

          // 更新录制状态为 false
          setRecording(false);

          // 重置停止标志
          isStoppingRef.current = true;

          // 清空录音器引用
          recorderRef.current = null;
        };

        // 直接停止录音器，MediaRecorder 会自动触发最后的 ondataavailable
        recorderRef.current.stop();
      } else {
        // 如果没有录音器或已经停止，直接合并现有数据
        console.log('=======Recording=======录音器已停止，直接合并数据');

        // 检查音频流是否存在
        if (streamRef.current) {
          // 停止所有音频轨道，释放麦克风资源
          streamRef.current.getTracks().forEach(t => t.stop());
          streamRef.current = null;
        }

        // 清理音频上下文
        if (audioContextRef.current) {
          audioContextRef.current.close();
          audioContextRef.current = null;
        }

        const mergedBlob = mergeRecordingChunks(recordingChunksRef.current);

        if (mergedBlob) {
          // 解析音频信息
          const audioInfo = await parseAudioInfo(mergedBlob);

          resolve({
            blob: mergedBlob,
            audioInfo: audioInfo,
          });
        } else {
          resolve(null);
        }

        // 更新录制状态为 false
        setRecording(false);

        // 重置停止标志
        isStoppingRef.current = true;
      }
    });
  }, [parseAudioInfo]);

  // 合并录音片段的函数（使用传入的数据而不是状态）
  const mergeRecordingChunks = chunks => {
    console.log('=======Recording=======合并录音片段，数量:', chunks.length);

    // 检查是否有录音数据
    if (chunks.length === 0) {
      notification.error({
        message: getIntl().formatMessage({
          id: 'im.twilio.components.device.not.supported.message.no.record',
        }),
      });
      return null;
    }

    // 将所有录音片段合并成一个 Blob 对象
    const mergedBlob = new Blob(chunks, { type: mimeType });

    // 创建新的URL
    const newUrl = URL.createObjectURL(mergedBlob);
    setMergedUrl(mergedBlob);
    console.log(
      '=======Recording=======合并完成，Blob大小:',
      mergedBlob.size,
      mergedBlob,
      'bytes',
      newUrl,
    );
    return mergedBlob;
  };

  // 组件卸载时的清理副作用
  useEffect(() => {
    // 返回清理函数
    return () => {
      // 清理定时器
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }

      // 停止录音器
      if (recorderRef.current && recorderRef.current.state === 'recording') {
        recorderRef.current.stop();
      }

      // 停止音频流
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(t => t.stop());
      }

      // 清理音频上下文
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }

      // 如果存在音频 URL，释放 URL 对象以防止内存泄漏
      if (mergedUrl) {
        URL.revokeObjectURL(mergedUrl);
      }
    };
  }, [mergedUrl]); // 依赖项：当 mergedUrl 改变时重新执行

  /**
   * 将音频数据转换为 WAV 格式
   * @param {Object} audioInfo
   * @returns {Blob}
   */
  const convertToWav = audioInfo => {
    const { rawAudioData, sampleRate, numberOfChannels } = audioInfo;

    if (!rawAudioData.left) return null;

    const length = rawAudioData.left.length;
    const buffer = new ArrayBuffer(44 + length * numberOfChannels * 2);
    const view = new DataView(buffer);

    // WAV 文件头
    const writeString = (offset, string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };

    writeString(0, 'RIFF');
    view.setUint32(4, 36 + length * numberOfChannels * 2, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, numberOfChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * numberOfChannels * 2, true);
    view.setUint16(32, numberOfChannels * 2, true);
    view.setUint16(34, 16, true);
    writeString(36, 'data');
    view.setUint32(40, length * numberOfChannels * 2, true);

    // 写入音频数据
    let offset = 44;
    for (let i = 0; i < length; i++) {
      // 左声道
      view.setInt16(offset, rawAudioData.left[i] * 0x7fff, true);
      offset += 2;

      // 右声道（如果存在）
      if (numberOfChannels > 1 && rawAudioData.right) {
        view.setInt16(offset, rawAudioData.right[i] * 0x7fff, true);
        offset += 2;
      }
    }

    return new Blob([buffer], { type: 'audio/wav' });
  };

  // 封装hooks，返回录音相关的方法和状态
  return {
    recording, //暴露是否在录音
    recordings, //暴露录音片段，数组类型，需要整合
    startRecording, //暴露开始录音函数
    startStereoRecording, //暴露开始双声道录音函数
    stopRecording, //暴露停止录音函数，promise返回最终数据，固定blob
    parseAudioInfo, // 暴露解析音频信息，接收blob，返回音频信息
    convertToWav,
    createNativeStereoStream, // 暴露创建原生双声道流函数
  };
};

// 导出组件
export default useMediaRecorder;
